# Ad Account Account Controls

On This Page

[Ad Account Account Controls](#overview)

[Reading](#Reading)

[Example](#example)

[Parameters](#parameters)

[Fields](#fields)

[Error Codes](#error-codes)

[Creating](#Creating)

[Parameters](#parameters-2)

[Return Type](#return-type)

[Error Codes](#error-codes-2)

[Updating](#Updating)

[Deleting](#Deleting)

Graph API Version

[v23.0](#)

# Ad Account Account Controls

[](#)

## Reading

Get default fields on an [AdAccountBusinessConstraints](/docs/marketing-api/reference/ad-account-business-constraints/) node associated with this [AdAccount](/docs/marketing-api/reference/ad-account). Refer to the [AdAccountBusinessConstraints](/docs/marketing-api/reference/ad-account-business-constraints/) reference for a list of these fields and their descriptions.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Faccount_controls&version=v23.0)

```
`GET /v23.0/{ad-account-id}/account_controls HTTP/1.1
Host: graph.facebook.com`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->get(
    '/{ad-account-id}/account_controls',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/{ad-account-id}/account_controls",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/account_controls",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/account_controls"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of [AdAccountBusinessConstraints](/docs/marketing-api/reference/ad-account-business-constraints/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

190

Invalid OAuth 2.0 Access Token

[](#)

## Creating

You can make a POST request to `account_controls` edge from the following paths:

*   [`/act_{ad_account_id}/account_controls`](/docs/marketing-api/reference/ad-account/account_controls/)

When posting to this edge, an [AdAccountBusinessConstraints](/docs/marketing-api/reference/ad-account-business-constraints/) will be created.

### Parameters

Parameter

Description

`audience_controls`

JSON or object-like arrays

audience\_controls

Required

`age_min`

int64

`geo_locations`

JSON or object-like arrays

`excluded_geo_locations`

JSON or object-like arrays

`exclusions`

JSON or object-like arrays

`placement_controls`

JSON or object-like arrays

This field contains another field called placement\_exclusion that provides information on which placements need to be excluded while targeting. All the other placements will be included. Each placement is denoted by a string that concatenates the publisher platform of the placement and a position inside the publisher platform, separated by an underscore. What is provided as parameter is a list of placements. For e.g. If we want to exclude the rewarded videos position from the audience network publisher platform, we provide the field as follows: { "placement\_controls": { "placement\_exclusions": \["audience\_network\_rewarded\_video"\] } } Only a few placements are allowed to be excluded: audience\_network\_classic (native, banner & interstitial positions of audience network) audience\_network\_rewarded\_video (rewarded videos of audience network) audience\_network\_instream\_video (instream videos of audience network) facebook\_marketplace (marketplace section inside facebook) facebook\_rhc (right hand column inside facebook)

`placement_exclusions`

array<enum {AUDIENCE\_NETWORK\_CLASSIC, AUDIENCE\_NETWORK\_REWARDED\_VIDEO, AUDIENCE\_NETWORK\_INSTREAM\_VIDEO, FACEBOOK\_MARKETPLACE, FACEBOOK\_RIGHT\_HAND\_COLUMN}>

`campaign_ids_to_set_ap`

array<numeric string>

### Return Type

Struct {

`id`: string,

`success`: bool,

`error_code`: string,

`error_message`: string,

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

2641

Your ad includes or excludes locations that are currently restricted

[](#)

## Updating

Use the [`POST /act_<AD_ACCOUNT_ID>/account_controls`](#Creating) endpoint to update the [AdAccountBusinessConstraints](/docs/marketing-api/reference/ad-account-business-constraints/) associated with this [AdAccount](/docs/marketing-api/reference/ad-account).

You can't perform this operation on this endpoint.

[](#)

## Deleting

You can't perform this operation on this endpoint.

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '***************'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)