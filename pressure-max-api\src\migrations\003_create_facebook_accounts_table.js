/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('facebook_accounts', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.uuid('tenant_id').notNullable().references('id').inTable('tenants').onDelete('CASCADE');
    table.string('facebook_user_id').notNullable();
    table.string('access_token').notNullable();
    table.timestamp('token_expires_at').nullable();
    table.string('refresh_token').nullable();
    table.json('permissions').defaultTo('[]');
    table.json('ad_accounts').defaultTo('[]');
    table.json('pages').defaultTo('[]');
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_sync_at').nullable();
    table.timestamps(true, true);

    // Indexes
    table.index('user_id');
    table.index('tenant_id');
    table.index('facebook_user_id');
    table.index('is_active');
    
    // Unique constraint
    table.unique(['user_id', 'facebook_user_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('facebook_accounts');
};
