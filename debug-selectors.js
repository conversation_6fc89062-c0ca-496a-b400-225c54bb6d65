const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('https://developers.facebook.com/docs/marketing-apis');
  await page.waitForLoadState('networkidle');
  
  console.log('Page loaded, checking selectors...');
  
  // Check if the specific selector exists
  const specificSelector = await page.locator('._1jbo._4are._1jcw._1jd4._5zlc').count();
  console.log('Specific selector count:', specificSelector);
  
  // Check for alternative selectors
  const alternatives = [
    '._1jbo',
    '._4are', 
    '._1jcw',
    '._1jd4',
    '._5zlc',
    '[data-click-area="main"]',
    '.documentation-content',
    'main',
    '[role="main"]',
    '._4-u2',
    '._4-u3',
    '._588p'
  ];
  
  for (const sel of alternatives) {
    const count = await page.locator(sel).count();
    console.log(`Selector '${sel}': ${count} elements`);
    
    if (count > 0) {
      try {
        const text = await page.locator(sel).first().textContent();
        console.log(`  First element text preview: ${text.substring(0, 100)}...`);
      } catch (e) {
        console.log(`  Could not get text content`);
      }
    }
  }
  
  // Check the actual content structure
  console.log('\n--- Page structure analysis ---');
  const bodyHTML = await page.locator('body').innerHTML();
  console.log('Body HTML length:', bodyHTML.length);
  
  // Look for main content containers
  const contentContainers = await page.$$eval('div', divs => {
    return divs
      .filter(div => {
        const text = div.textContent || '';
        return text.includes('Marketing API') && text.length > 100;
      })
      .map(div => ({
        className: div.className,
        id: div.id,
        textLength: (div.textContent || '').length,
        textPreview: (div.textContent || '').substring(0, 150)
      }))
      .slice(0, 5); // Top 5 candidates
  });
  
  console.log('\nContent container candidates:');
  contentContainers.forEach((container, i) => {
    console.log(`${i + 1}. Class: "${container.className}", ID: "${container.id}"`);
    console.log(`   Text length: ${container.textLength}`);
    console.log(`   Preview: ${container.textPreview}...`);
    console.log('');
  });
  
  await browser.close();
})();
