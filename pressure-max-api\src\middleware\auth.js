const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const config = require('../config/config');
const logger = require('../config/logger');
const redis = require('../config/redis');
const User = require('../models/User');

class AuthMiddleware {
  // Generate JWT token
  generateToken(payload) {
    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn
    });
  }

  // Generate refresh token
  generateRefreshToken(payload) {
    return jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn
    });
  }

  // Verify JWT token
  verifyToken(token) {
    try {
      return jwt.verify(token, config.jwt.secret);
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  // Verify refresh token
  verifyRefreshToken(token) {
    try {
      return jwt.verify(token, config.jwt.refreshSecret);
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  // Hash password
  async hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  // Compare password
  async comparePassword(password, hashedPassword) {
    return bcrypt.compare(password, hashedPassword);
  }

  // Authentication middleware
  authenticate = async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please provide a valid Bearer token'
        });
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      // Check if token is blacklisted
      const isBlacklisted = await redis.exists(`blacklist:${token}`);
      if (isBlacklisted) {
        return res.status(401).json({
          error: 'Token revoked',
          message: 'This token has been revoked'
        });
      }

      // Verify token
      const decoded = this.verifyToken(token);
      
      // Get user from database
      const user = await User.findById(decoded.userId);
      if (!user) {
        return res.status(401).json({
          error: 'User not found',
          message: 'The user associated with this token no longer exists'
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({
          error: 'Account deactivated',
          message: 'Your account has been deactivated'
        });
      }

      // Attach user info to request
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId,
        permissions: user.permissions || []
      };

      // Log successful authentication
      logger.audit('user_authenticated', user.id, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      next();
    } catch (error) {
      logger.error('Authentication error:', error);
      
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Token expired',
          message: 'Your session has expired. Please log in again.'
        });
      }

      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid or malformed token'
      });
    }
  };

  // Role-based authorization middleware
  authorize = (roles = []) => {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please authenticate first'
        });
      }

      // Convert single role to array
      const allowedRoles = Array.isArray(roles) ? roles : [roles];
      
      // Check if user has required role
      if (allowedRoles.length > 0 && !allowedRoles.includes(req.user.role)) {
        logger.audit('unauthorized_access_attempt', req.user.id, {
          requiredRoles: allowedRoles,
          userRole: req.user.role,
          endpoint: req.originalUrl
        });

        return res.status(403).json({
          error: 'Insufficient permissions',
          message: 'You do not have permission to access this resource'
        });
      }

      next();
    };
  };

  // Permission-based authorization middleware
  requirePermission = (permission) => {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please authenticate first'
        });
      }

      // Check if user has required permission
      if (!req.user.permissions.includes(permission)) {
        logger.audit('permission_denied', req.user.id, {
          requiredPermission: permission,
          userPermissions: req.user.permissions,
          endpoint: req.originalUrl
        });

        return res.status(403).json({
          error: 'Permission denied',
          message: `You need '${permission}' permission to access this resource`
        });
      }

      next();
    };
  };

  // Tenant isolation middleware
  requireTenant = (req, res, next) => {
    if (!req.user || !req.user.tenantId) {
      return res.status(403).json({
        error: 'Tenant required',
        message: 'This operation requires a valid tenant context'
      });
    }

    // Add tenant filter to query context
    req.tenantId = req.user.tenantId;
    next();
  };

  // Rate limiting per user
  rateLimitPerUser = (maxRequests = 100, windowMs = 900000) => {
    return async (req, res, next) => {
      if (!req.user) {
        return next();
      }

      const key = `rate_limit:user:${req.user.id}`;
      const current = await redis.incrementRateLimit(key, Math.floor(windowMs / 1000));

      if (current > maxRequests) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests,
        'X-RateLimit-Remaining': Math.max(0, maxRequests - current),
        'X-RateLimit-Reset': new Date(Date.now() + windowMs).toISOString()
      });

      next();
    };
  };

  // Blacklist token (for logout)
  async blacklistToken(token) {
    try {
      const decoded = this.verifyToken(token);
      const expiresIn = decoded.exp - Math.floor(Date.now() / 1000);
      
      if (expiresIn > 0) {
        await redis.set(`blacklist:${token}`, true, expiresIn);
      }
      
      return true;
    } catch (error) {
      logger.error('Error blacklisting token:', error);
      return false;
    }
  }

  // Optional authentication (for public endpoints that can benefit from user context)
  optionalAuth = async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = this.verifyToken(token);
        const user = await User.findById(decoded.userId);
        
        if (user && user.isActive) {
          req.user = {
            id: user.id,
            email: user.email,
            role: user.role,
            tenantId: user.tenantId,
            permissions: user.permissions || []
          };
        }
      }
    } catch (error) {
      // Silently fail for optional auth
      logger.debug('Optional auth failed:', error.message);
    }
    
    next();
  };
}

module.exports = new AuthMiddleware();
