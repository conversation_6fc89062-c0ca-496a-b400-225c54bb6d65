const { chromium } = require('playwright');

class ImprovedContentExtractor {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  /**
   * Initialize the browser
   */
  async initialize() {
    this.browser = await chromium.launch({ headless: true });
    this.page = await this.browser.newPage();
    this.page.setDefaultTimeout(30000);
  }

  /**
   * Clean up browser resources
   */
  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  /**
   * Extract clean content from a URL using improved selectors
   */
  async extractCleanContent(url) {
    try {
      await this.page.goto(url, { waitUntil: 'networkidle' });
      await this.page.waitForTimeout(1000); // Extra wait for dynamic content
      
      const extractedData = await this.page.evaluate(() => {
        // Enhanced content extraction logic
        const result = {
          title: '',
          breadcrumbs: [],
          content: '',
          navigationLinks: []
        };
        
        // Extract title
        const titleElement = document.querySelector('h1') || 
                           document.querySelector('.title') || 
                           document.querySelector('.page-title');
        result.title = titleElement ? titleElement.textContent.trim() : document.title;
        
        // Extract breadcrumbs
        const breadcrumbElements = document.querySelectorAll('.breadcrumb a, .breadcrumbs a, nav[aria-label="breadcrumb"] a');
        result.breadcrumbs = Array.from(breadcrumbElements).map(el => ({
          text: el.textContent.trim(),
          href: el.getAttribute('href')
        }));
        
        // Enhanced main content extraction
        let mainContent = '';
        
        // Strategy 1: Try to get documentation sections specifically
        const docSections = document.querySelectorAll('._4-u2 ._4-u3');
        if (docSections.length > 0) {
          console.log('Using documentation sections strategy');
          
          const cleanedSections = Array.from(docSections).map(section => {
            const clone = section.cloneNode(true);
            
            // Remove unwanted elements more aggressively
            const unwantedSelectors = [
              'script',
              'style', 
              'noscript',
              '.hidden_elem',
              '[data-click-area="to_top_nav"]',
              '._2k32', // Back to top buttons
              'fb\\:like',
              '.img[data-visualcompletion="css-img"]',
              '._1dyy', // Table of contents
              '._33zv', // On this page elements
              'nav',
              '.navigation',
              '[role="navigation"]',
              '.breadcrumb',
              '.breadcrumbs'
            ];
            
            unwantedSelectors.forEach(selector => {
              try {
                const elements = clone.querySelectorAll(selector);
                elements.forEach(el => el.remove());
              } catch (e) {
                // Ignore selector errors
              }
            });
            
            // Clean up empty elements
            const emptyElements = clone.querySelectorAll('div:empty, span:empty, p:empty');
            emptyElements.forEach(el => el.remove());
            
            return clone.innerHTML;
          });
          
          mainContent = cleanedSections.join('\n\n');
        }
        
        // Strategy 2: Fallback to main content area
        if (!mainContent || mainContent.length < 500) {
          console.log('Using main content area fallback');
          
          const mainSelectors = [
            '[data-click-area="main"]',
            'main',
            '[role="main"]',
            '.content',
            '#content',
            '.documentation-content'
          ];
          
          for (const selector of mainSelectors) {
            const element = document.querySelector(selector);
            if (element) {
              const clone = element.cloneNode(true);
              
              // Remove navigation and unwanted elements
              const unwantedSelectors = [
                'script',
                'style',
                'noscript',
                '.hidden_elem',
                '[data-click-area="to_top_nav"]',
                '._2k32',
                'fb\\:like',
                '.img[data-visualcompletion="css-img"]',
                '._1dyy',
                '._33zv',
                '._3wm1', // Navigation sidebar
                'nav:not([aria-label="breadcrumb"])',
                '.navigation',
                '[role="navigation"]:not([aria-label="breadcrumb"])'
              ];
              
              unwantedSelectors.forEach(unwantedSelector => {
                try {
                  const elements = clone.querySelectorAll(unwantedSelector);
                  elements.forEach(el => el.remove());
                } catch (e) {
                  // Ignore selector errors
                }
              });
              
              mainContent = clone.innerHTML;
              break;
            }
          }
        }
        
        // Strategy 3: Last resort - get body and clean aggressively
        if (!mainContent || mainContent.length < 200) {
          console.log('Using body cleanup fallback');
          
          const bodyClone = document.body.cloneNode(true);
          
          // Remove major unwanted sections
          const majorUnwantedSelectors = [
            'header',
            'footer',
            'nav',
            '.navigation',
            '[role="navigation"]',
            '.sidebar',
            '._3wm1',
            '._1dyy',
            'script',
            'style',
            'noscript'
          ];
          
          majorUnwantedSelectors.forEach(selector => {
            try {
              const elements = bodyClone.querySelectorAll(selector);
              elements.forEach(el => el.remove());
            } catch (e) {
              // Ignore selector errors
            }
          });
          
          mainContent = bodyClone.innerHTML;
        }
        
        result.content = mainContent;
        
        // Extract navigation links for URL discovery
        const navLinks = document.querySelectorAll('a[href*="/docs/marketing-api"]');
        result.navigationLinks = Array.from(navLinks)
          .map(link => link.getAttribute('href'))
          .filter(href => href && href.startsWith('/docs/marketing-api'))
          .filter((href, index, array) => array.indexOf(href) === index); // Remove duplicates
        
        return result;
      });
      
      // Additional validation and cleanup
      if (!extractedData.content || extractedData.content.length < 100) {
        throw new Error('Extracted content is too short or empty');
      }
      
      // Add metadata
      extractedData.url = this.page.url();
      extractedData.timestamp = new Date().toISOString();
      extractedData.extractionMethod = 'improved-extractor';
      
      return extractedData;
      
    } catch (error) {
      console.error(`Error extracting content from ${url}:`, error.message);
      throw error;
    }
  }

  /**
   * Batch extract content from multiple URLs
   */
  async extractMultipleUrls(urls, delay = 2000) {
    const results = [];
    
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      console.log(`🔄 Extracting content from ${url} (${i + 1}/${urls.length})`);
      
      try {
        const content = await this.extractCleanContent(url);
        results.push({ url, success: true, content });
        console.log(`✅ Successfully extracted content from ${url}`);
      } catch (error) {
        console.error(`❌ Failed to extract content from ${url}:`, error.message);
        results.push({ url, success: false, error: error.message });
      }
      
      // Add delay between requests
      if (i < urls.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    return results;
  }

  /**
   * Compare content quality between old and new extraction
   */
  compareContentQuality(oldContent, newContent) {
    const oldText = oldContent.replace(/<[^>]*>/g, '').trim();
    const newText = newContent.replace(/<[^>]*>/g, '').trim();
    
    const oldScriptCount = (oldContent.match(/<script/g) || []).length;
    const newScriptCount = (newContent.match(/<script/g) || []).length;
    
    const oldNavCount = (oldContent.match(/class="_3wm1"|class="_1dyy"/g) || []).length;
    const newNavCount = (newContent.match(/class="_3wm1"|class="_1dyy"/g) || []).length;
    
    return {
      textLengthImprovement: newText.length - oldText.length,
      scriptTagsRemoved: oldScriptCount - newScriptCount,
      navigationElementsRemoved: oldNavCount - newNavCount,
      qualityImproved: newText.length > oldText.length && newScriptCount < oldScriptCount
    };
  }
}

module.exports = ImprovedContentExtractor;
