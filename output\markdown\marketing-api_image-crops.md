# Image Crops

# Image Crops

Provide aspect ratios for images in different ad placements. Facebook crops your image according to your specifications given or if you provide no cropping we display it using defaults. See [Ad Image](/docs/reference/ads-api/adimage/). For example upload an image to use in ad creative:

```
curl \\
  \-F 'filename=@<IMAGE\_PATH>' \\
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/adimages
    
```

Then, provide ad creative by referencing the image hash returned in the previous call along with cropping.

Crops contains key-value pairs, where the key is a `crop key` and value is the pixel dimensions of the crop. For all supported keys, see [Ads Image Crops Reference](/docs/marketing-api/reference/ads-image-crops).

Provide value as `(x, y)` coordinates for the upper-left and bottom-right corners of the cropping rectangle. `crop key` describes an aspect ratio. The aspect ratio of the box specified by width and height must be as close as possible to the aspect ratio in `crop key`.

An image's origin `(0, 0)` is at the upper-left corner. The point, `(width - 1, height - 1)` is at the bottom-right corner.

## Specification

When you use this feature, **you should use it for all placements where an ad may appear**. For example, if you provide it for the Right Hand Column, and you also want to use the ad in Newsfeed, you'll need to provide cropping for the Newsfeed placement.

## Limitations

Image crops are only supported for ad creatives with `image_file` or `image_hash`. `Page posts` are not supported. Values must adhere to these constraints:

*   Points specified by `(x, y)` must lie within the image. A rectangle that extends beyond the bounds of the image is invalid.
*   The rectangle must be the same aspect ratio as specified by the crop key.
*   Coordinates cannot contain negative values.
*   Facebook Stories do not support image crops.

For example:

```
Example:{"100x100": \[ \[330, 67\], \[1080, 817\] \]}
```