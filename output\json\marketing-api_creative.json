{"title": "Ad Creative", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_5_Zw\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad-creative\">Ad Creative</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#creative\">Creative</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#limits\">Limits</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#read\">Read</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#placements\">Placements</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#previews\">Preview an Ad</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#see-more\">See More</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_0_VU\"><div class=\"_4cel\"><span data-click-area=\"main\"><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8\"><div class=\"_4-u3 _588p\"><h1 id=\"ad-creative\">Ad Creative</h1>\n\n<p>Use Facebook ads with your existing customers and to reach new ones. Each guide describes Facebook ads products to help meet your advertising goals. There are several types of ad units with a variety of appearances, placement and creative options. For guidelines on ads units as creative content, see <a href=\"https://www.facebook.com/business/ads-guide/?tab0=Mobile%20News%20Feed\">Facebook Ads Guide</a>.</p>\n</div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"creative\">Creative</h2>\n\n<p>An ad creative is an object that contains all the data for visually rendering the ad itself. In the API, there are different types of ads that you can create on Facebook, all listed <a href=\"/docs/reference/ads-api/adcreative#overview\">here</a>.</p>\n\n<p>If you have a <a href=\"/docs/marketing-api/reference/ad-campaign-group\">campaign</a> with the Page Post Engagement Objective, you can now create an ad that promotes a post made by the page. This is considered a Page post ad. Page post ads require a field called <code>object_story_id</code>, which is the <code>id</code> property of a Page post. Learn more about <a href=\"/docs/reference/ads-api/adcreative#create\">Ad Creative, Reference</a>.</p>\n\n<p>An ad creative has three parts:</p>\n\n<ul>\n<li>Ad <a href=\"#creatives\">creative</a> itself, defined by the visual attributes of the creative object</li>\n<li><a href=\"#placements\">Placement</a> that the ad runs on</li>\n<li><a href=\"#previews\">Preview</a> of the unit itself, per placement</li>\n</ul>\n\n<p>To create the ad creative object, make the following call:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"Sample Promoted Post\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_id=\"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span><span class=\"pln\">\n  \n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives?&amp;version=v23.0&amp;name=Sample+Promoted+Post&amp;object_story_id=%3CPAGE_ID%3E_%3CPOST_ID%3E\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><br><p>The response to the API call is the <code>id</code> of the creative object. Store this; you need it for the ad object:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"My Ad\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'adset_id=\"&lt;AD_SET_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'creative={\n       \"creative_id\": \"&lt;CREATIVE_ID&gt;\"\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=\"PAUSED\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/ads</span><span class=\"pln\">\n  \n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fads?&amp;version=v23.0&amp;name=My+Ad&amp;adset_id=%3CAD_SET_ID%3E&amp;creative=%7B%22creative_id%22%3A%22%3CCREATIVE_ID%3E%22%7D&amp;status=PAUSED\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><h3 id=\"limits\">Limits</h3>\n\n<p>There are limits on the creative's text, image size, image aspect ratio and other aspects of the creative. See the <a href=\"https://www.facebook.com/business/ads-guide\">Ads Guide</a>.</p>\n\n<h3 id=\"read\">Read</h3>\n\n<p>In the Ads API, each field you want to retrieve needs to be asked for explicitly, except for <code>id</code>. Each object's <a href=\"/docs/reference/ads-api/adcreative/#read\">Reference</a> has a section for reading back the object and lists what fields are readable. For the creative, it's the same fields as specified when creating the object, and <code>id</code>.</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=name,object_story_id'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\nhttps</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CREATIVE_ID&gt;</span><span class=\"pln\">\n \n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=%3CCREATIVE_ID%3E%2F?fields=name%2Cobject_story_id&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"placements\">Placements</h2>\n\n<p>A placement is where your ad is shown on Facebook, such as on Feed on desktop, Feed on a mobile device or on the right column. See  <a href=\"https://www.facebook.com/business/ads-guide/\">Ads Product Guide</a>.</p>\n\n<p>We encourage you to run ads across the full range of available placements. Facebook’s ad auction is designed to deliver ad impressions to the placement most likely to drive campaign results at the lowest possible cost.</p>\n\n<p>The easiest way to take advantage of this optimization is to leave this field blank. You can also select specific placements in an ad set’s target_spec.</p>\n\n<p>This example has a page post ad. The available placements are Mobile Feed, Desktop Feed and Right column of Facebook. In the API, see <a href=\"/docs/reference/ads-api/targeting-specs/#placement\">Placement Options</a>. If you choose <code>desktopfeed</code> and <code>rightcolumn</code> as the <code>page_type</code>, the ad runs on Desktop Feed and Right column placements. Any ad created below this ad set has only the desktop placement.</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Desktop Ad Set'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'campaign_id=&lt;CAMPAIGN_ID&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'daily_budget=10000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'targeting={ \n    \"geo_locations\": {\"countries\":[\"US\"]}, \n    \"publisher_platforms\": [\"facebook\",\"audience_network\"] \n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'optimization_goal=LINK_CLICKS'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'billing_event=IMPRESSIONS'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'bid_amount=1000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=PAUSED'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adsets</span><span class=\"pln\">\n  \n\n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets&amp;version=v23.0&amp;name=Desktop%20Ad%20Set&amp;campaign_id=%3CCAMPAIGN_ID%3E&amp;daily_budget=10000&amp;targeting=%7B%22geo_locations%22%3A%20%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%20%22publisher_platforms%22%3A%20%5B%22facebook%22%2C%22audience_network%22%5D%20%7D&amp;optimization_goal=LINK_CLICKS&amp;billing_event=IMPRESSIONS&amp;bid_amount=1000&amp;status=PAUSED\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"previews\">Preview an Ad</h2>\n\n<p>You preview an ad in one of two ways—with <a href=\"/docs/reference/ads-api/generatepreview/\">ad preview API</a> or the <a href=\"/docs/reference/ads-api/ad-preview-plugin\">ad preview plugin</a>.</p>\n\n<p>There are three ways to generate a preview with the API:</p>\n\n<ol>\n<li>By ad ID</li>\n<li>By ad creative ID</li>\n<li>By supplying a creative spec</li>\n</ol>\n\n<p>Following the <a href=\"/docs/reference/ads-api/generatepreview/#html\">reference</a> docs for the preview API, the minimum required API call is:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">--</span><span class=\"pln\">data</span><span class=\"pun\">-</span><span class=\"pln\">urlencode </span><span class=\"str\">'creative=\"&lt;CREATIVE_SPEC&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"&lt;AD_FORMAT&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/generatepreviews</span><span class=\"pln\">\n  \n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%3CCREATIVE_SPEC%3E%26ad_format=%3CAD_FORMAT%3E&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><br><p>The creative spec is an array of each field and value required to create the ad creative.</p>\n\n<p>Currently, our ad creative call looks like this:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"Sample Promoted Post\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_id=\"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span><span class=\"pln\">\n\n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives?&amp;version=v23.0&amp;name=Sample+Promoted+Post&amp;object_story_id=%3CPAGE_ID%3E_%3CPOST_ID%3E\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><br><p>Take <code>object_story_id</code> and use it in the preview API call:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'creative={\"object_story_id\":\"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;\"}'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=&lt;AD_FORMAT&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/generatepreviews</span><span class=\"pln\">\n\n\n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews%3Fcreative%3D%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7Dad_format%3D%3CAD_FORMAT%3E&amp;version=v20.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><p>The available values for <code>ad_format</code> differ a bit from <code>page_types</code>. But, in this scenario, Desktop Feed and Right column of Facebook are selected. This requires you to make two API calls to generate the previews for each placement:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\n\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'creative={\"object_story_id\":\"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;\"}'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=DESKTOP_FEED_STANDARD'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/generatepreviews</span><span class=\"pln\">\n  \n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7D%26ad_format=DESKTOP_FEED_STANDARD&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">\ncurl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'creative={\"object_story_id\":\"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;\"}'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=RIGHT_COLUMN_STANDARD'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/generatepreviews</span><span class=\"pln\">\n\n</span><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews%3Fcreative%3D%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7D,ad_format%3DRIGHT_COLUMN_STANDARD&amp;version=v20.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\"><span class=\"typ\">Open</span><span class=\"pln\"> </span><span class=\"typ\">In</span><span class=\"pln\"> </span><span class=\"typ\">Graph</span><span class=\"pln\"> API </span><span class=\"typ\">Explorer</span></a></pre><p>The response is an iFrame that's valid for 24 hrs.</p>\n<div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"see-more\">See More</h2>\n\n<ul>\n<li><a href=\"/docs/marketing-api/reference/ad-creative\">Ad Creative</a></li>\n<li><a href=\"/docs/app-ads\">Facebook App Ads</a></li>\n<li><a href=\"https://www.facebook.com/business/ads-guide\">Ads Guide</a></li>\n</ul>\n<div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div></span><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p _4_k\"><fb:like href=\"https://developers.facebook.com/docs/marketing-api/creative/\" layout=\"button_count\" share=\"1\"></fb:like><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div id=\"developer_documentation_toolbar\" data-referrer=\"developer_documentation_toolbar\" data-click-area=\"toolbar\"></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '217404712025032');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/creative", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/guides/videoads", "/docs/marketing-api/guides/instant-experiences", "/docs/marketing-api/guides/collection", "/docs/marketing-api/mobile-app-ads", "/docs/marketing-api/guides/lead-ads", "/docs/marketing-api/guides/instagramads", "/docs/marketing-api/ad-creative/threads-ads", "/docs/marketing-api/ad-creative/messaging-ads", "/docs/marketing-api/guides/event-ads", "/docs/marketing-api/advantage-campaigns", "/docs/marketing-api/advantage-shopping-campaigns", "/docs/marketing-api/shops-ads", "/docs/marketing-api/guides/branded-content", "/docs/marketing-api/ad-creative/asset-feed-spec", "/docs/marketing-api/advantage-catalog-ads", "/docs/marketing-api/creative/advantage-creative", "/docs/marketing-api/ad-creative/omnichannel-ads", "/docs/marketing-api/creative/generative-ai-features", "/docs/marketing-api/collaborative-ads", "/docs/marketing-api/creative/multi-advertiser-ads", "/docs/marketing-api/creative/reels-ads", "/docs/marketing-api/call-ads", "/docs/marketing-api/flexible-ad-format", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/ad-campaign-group", "/docs/marketing-api/reference/ad-creative"], "url": "https://developers.facebook.com/docs/marketing-api/creative", "timestamp": "2025-06-25T15:05:25.102Z", "reprocessedAt": "2025-06-25T16:16:41.788Z", "reprocessedWith": "claude-sonnet-4"}