# Facebook Marketing API - Business Role Request Reference

## Summary
Complete reference for the Business Role Request endpoint in Facebook Marketing API v23.0. Covers reading business user invitation requests, updating role assignments, and managing business membership invitations with detailed field descriptions and error handling.

## Key Points
- Business Role Request endpoint manages invitations for users to join businesses
- Supports reading, updating, and deleting business role requests but not creating new ones
- Provides detailed invitation status tracking including creation time, expiration, and current status
- Supports multiple business roles including admin, employee, developer, and various partner center roles
- Requires proper permissions and may need two-factor authentication for protected business assets

## API Endpoints
- `GET /v23.0/{business-role-request-id}`
- `POST /{business_role_request_id}`
- `DELETE /{business_role_request_id}`

## Parameters
- business-role-request-id
- role
- id
- email
- status
- created_by
- created_time
- expiration_time
- finance_role
- invited_user_type
- owner
- updated_by
- updated_time

## Content
# Business Role Request

## Overview

Represents a business user request in the Facebook Marketing API. This endpoint allows admins to view and manage requests for people to join as members of a business.

## Reading Business Role Requests

### Endpoint
```
GET /v23.0/{business-role-request-id}
```

### Parameters
This endpoint doesn't require any parameters.

### Response Fields

| Field | Type | Description | Default |
|-------|------|-------------|----------|
| `id` | numeric string | Business role invitation request ID | ✓ |
| `created_by` | BusinessUser\|SystemUser | User who sent the invitation to join this business | |
| `created_time` | datetime | Time when admin sent this request | |
| `email` | string | Email of user invited to join the business | ✓ |
| `expiration_time` | datetime | When the invitation expires | |
| `finance_role` | enum | Pre-assigned Finance role for the invitation | |
| `invited_user_type` | list<enum> | Type of user being invited | |
| `owner` | Business | The business the user is invited to join | |
| `role` | enum | Business role assigned to the invited user | ✓ |
| `status` | enum | Invitation status (accepted, declined, expired, etc.) | ✓ |
| `updated_by` | BusinessUser\|SystemUser | User who last updated the invitation | |
| `updated_time` | datetime | Time when invitation was last updated | |

## Updating Business Role Requests

### Endpoint
```
POST /{business_role_request_id}
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `role` | enum | Update invitation role. Options: FINANCE_EDITOR, FINANCE_ANALYST, ADS_RIGHTS_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER_CENTER_ADMIN, PARTNER_CENTER_ANALYST, PARTNER_CENTER_OPERATIONS, PARTNER_CENTER_MARKETING, PARTNER_CENTER_EDUCATION, MANAGE, DEFAULT, FINANCE_EDIT, FINANCE_VIEW |

### Return Type
```json
{
  "id": "numeric string"
}
```

## Deleting Business Role Requests

### Endpoint
```
DELETE /{business_role_request_id}
```

### Parameters
This endpoint doesn't require any parameters.

### Return Type
```json
{
  "success": "bool"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |
| 368 | Action deemed abusive or disallowed |
| 415 | Two factor authentication required |

## Notes

- Creating new business role requests is not supported through this endpoint
- The endpoint supports read-after-write functionality for updates
- Two-factor authentication may be required when accessing protected business assets

## Examples
GET /v23.0/{business-role-request-id} HTTP/1.1

POST request to update role parameter

DELETE request to remove business role request

---
**Tags:** Facebook Marketing API, Business Management, User Roles, Invitations, Graph API, Business Role Request, API Reference
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/business-role-request
**Processed:** 2025-06-25T15:44:41.972Z