# Getting Started with Facebook Marketing API

## Summary
This guide covers the essential prerequisites and setup steps for using the Facebook Marketing API, including ad account requirements, developer account setup, app creation, and authentication processes. It provides a comprehensive overview of what developers need to begin building with the Marketing API.

## Key Points
- An active ad account is required to use the Marketing API for campaign management and billing
- Meta Developer account registration is mandatory for API access
- App creation in the App Dashboard is necessary with proper app type selection
- Authorization and authentication setup is crucial for secure API access
- Ad account number can be found through Meta Ads Manager settings

## Parameters
- ad account number
- access tokens
- app permissions
- billing settings
- spending limits

## Content
# Getting Started with Facebook Marketing API

To effectively utilize the Marketing API, users must follow some key steps to set up their environment and gain access to the API's features. This section outlines the prerequisites necessary for getting started.

## Ad Account Requirements

To manage your ads through the Marketing API, you must have an active ad account. This account is crucial not only for running campaigns but also for managing billing settings and setting spending limits. An ad account allows you to track your advertising expenses, monitor performance, and optimize your campaigns effectively.

### Finding Your Ad Account Number

Locating your ad account number can be done through the Meta Ads Manager:

1. **Log into Facebook:** Start by logging into your Facebook account that is associated with your business.
2. **Access Ads Manager:** Ads Manager can be found in the drop-down menu in the upper right corner of your Facebook homepage or business page.
3. **Locate your ad account:** In Ads Manager, click on the ad account Settings from the menu on the bottom left of the screen.
4. **View ad account information:** In the Settings screen, you will find your ad account number listed along with other details such as your billing information and spending limits.

## Meta Developer Account

A Meta Developer account is required to access the Marketing API. This involves registering as a Meta Developer to gain access to development tools and resources.

## Create an App

You need to create an app in the App Dashboard. This involves selecting appropriate app types and understanding different use cases for your Marketing API integration.

## Authorization and Authentication

Proper authorization and authentication are essential for accessing the Marketing API:

- **Authorization:** Verify users and apps that will access the Marketing API and grant them appropriate permissions
- **Authentication:** Manage access tokens including getting, extending, and renewing them for continued API access

## Next Steps

Once setup is complete, you can proceed with:

1. Creating ad campaigns
2. Managing ad campaigns
3. Optimizing ad campaigns

---
**Tags:** Facebook Marketing API, Meta Developer, Ad Account, Authentication, Authorization, Setup, Getting Started
**Difficulty:** beginner
**Content Type:** guide
**Source:** https://developers.facebook.com/docs/marketing-api/get-started
**Processed:** 2025-06-25T15:05:21.522Z