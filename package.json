{"name": "fb-marketing-api-scraper", "version": "1.0.0", "description": "Playwright scraper for Facebook Marketing API documentation", "main": "scraper.js", "scripts": {"install-playwright": "npx playwright install", "scrape": "node scraper.js", "scrape-ai": "node run-with-ai.js", "test": "node scraper.js --test", "test-ai": "node run-with-ai.js --test", "setup": "node setup.js", "analyze-quality": "node -e \"const ContentQualityAnalyzer = require('./content-quality-analyzer'); (async () => { const analyzer = new ContentQualityAnalyzer(); const results = await analyzer.analyzeAllContent(); await analyzer.generateReport(results); })();\"", "reprocess": "node reprocess-content.js", "reprocess-dry-run": "node reprocess-content.js --dry-run"}, "dependencies": {"@playwright/test": "^1.40.0", "playwright": "^1.40.0", "fs-extra": "^11.1.1", "turndown": "^7.1.2", "sanitize-filename": "^1.6.3", "cli-progress": "^3.12.0", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "keywords": ["facebook", "marketing-api", "scraper", "playwright", "documentation"], "author": "", "license": "MIT"}