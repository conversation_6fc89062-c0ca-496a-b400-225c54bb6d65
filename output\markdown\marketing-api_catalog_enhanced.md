# Facebook Marketing API Catalog

## Summary
Facebook catalogs are containers for product information and inventory that enable various commerce and advertising features. They support Collection Ads, Commerce distribution, Advantage+ Catalog Ads, Instagram Shopping, and WhatsApp conversational commerce.

## Key Points
- Facebook catalogs are containers for product information and inventory management
- Catalogs enable Collection Ads, Commerce distribution, Advantage+ Catalog Ads, Instagram Shopping, and WhatsApp commerce
- Comprehensive documentation includes overview, setup guides, best practices, API reference, and support resources
- Catalogs support dynamic personalized advertising and immersive shopping experiences
- Integration available across Facebook, Instagram, Marketplace, and WhatsApp platforms

## Content
# Catalog

A Facebook catalog is an object (or container) of information about your products and where you can upload your inventory. Learn more about [product catalog](/docs/marketing-api/catalog/overview).

## Common Uses

- **[Collection Ads](https://developers.facebook.com/docs/marketing-api/guides/collection)** — Use them in immersive formats.
- **[Commerce](https://developers.facebook.com/docs/commerce-platform/catalog/)** — Distribute products in Marketplace.
- **[Advantage+ Catalog Ads](https://developers.facebook.com/docs/marketing-api/dynamic-ads)** — Feature products in different formats to be served dynamically as personalized ads.
- **Instagram Shopping** — Feature in Instagram Shopping experiences, such as product tags on Instagram and soon on Instagram Shops.
- **WhatsApp** — Feature in conversational commerce in WhatsApp.

## Documentation Contents

### [Overview](/docs/marketing-api/catalog/overview)
Learn more about catalog and its components.

### [Get Started](/docs/marketing-api/catalog/getting-started)
Learn how to successfully set up a catalog for commerce or Advantage+ catalog ads, and more.

### [Guides](/docs/marketing-api/catalog/guides)
Learn more about the various guides and how to use them in your catalog.

### [Best Practices](https://developers.facebook.com/docs/marketing-api/catalog/best-practices)
Tips for using catalog effectively.

### [Reference](/docs/marketing-api/catalog/reference)
Product specifications and endpoint references.

### [Support](/docs/marketing-api/catalog/support)
Solutions to common problems and troubleshooting tips.

## See Also

- [Catalog Batch API](/docs/marketing-api/catalog-batch)

---
**Tags:** catalog, product-catalog, commerce, advertising, collection-ads, advantage-plus, instagram-shopping, whatsapp-commerce, marketplace, dynamic-ads
**Difficulty:** beginner
**Content Type:** overview
**Source:** https://developers.facebook.com/docs/marketing-api/catalog
**Processed:** 2025-06-25T15:50:18.334Z