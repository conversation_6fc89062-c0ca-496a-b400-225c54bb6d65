<div class="_1dyy" id="u_0_5_nx"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#bidding">Bidding</a></div><div class="_5-24 hidden_elem"><a href="#documentation-contents">Documentation Contents</a></div><div class="_5-24 hidden_elem"><a href="#overview">Overview</a></div><div class="_5-24 hidden_elem"><a href="#guides">Guides</a></div><div class="_5-24 hidden_elem"><a href="#support">Support</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_0_oo"><div class="_4cel"><span data-click-area="main"><div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8"><div class="_4-u3 _588p"><h1 id="bidding">Bidding</h1>

<p>Learn how your bids and budget work with Facebook's ad auction and delivery. This covers bidding options, placing bids for desired action, setting budget limits and tracking ads delivery. Facebook's auction functions the same way for API-created ads as they do for ads from Facebook tools. See <a href="https://www.facebook.com/business/help/delivery">Ads Help Center, Auction</a>.</p>
</div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2>Main Concepts</h2>

<ul>
<li><a href="/docs/marketing-api/bidding/overview/bid-strategy"><strong>Bid Strategies</strong></a> — Provide your bid preferences.</li>
<li><a href="/docs/marketing-api/bidding-and-optimization#opt"><strong>Optimization Goals</strong></a> — Define advertising goals you want to achieve when Facebook delivers your ads.</li>
<li><a href="/docs/marketing-api/bidding/overview/budgets"><strong>Budgets</strong></a></li>
<li><a href="/docs/marketing-api/pacing"><strong>Pacing and Scheduling</strong></a> — Determine how your ads budget is spent over time. </li>
<li><a href="/docs/marketing-api/bidding-and-optimization/billing-events"><strong>Billing Events</strong></a> - Defines events you want to pay for, including impressions, clicks, or various actions. </li>
</ul>

<h2>Common Use Cases</h2>

<ul>
<li><a href="/docs/marketing-api/bidding-and-optimization/campaign-budget-optimization"><strong>Campaign Budget Optimization</strong></a> — Optimize the distribution of a campaign budget across your campaign's ad sets. </li>
<li><a href="/docs/marketing-api/bidding/optimized-cost-per-mille"><strong>Optimized Cost Per Mille Ads</strong></a> — Prioritize your marketing goals. Then, automatically deliver ads towards these goals in the most effective way possible.</li>
<li><a href="/docs/marketing-api/cost-per-action-ads"><strong>Cost Per Action Ads</strong></a> — Specify conversion events and get charged by the amount of conversions. </li>
<li><a href="/docs/marketing-api/reachandfrequency"><strong>Reach and Frequency</strong></a> — Bid on a predicted unique audience reach for your ads on Facebook and Instagram and control display frequency.</li>
<li><a href="/docs/marketing-api/bidding-and-optimization/bid-multiplier"><strong>Bid Multipliers</strong></a> — Allows you to maintain a nuanced bidding strategy within a single ad set with one targeted audience. <strong>Available on a limited basis.</strong></li>
</ul>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="documentation-contents">Documentation Contents</h2>
<table class="uiGrid _51mz _57v1" cellspacing="0" cellpadding="0"><tbody><tr class="_51mx"><td class="_51m- vTop _57v2"><h3 id="overview"><a href="/docs/marketing-api/bidding-and-optimization">Overview</a></h3>

<p>Core concepts and usage requirements. Learn about <a href="/docs/marketing-api/bidding/overview/budgetsg"><strong>Budgets</strong></a>, <a href="/docs/marketing-api/bidding-and-optimization#opt"><strong>Optimization Goals</strong></a>, and <a href="/docs/marketing-api/bidding/overview/bid-strategy"><strong>Bid Strategies</strong></a>.</p>

<h3 id="guides"><a href="/docs/marketing-api/bidding/guides">Guides</a></h3>

<p>Use case based guides to help you perform specific actions.</p>
</td><td class="_51m- vTop _57v2 _51mw"><h3 id="support"><a href="/docs/marketing-api/support">Support</a></h3>

<p>Get support: FAQs, API updates, helpful links, Reference pages, and Ads Help Center.</p>
</td></tr></tbody></table><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div></span><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p _4_k"><fb:like href="https://developers.facebook.com/docs/marketing-api/bidding/" layout="button_count" share="1"></fb:like><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div id="developer_documentation_toolbar" data-referrer="developer_documentation_toolbar" data-click-area="toolbar"></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>