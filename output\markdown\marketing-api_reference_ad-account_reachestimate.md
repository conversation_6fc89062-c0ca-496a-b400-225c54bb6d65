# Ad Account Reachestimate

Graph API Version

[v23.0](#)

# Ad Account Reachestimate

## Reading

Used to get the audience size estimation based on a targeting specification using this ad account. This endpoint returns a range-based size in the form of two fields: `users_lower_bound` and `users_upper_bound`.

### Limitations

Reach estimates for custom audiences may not be available for certain businesses.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Freachestimate%3Ftargeting_spec%3D%257B%2522geo_locations%2522%253A%257B%2522countries%2522%253A%255B%2522US%2522%255D%257D%252C%2522age_min%2522%253A20%252C%2522age_max%2522%253A40%257D&version=v23.0)

```
`GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate?targeting_spec=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22age_min%22%3A20%2C%22age_max%22%3A40%7D HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`object_store_url`

string

Used in mobile app campaign. The url of the app in the app store.

`targeting_spec`

Targeting object

The targeting structure for reach estimate. `countries` is required. See [targeting](/docs/marketing-api/targeting-specs).

Required

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A single [AdAccountReachEstimate](/docs/graph-api/reference/ad-account-reach-estimate/) node.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

100

Invalid parameter

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

613

Calls to this api have exceeded the rate limit.

200

Permissions error

2641

Your ad includes or excludes locations that are currently restricted

2635

You are calling a deprecated version of the Ads API. Please update to the latest version.

190

Invalid OAuth 2.0 Access Token

## Creating

You can't perform this operation on this endpoint.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.