{"title": "Ad Videos", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_l_Oo\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Ad Videos</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-2\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type-2\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-2\">Error Codes</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_Qs\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_Jo\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_g0\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_9E\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Ad Videos</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>advideos</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/advideos/\"><code>/act_{ad_account_id}/advideos</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/graph-api/reference/video/\">Video</a> will be created.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_5_zx\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>audio_story_wave_animation_handle</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Everstore handle of wave animation used to burn audio story video</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>composer_session_id</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>description</code></span></div><div class=\"_yb\">UTF-8 string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Supports Emoji</span></div></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>end_offset</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_6_im\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>end_offset</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>file_size</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_7_vR\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>The size of the video file in bytes. Using during\n                                                                          <a href=\"/docs/marketing-api/advideo/#chunked\">chunked upload</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>file_url</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>fisheye_video_cropped</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>Whether the single fisheye video is cropped or not</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>front_z_rotation</code></span></div><div class=\"_yb\">float</div></td><td><p class=\"_yd\"></p><div><div><p>The front z rotation in degrees on the single fisheye video</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The name of the video in the library.</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>og_action_type_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>og_icon_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>og_object_id</code></span></div><div class=\"_yb\">OG object ID or URL string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>og_phrase</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>og_suggestion_mechanism</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>original_fov</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Original field of view of the source camera</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29\"><td><div class=\"_yc\"><span><code>original_projection_type</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_8_3I\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">enum {equirectangular, cubemap, half_equirectangular}</div></td><td><p class=\"_yd\"></p><div><div><p>Original Projection type of the video being uploaded</p>\n</div></div><p></p></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>prompt_id</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_9_Zj\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29\"><td><div class=\"_yc\"><span><code>prompt_tracking_string</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_a_A7\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_18\"><td><div class=\"_yc\"><span><code>referenced_sticker_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_19 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>slideshow_spec</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>slideshow_spec</p>\n</div></div><p></p></td></tr><tr class=\"row_19-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>images_urls</code></span></div><div class=\"_yb\">list&lt;URL&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>A 3-7 element array of the URLs of the images. Required.</p>\n</div></div><p></p></td></tr><tr class=\"row_19-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>duration_ms</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>The duration in milliseconds of each image. Default value is 1000.</p>\n</div></div><p></p></td></tr><tr class=\"row_19-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>transition_ms</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>The duration in milliseconds of the crossfade transition between images.\n      Default value is 1000.</p>\n</div></div><p></p></td></tr><tr class=\"row_19-3 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>reordering_opt_in</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_19-4 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>music_variations_opt_in</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>source</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The video, encoded as form data. See the\n                                                                          <a href=\"/docs/graph-api/reference/video-format\">Video Format</a> doc for more\n                                                                          details on video formats.</p>\n</div></div><p></p></td></tr><tr class=\"row_21 _5m29\"><td><div class=\"_yc\"><span><code>start_offset</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_b_Oo\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>The start position in byte of the chunk that\n                                                                          is being sent, inclusive. Used during\n                                                                          <a href=\"/docs/marketing-api/advideo/#chunked\">chunked upload</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_22\"><td><div class=\"_yc\"><span><code>time_since_original_post</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_23 _5m29\"><td><div class=\"_yc\"><span><code>title</code></span></div><div class=\"_yb\">UTF-8 string</div></td><td><p class=\"_yd\"></p><div><div><p>The name of the video being uploaded. Must be less than 255 characters. Special characters may count as more than 1 character.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Supports Emoji</span></div></td></tr><tr class=\"row_24\"><td><div class=\"_yc\"><span><code>transcode_setting_properties</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_c_7o\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Properties used in computing transcode settings for the video</p>\n</div></div><p></p></td></tr><tr class=\"row_25 _5m29\"><td><div class=\"_yc\"><span><code>unpublished_content_type</code></span></div><div class=\"_yb\">enum {SCHEDULED, SCHEDULED_RECURRING, DRAFT, ADS_POST, INLINE_CREATED, PUBLISHED, REVIEWABLE_BRANDED_CONTENT}</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_26\"><td><div class=\"_yc\"><span><code>upload_phase</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_d_zE\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">enum {start, transfer, finish, cancel}</div></td><td><p class=\"_yd\"></p><div><div><p>The phase during chunked upload. Using during\n                                                                          <a href=\"/docs/marketing-api/advideo/#chunked\">chunked upload</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_27 _5m29\"><td><div class=\"_yc\"><span><code>upload_session_id</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_e_QH\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The session ID of this chunked upload. Using\n                                                                          during <a href=\"/docs/marketing-api/advideo/#chunked\">chunked upload</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_28\"><td><div class=\"_yc\"><span><code>video_file_chunk</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_f_qQ\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The chunk of the video, between <code>start_offset</code>\n                                                                          and <code>end_offset</code>. Using during\n                                                                          <a href=\"/docs/marketing-api/advideo/#chunked\">chunked upload</a>.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div><div class=\"_uoj\"><code>upload_session_id</code>: numeric string, </div><div class=\"_uoj\"><code>video_id</code>: numeric string, </div><div class=\"_uoj\"><code>start_offset</code>: numeric string, </div><div class=\"_uoj\"><code>end_offset</code>: numeric string, </div><div class=\"_uoj\"><code>success</code>: bool, </div><div class=\"_uoj\"><code>skip_upload</code>: bool, </div><div class=\"_uoj\"><code>upload_domain</code>: string, </div><div class=\"_uoj\"><code>region_hint</code>: string, </div><div class=\"_uoj\"><code>xpv_asset_id</code>: numeric string, </div><div class=\"_uoj\"><code>is_xpv_single_prod</code>: bool, </div><div class=\"_uoj\"><code>transcode_bit_rate_bps</code>: numeric string, </div><div class=\"_uoj\"><code>transcode_dimension</code>: numeric string, </div><div class=\"_uoj\"><code>should_expand_to_transcode_dimension</code>: bool, </div><div class=\"_uoj\"><code>action_id</code>: string, </div><div class=\"_uoj\"><code>gop_size_seconds</code>: numeric string, </div><div class=\"_uoj\"><code>target_video_codec</code>: string, </div><div class=\"_uoj\"><code>target_hdr</code>: string, </div><div class=\"_uoj\"><code>maximum_frame_rate</code>: numeric string, </div>}</div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>381</td><td>There was a problem uploading your video file. Please try again.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>222</td><td>Video not visible</td></tr><tr><td>389</td><td>Unable to fetch video file from URL.</td></tr><tr><td>352</td><td>The video file you selected is in a format that we don't support.</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>382</td><td>The video file you tried to upload is too small. Please try again with a larger file.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>6001</td><td>There was a problem uploading your video. Please try again.</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\"><div class=\"_3-98\">You can dissociate a&nbsp;<a href=\"/docs/graph-api/reference/video/\">Video</a> from an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/ad-account/advideos/\"><code>/act_{ad_account_id}/advideos</code></a>.<div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_g_Fh\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>video_id</code></span></div><div class=\"_yb\">video ID</div></td><td><p class=\"_yd\"></p><div><div><p>Ad account library video ID</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3 id=\"return-type-2\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/ad-account/advideos", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/advideo/#chunked"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/", "timestamp": "2025-06-25T15:19:46.969Z", "reprocessedAt": "2025-06-25T16:23:05.734Z", "reprocessedWith": "claude-sonnet-4"}