const express = require('express');
const { body, query, validationResult } = require('express-validator');
const crypto = require('crypto');

const { asyncHandler, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const facebookService = require('../services/facebookService');
const User = require('../models/User');
const database = require('../config/database');
const logger = require('../config/logger');
const config = require('../config/config');

const router = express.Router();

/**
 * @swagger
 * /facebook/oauth-url:
 *   get:
 *     summary: Get Facebook OAuth URL
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: redirectUri
 *         required: true
 *         schema:
 *           type: string
 *         description: Redirect URI after OAuth
 *     responses:
 *       200:
 *         description: OAuth URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 oauthUrl:
 *                   type: string
 *                 state:
 *                   type: string
 */
router.get('/oauth-url', [
  query('redirectUri').isURL().withMessage('Valid redirect URI is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { redirectUri } = req.query;
  
  // Generate state parameter for security
  const state = crypto.randomBytes(32).toString('hex');
  
  // Store state in session/cache for verification
  await req.app.locals.redis.set(`oauth_state:${req.user.id}`, state, 600); // 10 minutes

  const oauthUrl = facebookService.generateOAuthUrl(redirectUri, state);

  res.json({
    oauthUrl,
    state
  });
}));

/**
 * @swagger
 * /facebook/oauth-callback:
 *   post:
 *     summary: Handle Facebook OAuth callback
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *             properties:
 *               code:
 *                 type: string
 *               state:
 *                 type: string
 *               redirectUri:
 *                 type: string
 *     responses:
 *       200:
 *         description: OAuth callback processed successfully
 */
router.post('/oauth-callback', [
  body('code').notEmpty().withMessage('Authorization code is required'),
  body('state').notEmpty().withMessage('State parameter is required'),
  body('redirectUri').isURL().withMessage('Valid redirect URI is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { code, state, redirectUri } = req.body;

  // Verify state parameter
  const storedState = await req.app.locals.redis.get(`oauth_state:${req.user.id}`);
  if (!storedState || storedState !== state) {
    throw new ValidationError('Invalid state parameter');
  }

  // Clean up state
  await req.app.locals.redis.del(`oauth_state:${req.user.id}`);

  try {
    // Exchange code for access token
    const tokenData = await facebookService.exchangeCodeForToken(code, redirectUri);
    
    // Get long-lived token
    const longLivedToken = await facebookService.getLongLivedToken(tokenData.access_token);
    
    // Get user profile
    const profile = await facebookService.getUserProfile(longLivedToken.access_token);
    
    // Get permissions
    const permissions = tokenData.scope ? tokenData.scope.split(',') : [];

    // Store Facebook account in database
    const knex = database.getKnex();
    await knex('facebook_accounts').insert({
      user_id: req.user.id,
      tenant_id: req.user.tenantId,
      facebook_user_id: profile.id,
      access_token: longLivedToken.access_token,
      token_expires_at: longLivedToken.expires_in ? 
        new Date(Date.now() + longLivedToken.expires_in * 1000) : null,
      permissions: JSON.stringify(permissions),
      created_at: new Date(),
      updated_at: new Date()
    }).onConflict(['user_id', 'facebook_user_id']).merge({
      access_token: longLivedToken.access_token,
      token_expires_at: longLivedToken.expires_in ? 
        new Date(Date.now() + longLivedToken.expires_in * 1000) : null,
      permissions: JSON.stringify(permissions),
      updated_at: new Date()
    });

    logger.audit('facebook_account_connected', req.user.id, {
      facebookUserId: profile.id,
      permissions
    });

    res.json({
      message: 'Facebook account connected successfully',
      profile,
      permissions
    });
  } catch (error) {
    logger.error('Facebook OAuth callback error:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook/ad-accounts:
 *   get:
 *     summary: Get user's Facebook ad accounts
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ad accounts retrieved successfully
 */
router.get('/ad-accounts', asyncHandler(async (req, res) => {
  // Get Facebook account from database
  const knex = database.getKnex();
  const facebookAccount = await knex('facebook_accounts')
    .where('user_id', req.user.id)
    .where('is_active', true)
    .first();

  if (!facebookAccount) {
    return res.json([]);
  }

  try {
    // Check rate limit
    await facebookService.checkRateLimit(req.user.id);

    // Try to get from cache first
    let adAccounts = await facebookService.getCachedAdAccounts(req.user.id);
    
    if (!adAccounts) {
      // Fetch from Facebook API
      adAccounts = await facebookService.getAdAccounts(facebookAccount.access_token);
      
      // Cache the results
      await facebookService.cacheAdAccounts(req.user.id, adAccounts);
    }

    // Update stored ad accounts
    await knex('facebook_accounts')
      .where('id', facebookAccount.id)
      .update({
        ad_accounts: JSON.stringify(adAccounts),
        last_sync_at: new Date(),
        updated_at: new Date()
      });

    res.json(adAccounts);
  } catch (error) {
    logger.error('Error fetching ad accounts:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook/pages:
 *   get:
 *     summary: Get user's Facebook pages
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pages retrieved successfully
 */
router.get('/pages', asyncHandler(async (req, res) => {
  // Get Facebook account from database
  const knex = database.getKnex();
  const facebookAccount = await knex('facebook_accounts')
    .where('user_id', req.user.id)
    .where('is_active', true)
    .first();

  if (!facebookAccount) {
    return res.json([]);
  }

  try {
    // Check rate limit
    await facebookService.checkRateLimit(req.user.id);

    // Fetch pages from Facebook API
    const pages = await facebookService.getPages(facebookAccount.access_token);

    // Update stored pages
    await knex('facebook_accounts')
      .where('id', facebookAccount.id)
      .update({
        pages: JSON.stringify(pages),
        last_sync_at: new Date(),
        updated_at: new Date()
      });

    res.json(pages);
  } catch (error) {
    logger.error('Error fetching pages:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook/campaigns:
 *   post:
 *     summary: Create a new Facebook campaign
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - adAccountId
 *               - name
 *               - objective
 *             properties:
 *               adAccountId:
 *                 type: string
 *               name:
 *                 type: string
 *               objective:
 *                 type: string
 *               status:
 *                 type: string
 *               specialAdCategories:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: Campaign created successfully
 */
router.post('/campaigns', [
  body('adAccountId').notEmpty().withMessage('Ad account ID is required'),
  body('name').trim().isLength({ min: 1 }).withMessage('Campaign name is required'),
  body('objective').notEmpty().withMessage('Campaign objective is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  // Get Facebook account from database
  const knex = database.getKnex();
  const facebookAccount = await knex('facebook_accounts')
    .where('user_id', req.user.id)
    .where('is_active', true)
    .first();

  if (!facebookAccount) {
    throw new NotFoundError('Facebook account not connected');
  }

  try {
    // Check rate limit
    await facebookService.checkRateLimit(req.user.id);

    const { adAccountId, ...campaignData } = req.body;
    
    // Create campaign via Facebook API
    const campaign = await facebookService.createCampaign(
      facebookAccount.access_token,
      adAccountId,
      campaignData
    );

    logger.audit('facebook_campaign_created', req.user.id, {
      campaignId: campaign.id,
      adAccountId,
      campaignName: campaignData.name
    });

    res.status(201).json({
      message: 'Campaign created successfully',
      campaign
    });
  } catch (error) {
    logger.error('Error creating campaign:', error);
    throw error;
  }
}));

/**
 * @swagger
 * /facebook/campaigns/{adAccountId}:
 *   get:
 *     summary: Get campaigns for an ad account
 *     tags: [Facebook]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: adAccountId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Campaigns retrieved successfully
 */
router.get('/campaigns/:adAccountId', asyncHandler(async (req, res) => {
  const { adAccountId } = req.params;

  // Get Facebook account from database
  const knex = database.getKnex();
  const facebookAccount = await knex('facebook_accounts')
    .where('user_id', req.user.id)
    .where('is_active', true)
    .first();

  if (!facebookAccount) {
    throw new NotFoundError('Facebook account not connected');
  }

  try {
    // Check rate limit
    await facebookService.checkRateLimit(req.user.id);

    // This would require implementing getCampaigns in facebookService
    // For now, return empty array as placeholder
    const campaigns = [];

    res.json(campaigns);
  } catch (error) {
    logger.error('Error fetching campaigns:', error);
    throw error;
  }
}));

module.exports = router;
