# Ad Creative

On This Page

[Ad Creative](#ad-creative)

[Creative](#creative)

[Limits](#limits)

[Read](#read)

[Placements](#placements)

[Preview an Ad](#previews)

[See More](#see-more)

# Ad Creative

Use Facebook ads with your existing customers and to reach new ones. Each guide describes Facebook ads products to help meet your advertising goals. There are several types of ad units with a variety of appearances, placement and creative options. For guidelines on ads units as creative content, see [Facebook Ads Guide](https://www.facebook.com/business/ads-guide/?tab0=Mobile%20News%20Feed).

## Creative

An ad creative is an object that contains all the data for visually rendering the ad itself. In the API, there are different types of ads that you can create on Facebook, all listed [here](/docs/reference/ads-api/adcreative#overview).

If you have a [campaign](/docs/marketing-api/reference/ad-campaign-group) with the Page Post Engagement Objective, you can now create an ad that promotes a post made by the page. This is considered a Page post ad. Page post ads require a field called `object_story_id`, which is the `id` property of a Page post. Learn more about [Ad Creative, Reference](/docs/reference/ads-api/adcreative#create).

An ad creative has three parts:

*   Ad [creative](#creatives) itself, defined by the visual attributes of the creative object
*   [Placement](#placements) that the ad runs on
*   [Preview](#previews) of the unit itself, per placement

To create the ad creative object, make the following call:

```
curl \-X POST \\
  \-F 'name="Sample Promoted Post"' \\
  \-F 'object\_story\_id="<PAGE\_ID>\_<POST\_ID>"' \\
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/adcreatives
  
[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives?&version=v23.0&name=Sample+Promoted+Post&object_story_id=%3CPAGE_ID%3E_%3CPOST_ID%3E)
```
  

The response to the API call is the `id` of the creative object. Store this; you need it for the ad object:

```

curl \-X POST \\
  \-F 'name="My Ad"' \\
  \-F 'adset\_id="<AD\_SET\_ID>"' \\
  \-F 'creative={
       "creative\_id": "<CREATIVE\_ID>"
     }' \\
  \-F 'status="PAUSED"' \\
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/ads
  
[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fads?&version=v23.0&name=My+Ad&adset_id=%3CAD_SET_ID%3E&creative=%7B%22creative_id%22%3A%22%3CCREATIVE_ID%3E%22%7D&status=PAUSED)
```

### Limits

There are limits on the creative's text, image size, image aspect ratio and other aspects of the creative. See the [Ads Guide](https://www.facebook.com/business/ads-guide).

### Read

In the Ads API, each field you want to retrieve needs to be asked for explicitly, except for `id`. Each object's [Reference](/docs/reference/ads-api/adcreative/#read) has a section for reading back the object and lists what fields are readable. For the creative, it's the same fields as specified when creating the object, and `id`.

```

curl \-G \\
  \-d 'fields=name,object\_story\_id' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
https://graph.facebook.com/`v23.0`/<CREATIVE\_ID>
 
[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=%3CCREATIVE_ID%3E%2F?fields=name%2Cobject_story_id&version=v23.0)
```

[](#)

## Placements

A placement is where your ad is shown on Facebook, such as on Feed on desktop, Feed on a mobile device or on the right column. See [Ads Product Guide](https://www.facebook.com/business/ads-guide/).

We encourage you to run ads across the full range of available placements. Facebook’s ad auction is designed to deliver ad impressions to the placement most likely to drive campaign results at the lowest possible cost.

The easiest way to take advantage of this optimization is to leave this field blank. You can also select specific placements in an ad set’s target\_spec.

This example has a page post ad. The available placements are Mobile Feed, Desktop Feed and Right column of Facebook. In the API, see [Placement Options](/docs/reference/ads-api/targeting-specs/#placement). If you choose `desktopfeed` and `rightcolumn` as the `page_type`, the ad runs on Desktop Feed and Right column placements. Any ad created below this ad set has only the desktop placement.

```
curl \-X POST \\
  \-F 'name=Desktop Ad Set' \\
  \-F 'campaign\_id=<CAMPAIGN\_ID>' \\
  \-F 'daily\_budget=10000' \\
  \-F 'targeting={ 
    "geo\_locations": {"countries":\["US"\]}, 
    "publisher\_platforms": \["facebook","audience\_network"\] 
  }' \\
  \-F 'optimization\_goal=LINK\_CLICKS' \\
  \-F 'billing\_event=IMPRESSIONS' \\
  \-F 'bid\_amount=1000' \\
  \-F 'status=PAUSED' \\
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/adsets
  

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets&version=v23.0&name=Desktop%20Ad%20Set&campaign_id=%3CCAMPAIGN_ID%3E&daily_budget=10000&targeting=%7B%22geo_locations%22%3A%20%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%20%22publisher_platforms%22%3A%20%5B%22facebook%22%2C%22audience_network%22%5D%20%7D&optimization_goal=LINK_CLICKS&billing_event=IMPRESSIONS&bid_amount=1000&status=PAUSED)
```

[](#)

## Preview an Ad

You preview an ad in one of two ways—with [ad preview API](/docs/reference/ads-api/generatepreview/) or the [ad preview plugin](/docs/reference/ads-api/ad-preview-plugin).

There are three ways to generate a preview with the API:

1.  By ad ID
2.  By ad creative ID
3.  By supplying a creative spec

Following the [reference](/docs/reference/ads-api/generatepreview/#html) docs for the preview API, the minimum required API call is:

```

curl \-G \\
  \--data\-urlencode 'creative="<CREATIVE\_SPEC>"' \\
  \-d 'ad\_format="<AD\_FORMAT>"' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/generatepreviews
  
[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%3CCREATIVE_SPEC%3E%26ad_format=%3CAD_FORMAT%3E&version=v23.0)
```
  

The creative spec is an array of each field and value required to create the ad creative.

Currently, our ad creative call looks like this:

```

curl \-X POST \\
  \-F 'name="Sample Promoted Post"' \\
  \-F 'object\_story\_id="<PAGE\_ID>\_<POST\_ID>"' \\
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/adcreatives

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives?&version=v23.0&name=Sample+Promoted+Post&object_story_id=%3CPAGE_ID%3E_%3CPOST_ID%3E)
```
  

Take `object_story_id` and use it in the preview API call:

```

curl \-G \\
  \-d 'creative={"object\_story\_id":"<PAGE\_ID>\_<POST\_ID>"}' \\
  \-d 'ad\_format=<AD\_FORMAT>' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/generatepreviews

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews%3Fcreative%3D%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7Dad_format%3D%3CAD_FORMAT%3E&version=v20.0)
```

The available values for `ad_format` differ a bit from `page_types`. But, in this scenario, Desktop Feed and Right column of Facebook are selected. This requires you to make two API calls to generate the previews for each placement:

```


curl \-G \\
  \-d 'creative={"object\_story\_id":"<PAGE\_ID>\_<POST\_ID>"}' \\
  \-d 'ad\_format=DESKTOP\_FEED\_STANDARD' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/generatepreviews
  
[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7D%26ad_format=DESKTOP_FEED_STANDARD&version=v23.0)
```
```

curl \-G \\
  \-d 'creative={"object\_story\_id":"<PAGE\_ID>\_<POST\_ID>"}' \\
  \-d 'ad\_format=RIGHT\_COLUMN\_STANDARD' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/generatepreviews

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews%3Fcreative%3D%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7D,ad_format%3DRIGHT_COLUMN_STANDARD&version=v20.0)
```

The response is an iFrame that's valid for 24 hrs.

[](#)

## See More

*   [Ad Creative](/docs/marketing-api/reference/ad-creative)
*   [Facebook App Ads](/docs/app-ads)
*   [Ads Guide](https://www.facebook.com/business/ads-guide)

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '***************'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)