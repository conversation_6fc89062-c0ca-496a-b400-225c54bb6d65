# Facebook Marketing API - Ad Account Insights Reference

## Summary
Complete reference documentation for the Facebook Marketing API Insights endpoint, which provides advertising performance metrics and analytics. Covers reading insights data, creating async reports, and all available parameters and fields for ad account analytics.

## Key Points
- Provides comprehensive advertising performance metrics with support for breakdowns and custom time ranges
- Supports both synchronous reading and asynchronous report generation for large datasets
- Includes estimated and in-development metrics that should be used with appropriate caution
- iOS 14.5 attribution changes affect non-inline conversion metric aggregation
- Maximum 37 months of historical data available with various attribution window options

## API Endpoints
- `GET /v23.0/{ad-account-id}/insights`
- `POST /v23.0/{ad-account-id}/insights`

## Parameters
- date_preset
- time_range
- time_ranges
- time_increment
- action_attribution_windows
- breakdowns
- action_breakdowns
- fields
- filtering
- level
- sort
- use_account_attribution_setting
- use_unified_attribution_setting

## Content
# Facebook Marketing API - Ad Account Insights

## Overview

The Insights API provides comprehensive advertising performance metrics and analytics for Facebook ad accounts. It supports both synchronous and asynchronous reporting with advanced features like deduplication, breakdowns, and custom time ranges.

### Important Notes

- **Estimated Metrics**: Some metrics are estimated and provide directional insights for outcomes that are hard to precisely quantify
- **In Development Metrics**: Certain metrics are still being tested and may change as methodologies improve
- **iOS 14.5 Impact**: Non-inline conversion metrics cannot be aggregated across iOS 14.5 and non-iOS 14.5 campaigns due to attribution logic differences
- **Data Retention**: Maximum of 37 months of historical data available (replaces previous `lifetime` preset)

## Reading Insights

Retrieves advertising performance insights with support for deduped metrics, sorting, and async reporting.

### Endpoint
```
GET /v23.0/{ad-account-id}/insights
```

### Example Request
```bash
curl -X GET -G \
  -d 'fields="impressions"' \
  -d 'breakdown="publisher_platform"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_ACCOUNT_ID>/insights
```

### Key Parameters

#### Time Range Parameters
- `date_preset`: Predefined time ranges (today, yesterday, last_30d, etc.)
- `time_range`: Custom date range with since/until dates
- `time_ranges`: Array of multiple time ranges
- `time_increment`: Granularity (daily, monthly, or N-day periods)

#### Attribution Parameters
- `action_attribution_windows`: Attribution windows for conversions (1d_view, 7d_click, 28d_click, etc.)
- `use_account_attribution_setting`: Use account-level attribution settings
- `use_unified_attribution_setting`: Use ad set-level unified attribution

#### Breakdown Parameters
- `breakdowns`: Dimension breakdowns (age, gender, country, device_platform, etc.)
- `action_breakdowns`: Action-specific breakdowns (action_type, action_device, etc.)

#### Filtering and Sorting
- `filtering`: Array of filter objects with field, operator, and value
- `sort`: Field and direction for sorting results
- `level`: Reporting level (ad, adset, campaign, account)

### Response Structure

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Core Metrics Fields

**Performance Metrics**
- `impressions`: Number of times ads were shown
- `clicks`: Total clicks on ads
- `spend`: Total amount spent
- `reach`: Estimated unique people reached
- `frequency`: Average times each person saw the ad

**Cost Metrics**
- `cpc`: Cost per click
- `cpm`: Cost per 1,000 impressions
- `cpp`: Cost per 1,000 people reached
- `cost_per_action_type`: Cost per specific action

**Conversion Metrics**
- `actions`: Actions attributed to ads
- `action_values`: Value of conversions
- `conversions`: Conversion events
- `conversion_values`: Conversion values

**Video Metrics**
- `video_play_actions`: Video play starts
- `video_30_sec_watched_actions`: 30-second video views
- `video_p25_watched_actions`: 25% video completion
- `video_avg_time_watched_actions`: Average watch time

## Creating Async Reports

For large data sets or complex queries, create asynchronous reports.

### Endpoint
```
POST /v23.0/{ad-account-id}/insights
```

### Parameters
Same parameters as reading insights, plus:
- `export_format`: File format ("xls", "csv")
- `export_name`: Custom filename
- `export_columns`: Specific fields to export

### Response
```json
{
  "report_run_id": "<report_id>"
}
```

## Common Breakdowns

### Demographic Breakdowns
- `age`: Age ranges
- `gender`: Male/female/unknown
- `country`: Country codes
- `region`: Geographic regions

### Platform Breakdowns
- `publisher_platform`: Facebook, Instagram, Messenger, etc.
- `platform_position`: Feed, stories, right column, etc.
- `device_platform`: Mobile, desktop, etc.
- `impression_device`: Specific device types

### Creative Breakdowns
- `ad_format_asset`: Ad format types
- `body_asset`: Ad body text variations
- `image_asset`: Image creative variations
- `video_asset`: Video creative variations

## Error Handling

### Common Error Codes
- `100`: Invalid parameter
- `190`: Invalid OAuth 2.0 Access Token
- `200`: Permissions error
- `2635`: Deprecated API version
- `3018`: Date range exceeds 37-month limit

## Best Practices

1. **Use appropriate attribution windows** for your business model
2. **Limit breakdowns** to avoid data fragmentation
3. **Use async reporting** for large data sets
4. **Cache results** when possible to reduce API calls
5. **Handle estimated metrics** appropriately in analysis
6. **Monitor for deprecated fields** and update accordingly

## Examples
GET request with impressions field and publisher_platform breakdown

PHP SDK implementation with error handling

JavaScript SDK API call

cURL command with access token

---
**Tags:** facebook-marketing-api, insights, analytics, advertising-metrics, performance-data, breakdowns, attribution, reporting  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/  
**Processed:** 2025-06-25T16:14:56.935Z