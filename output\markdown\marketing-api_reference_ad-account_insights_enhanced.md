# Facebook Marketing API - Ad Account Insights Reference

## Summary
Complete reference documentation for the Facebook Marketing API Insights endpoint, which provides advertising performance metrics and analytics. Covers reading insights data, creating async reports, and all available parameters and fields for ad account analytics.

## Key Points
- Provides comprehensive advertising performance metrics with support for deduplication and advanced breakdowns
- Supports both synchronous and asynchronous reporting with export capabilities
- Offers extensive filtering, sorting, and time range options for flexible data analysis
- Includes estimated and in-development metrics that require careful interpretation
- Maximum 37 months of historical data available with iOS 14.5 attribution limitations

## API Endpoints
- `GET /v23.0/{ad-account-id}/insights`
- `POST /v23.0/{ad-account-id}/insights`

## Parameters
- date_preset
- time_range
- time_ranges
- time_increment
- breakdowns
- action_breakdowns
- action_attribution_windows
- fields
- level
- filtering
- sort
- export_format
- export_name
- use_account_attribution_setting
- use_unified_attribution_setting

## Content
# Facebook Marketing API - Ad Account Insights

## Overview

The Insights API provides comprehensive advertising performance metrics and analytics for Facebook ad accounts. It supports both synchronous and asynchronous reporting with advanced features like deduplication, breakdowns, and custom time ranges.

### Important Notes

- **Estimated Metrics**: Some metrics are estimated and provide directional insights for outcomes that are hard to precisely quantify
- **In Development Metrics**: Certain metrics are still being tested and may change as methodologies improve
- **iOS 14.5 Impact**: Non-inline conversion metrics cannot be aggregated across iOS 14.5 and non-iOS 14.5 campaigns due to attribution logic differences
- **Data Retention**: Maximum of 37 months of historical data available

## Reading Insights

Retrieves advertising performance insights with support for deduped metrics, sorting, and async reporting.

### Endpoint
```
GET /v23.0/{ad-account-id}/insights
```

### Key Parameters

#### Time Range Parameters
- `date_preset`: Predefined time ranges (today, yesterday, last_30d, etc.)
- `time_range`: Custom date range with since/until dates
- `time_ranges`: Array of multiple time ranges
- `time_increment`: Granularity (all_days, monthly, or 1-90 days)

#### Breakdown Parameters
- `breakdowns`: How to segment results (age, gender, country, platform, etc.)
- `action_breakdowns`: How to break down action results
- `action_attribution_windows`: Attribution windows for actions

#### Data Selection
- `fields`: Specific metrics to retrieve
- `level`: Reporting level (ad, adset, campaign, account)
- `filtering`: Array of filter objects
- `sort`: Field and direction for sorting results

### Response Structure

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

## Key Metrics Available

### Core Metrics
- `impressions`: Number of times ads were shown
- `clicks`: Total clicks on ads
- `spend`: Total amount spent
- `reach`: Estimated unique people reached
- `frequency`: Average times each person saw ads

### Performance Metrics
- `cpc`: Cost per click
- `cpm`: Cost per 1,000 impressions
- `ctr`: Click-through rate
- `cost_per_action_type`: Cost per specific action

### Conversion Metrics
- `actions`: Actions attributed to ads
- `action_values`: Value of conversions
- `conversions`: Conversion events
- `purchase_roas`: Return on ad spend

### Video Metrics
- `video_play_actions`: Video play starts
- `video_30_sec_watched_actions`: 30-second video views
- `video_p25_watched_actions`: 25% video completion
- `video_avg_time_watched_actions`: Average watch time

## Creating Async Reports

For large data requests, create asynchronous reports that can be retrieved later.

### Endpoint
```
POST /v23.0/{ad-account-id}/insights
```

### Export Options
- `export_format`: File format ("xls", "csv")
- `export_name`: Custom filename
- `export_columns`: Specific fields to export

### Response
```json
{
  "report_run_id": "string"
}
```

## Advanced Features

### Attribution Settings
- `use_account_attribution_setting`: Use account-level attribution
- `use_unified_attribution_setting`: Use ad set-level unified attribution

### Filtering
Supports complex filtering with operators:
- EQUAL, NOT_EQUAL
- GREATER_THAN, LESS_THAN
- IN_RANGE, NOT_IN_RANGE
- CONTAIN, NOT_CONTAIN
- IN, NOT_IN

### Breakdowns
Combine multiple breakdowns for detailed analysis:
- Demographics: age, gender
- Geographic: country, region, dma
- Platform: publisher_platform, device_platform
- Time: hourly_stats_aggregated_by_advertiser_time_zone

## Error Handling

Common error codes:
- `100`: Invalid parameter
- `190`: Invalid OAuth 2.0 Access Token
- `200`: Permissions error
- `3018`: Start date beyond 37 months limit
- `2635`: Deprecated API version

## Best Practices

1. Use appropriate attribution windows for your business model
2. Leverage breakdowns for detailed analysis
3. Consider async reporting for large datasets
4. Monitor estimated and in-development metrics carefully
5. Use unified attribution settings for consistency with Ads Manager

## Examples
GET /v23.0/<AD_SET_ID>/insights?fields=impressions&breakdown=publisher_platform

PHP SDK example with Facebook\FacebookResponse

JavaScript SDK example with FB.api

cURL example with access token

---
**Tags:** facebook-marketing-api, insights, analytics, advertising-metrics, performance-reporting, ad-account, breakdowns, attribution
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/
**Processed:** 2025-06-25T15:30:12.030Z