{"title": "Marketing API Reference", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_g_hg\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#marketing-api-reference\">Marketing API Reference</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#user\">User</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#user_edges\">Edges</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#account\">Ad Account</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#account_edges\">Edges</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad\">Ad</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad_edges\">Edges</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#adset\">Ad Set</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#adset_edges\">Edges</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#campaign\">Ad Campaign</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#campaign_edges\">Edges</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#creative\">Ad Creative</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#creative_edges\">Edges</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_BQ\"><div class=\"_4cel\"><div><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Marketing API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_6t\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_pT\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_hm\"></div></span></div></div></div></div></div><span data-click-area=\"main\"><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"marketing-api-reference\">Marketing API Reference</h1>\n\n<h4>Marketing API Root Nodes</h4>\n\n<p>This is a full list of root nodes for the Facebook Marketing API with links to reference docs for each. For background on the API's architecture how to call root nodes and their edges, see <a href=\"/docs/graph-api/using-graph-api\">Using the Graph API</a>.</p>\n<div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>To access all reference information you will need to be logged in to Facebook.</p>\n</div></div><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nNode\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_5_iq\"><tr class=\"row_0\"><td><p><a href=\"/docs/marketing-api/reference/ad-account-user\"><code>/{AD_ACCOUNT_USER_ID}</code></a></p>\n</td><td><p>Someone on Facebook who creates ads. Each ad user can have a role on several ad accounts.</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><a href=\"/docs/marketing-api/reference/ad-account\"><code>/act_{AD_ACCOUNT_ID}</code></a></p>\n</td><td><p>Represents the business entity managing ads.</p>\n</td></tr><tr class=\"row_2\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/adgroup\"><code>/{AD_ID}</code></a></p>\n</td><td><p>Contains information for an ad, such as creative elements and measurement information.</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p><a href=\"/docs/marketing-api/reference/ad-creative\"><code>/{AD_CREATIVE_ID}</code></a></p>\n</td><td><p>Format for your image, carousel, collection, or video ad.</p>\n</td></tr><tr class=\"row_4\"><td><p><a href=\"/docs/marketing-api/reference/ad-campaign\"><code>/{AD_SET_ID}</code></a></p>\n</td><td><p>Contains all ads that share the same budget, schedule, bid, and targeting.</p>\n</td></tr><tr class=\"row_5 _5m29\"><td><p><a href=\"/docs/marketing-api/reference/ad-campaign-group\"><code>/{AD_CAMPAIGN_ID}</code></a></p>\n</td><td><p>Defines your ad campaigns' objective. Contains one or more ad set.</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"user\">User</h2>\n\n<h3 id=\"user_edges\">Edges</h3>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nEdge\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_6_Vf\"><tr class=\"row_0\"><td><p><a href=\"https://developers.facebook.com/docs/graph-api/reference/user/adaccounts\"><code>/adaccounts</code></a></p>\n</td><td><p>All ad accounts associated with this person</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/graph-api/reference/user/accounts/\"><code>/accounts</code></a></p>\n</td><td><p>All pages and places that someone is an admin of</p>\n</td></tr><tr class=\"row_2\"><td><p><a href=\"https://developers.facebook.com/docs/graph-api/reference/user/promotable_events/\"><code>/promotable_events</code></a></p>\n</td><td><p>All promotable events you created or promotable page events that belong to pages you are an admin for</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"account\">Ad Account</h2>\n\n<p>All collections of ad objects in Marketing APIs belong to an <a href=\"/docs/reference/ads-api/adaccount\">ad account</a>.</p>\n\n<h3 id=\"account_edges\">Edges</h3>\n\n<p>The most popular edges of the Ad Account node. Visit the <a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account#edges\">Ad Account Edges reference</a> for a complete list of all edges.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nEdge\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_7_Es\"><tr class=\"row_0\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/\"><code>/adcreatives</code></a></p>\n</td><td><p>Defines your ad's appearance and content</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/adimages/\"><code>/adimages</code></a></p>\n</td><td><p>Library of images to use in ad creatives. Can be uploaded and managed independently</p>\n</td></tr><tr class=\"row_2\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/\"><code>/ads</code></a></p>\n</td><td><p>Data for an ad, such as creative elements and measurement information</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/\"><code>/adsets</code></a></p>\n</td><td><p>Contain all ads that share the same budget, schedule, bid, and targeting</p>\n</td></tr><tr class=\"row_4\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/\"><code>/advideos</code></a></p>\n</td><td><p>Library of videos for use in ad creatives. Can be uploaded and managed independently</p>\n</td></tr><tr class=\"row_5 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/campaigns/\"><code>/campaigns</code></a></p>\n</td><td><p>Define your campaigns' objective and contain one or more ad sets</p>\n</td></tr><tr class=\"row_6\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiences\"><code>/customaudiences</code></a></p>\n</td><td><p>The custom audiences owned by/shared with this ad account</p>\n</td></tr><tr class=\"row_7 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/\"><code>/insights</code></a></p>\n</td><td><p>Interface for insights. De-dupes results across child objects, provides sorting, and async reports.</p>\n</td></tr><tr class=\"row_8\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/users/\"><code>/users</code></a></p>\n</td><td><p>List of people assocated with an ad account</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"ad\">Ad</h2>\n\n<p>An individual ad associated with an ad set.</p>\n\n<h3 id=\"ad_edges\">Edges</h3>\n\n<p>The most popular edges of the Ad node. Visit the <a href=\"https://developers.facebook.com/docs/marketing-api/reference/adgroup#edges\">Ad Edges reference</a> for a complete list of all edges.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nEdge\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_8_kj\"><tr class=\"row_0\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/adgroup/adcreatives/\"><code>/adcreatives</code></a></p>\n</td><td><p>Defines your ad's appearance and content</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/adgroup/insights/\"><code>/insights</code></a></p>\n</td><td><p>Insights on your advertising performance.</p>\n</td></tr><tr class=\"row_2\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/adgroup/leads/\"><code>/leads</code></a></p>\n</td><td><p>Any leads associated with with a Lead Ad.</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/adgroup/previews/\"><code>/previews</code></a></p>\n</td><td><p>Generate ad previews from an existing ad</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"adset\">Ad Set</h2>\n\n<p>An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data.</p>\n\n<h3 id=\"adset_edges\">Edges</h3>\n\n<p>The most popular edges of the Ad Set node. Visit the <a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign\">Ad Set Edges reference</a> for a complete list of all edges.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nEdge\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_9_MG\"><tr class=\"row_0\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/activities/\"><code>/activities</code></a></p>\n</td><td><p>Log of actions taken on the ad set</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/adcreatives/\"><code>/adcreatives</code></a></p>\n</td><td><p>Defines your ad's content and appearance</p>\n</td></tr><tr class=\"row_2\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/ads/\"><code>/ads</code></a></p>\n</td><td><p>Data necessary for an ad, such as creative elements and measurement information</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/insights/\"><code>/insights</code></a></p>\n</td><td><p>Insights on your advertising performance.</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"campaign\">Ad Campaign</h2>\n\n<p>A campaign is the highest level organizational structure within an ad account and should represent a single objective for an advertiser.</p>\n\n<h3 id=\"campaign_edges\">Edges</h3>\n\n<p>The most popular edges of the Ad Campaign node. Visit the <a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group\">Ad Campaign Edges reference</a> for a complete list of all edges.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nEdge\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_a_XF\"><tr class=\"row_0\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/ads\"><code>/ads</code></a></p>\n</td><td><p>Data necessary for an ad, such as creative elements and measurement information</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group\"><code>/adsets</code></a></p>\n</td><td><p>Contain all ads that share the same budget, schedule, bid, and targeting.</p>\n</td></tr><tr class=\"row_2\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/insights\"><code>/insights</code></a></p>\n</td><td><p>Insights on your advertising performance.</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"creative\">Ad Creative</h2>\n\n<p>The format which provides layout and contains content for the ad.</p>\n\n<h3 id=\"creative_edges\">Edges</h3>\n\n<p>The most popular edges of the Ad Creative node. Visit the <a href=\"#\" role=\"button\">Ad Creative Edges reference</a> for a complete list of all edges.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nEdge\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_b_3A\"><tr class=\"row_0\"><td><p><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative/previews/\"><code>/previews</code></a></p>\n</td><td><p>Generate ad previews from the existing ad creative object</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div></span><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p _4_k\"><fb:like href=\"https://developers.facebook.com/docs/marketing-api/reference/\" layout=\"button_count\" share=\"1\"></fb:like><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div id=\"developer_documentation_toolbar\" data-referrer=\"developer_documentation_toolbar\" data-click-area=\"toolbar\"></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '217404712025032');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/ad-account-user", "/docs/marketing-api/reference/ad-creative", "/docs/marketing-api/reference/ad-campaign", "/docs/marketing-api/reference/ad-campaign-group"], "url": "https://developers.facebook.com/docs/marketing-api/reference/v23.0", "timestamp": "2025-06-25T15:09:22.510Z"}