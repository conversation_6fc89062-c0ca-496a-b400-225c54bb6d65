# Ad Previews

Marketing API Version

[v23.0](#)

# Ad Previews

Preview existing ads and generate previews of ads you want to create. Generated previews are based on your ad creative. For ad preview **provide a user access token**, not a Page access token. For example, preview existing ad creative:

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X GET \
  -d 'ad_format="DESKTOP_FEED_STANDARD"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>/previews`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=%3CCREATIVE_ID%3E%2Fpreviews?ad_format=DESKTOP_FEED_STANDARD&version=v23.0)

Give Feedback

  

Or preview using creative spec for a domain ad for an external website:

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X GET \
  -d 'creative="<CREATIVE_SPEC>"' \
  -d 'ad_format="<AD_FORMAT>"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%3CCREATIVE_SPEC%3E%26ad_format=%3CAD_FORMAT%3E&version=v23.0)

Give Feedback

## Generating Previews

There are a few ways to generate a preview, using:

*   [Ad](/docs/reference/ads-api/adgroup/) ID
*   [Ad Creative](/docs/reference/ads-api/adcreative/) ID
*   Supplying a creative spec

To use an ad ID for an existing ad, use ad's [`previews`](/docs/marketing-api/reference/adgroup/previews).

```
https://graph.facebook.com/<API\_VERSION>/<AD\_ID>/previews
```

To use an existing ad creative's ID, use the ad creative's [`previews`](/docs/marketing-api/reference/ad-creative/previews).

```
https://graph.facebook.com/<API\_VERSION>/<AD\_CREATIVE\_ID>/previews
```

To use an [ad creative spec](/docs/reference/ads-api/adcreative/), you have two endpoint options:

*   [`/act_<AD_ACCOUNT_ID>/generatepreviews`](/docs/marketing-api/reference/ad-account/generatepreviews/), or
*   [`/generatepreviews`](/docs/graph-api/reference/generatepreviews/) — The creative passed in this call should not be associated with a specific ad account.

For Advantage+ catalog ads pass the entire object\_story\_spec into the /generatepreviews endpoint, and also use `product_item_ids` described in [Advantage+ Catalog Ads, Preview](/docs/marketing-api/dynamic-product-ads/ads-management#dynamicpreview).

Previews from an ad account are only visible to people who have a role on the ad account. Previews generated using `generatepreviews` edge are visible to anyone.

```
https://graph.facebook.com/API\_VERSION>/act\_<AD\_ACCOUNT\_ID>/generatepreviews
https://graph.facebook.com/API\_VERSION>/generatepreviews
```

Any of the four endpoints above will return an [ad preview object](/docs/marketing-api/reference/ad-preview/)

## Examples

Create a preview using `object_story_spec`:

PHP Business SDKPython Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdAccount;
use FacebookAds\Object\AdCreative;
use FacebookAds\Object\Fields\AdCreativeFields;
use FacebookAds\Object\Fields\AdPreviewFields;
use FacebookAds\Object\Fields\AdCreativeLinkDataFields;
use FacebookAds\Object\Fields\AdCreativeObjectStorySpecFields;
use FacebookAds\Object\AdCreativeLinkData;
use FacebookAds\Object\AdCreativeObjectStorySpec;
use FacebookAds\Object\Values\AdPreviewAdFormatValues;
use FacebookAds\Object\Values\AdCreativeCallToActionTypeValues;

$link_data = new AdCreativeLinkData();
$link_data->setData(array(
  AdCreativeLinkDataFields::LINK => '<URL>',
  AdCreativeLinkDataFields::MESSAGE => 'Message',
  AdCreativeLinkDataFields::NAME => 'Name',
  AdCreativeLinkDataFields::DESCRIPTION => 'Description',
  AdCreativeLinkDataFields::CALL_TO_ACTION => array(
    'type' => AdCreativeCallToActionTypeValues::SIGN_UP,
    'value' => array(
      'link' => '<URL>',
    ),
  ),
));

$story = new AdCreativeObjectStorySpec();
$story->setData(array(
  AdCreativeObjectStorySpecFields::PAGE_ID => <PAGE_ID>,
  AdCreativeObjectStorySpecFields::LINK_DATA => $link_data,
));

$creative = new AdCreative();
$creative->setData(array(
  AdCreativeFields::OBJECT_STORY_SPEC => $story,
));

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$account->getGeneratePreviews(array(), array(
  AdPreviewFields::CREATIVE => $creative,
  AdPreviewFields::AD_FORMAT => AdPreviewAdFormatValues::DESKTOP_FEED_STANDARD,
));`
```

Create an multi-product ad preview using `object_story_id`. To get `object_story_id`, first create a [Multi-Product Ad](/docs/reference/ads-api/multi-product-ads).

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X GET \
  -d 'creative={
       "object_story_id": "<PAGE_ID>_<POST_ID>"
     }' \
  -d 'ad_format="DESKTOP_FEED_STANDARD"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7D%26ad_format=DESKTOP_FEED_STANDARD&version=v23.0)

Give Feedback

  

Create an app ad preview using `object_story_spec`. This is the only way to generate a preview for an app ad.

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X GET \
  -d 'creative={
       "object_story_spec": {
         "link_data": {
           "call_to_action": {
             "type": "USE_APP",
             "value": {
               "link": "<URL>"
             }
           },
           "description": "Description",
           "link": "<URL>",
           "message": "Message",
           "name": "Name",
           "picture": "<IMAGE_URL>"
         },
         "page_id": "<PAGE_ID>"
       }
     }' \
  -d 'ad_format="MOBILE_FEED_STANDARD"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%7B%22object_story_spec%22%3A%7B%22link_data%22%3A%7B%22call_to_action%22%3A%7B%22type%22%3A%22USE_APP%22%2C%22value%22%3A%7B%22link%22%3A%22%3CURL%3E%22%7D%7D%2C%22description%22%3A%22Description%22%2C%22link%22%3A%22%3CURL%3E%22%2C%22message%22%3A%22Message%22%2C%22name%22%3A%22Name%22%2C%22picture%22%3A%22%3CIMAGE_URL%3E%22%7D%2C%22page_id%22%3A%22%3CPAGE_ID%3E%22%7D%7D%26ad_format=MOBILE_FEED_STANDARD&version=v23.0)

Give Feedback

  
  

Instagram Explore home Ad Previews using INSTAGRAM\_EXPLORE\_GRID\_HOME ad format

```
curl \-X GET \\
  \-d 'ad\_format="INSTAGRAM\_EXPLORE\_GRID\_HOME"' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/<AD\_ID>/previews
```

This returns something like this:

```
{
  "data": \[
    {
      "body": "<iframe src=\\"https://www.facebook.com/ads/api/preview\_iframe.php?d=AQKuwYcWpyFgVKLORPWQi52\_uTud4v8PpMoDtyBfntL65i0iFtgkiXWN5S4JMBhq-UMKQmvxXFexVxu-5l5Xbf4WWRP48sCAtn3ArQAXwbdrD5qH0EL2z34K-gAgYyENd80cOGAdhVreKGJZvPkLbjDS3iDkdqdNNJQ6yaAFTmUpaz\_\_cjgmhVVCUW68wU3UOZwqlv376mkijYR57Sm2OlyES4U6ivMPNGDx4xnZEd5d8kWyagDD-lPbCaGEk0nnQF5mnyeV9pFqdByhq-IqN6n0ZhSWjCPXZQa84wu5GNQ70YR2w7QxEYoiWCgI2WP0Z2OPeUMiNOf9bhYB-TBZJZ7G6HylsOnzzII9FQ8-0K-b\_Q&t=AQJws9t-TtIGrKoFtCM\\" width=\\"274\\" height=\\"213\\" scrolling=\\"yes\\" style=\\"border: none;\\"></iframe>"
    }
  \]
}
```

Instagram search results Ad Previews using INSTAGRAM\_SEARCH\_CHAIN ad format

```
curl \-X GET \\
  \-d 'ad\_format="INSTAGRAM\_SEARCH\_CHAIN"' \\
  \-d 'access\_token=<ACCESS\_TOKEN>' \\
  https://graph.facebook.com/`v23.0`/<AD\_ID>/previews
```

This returns something like this:

```
{
  "data": \[
    {
      "body": "<iframe src=\\"https://www.facebook.com/ads/api/preview\_iframe.php?d=AQKVMPdwuorP3mliXRaOi0TCSvsGRfucEzBTnB4jghArle84f8kBjvJmX3gmdjniUjohKA3GUppDZqljStZwxxRRxkQl9Y4R1o5wV4zRGE3xO3NHf1\_qBbFM\_uEIGAnAvptMWo\_DLpbiIqIYFMjxbXNELzmZQsR0gnbBjaXM9i6gkI29dnHPqnm4xGvPxo2w8RWeXfWvmik2C96\_2PrhrRhh4NKL3SOmFC9JDVsTp9Z6SYDlLVcLJWwpRKmciAZqEMOnMEFgepVTZ39yJ4ZiAMRo76RK9XNVGcornsUBtxI8cZHKtdW7nmj3ivq09\_NGGUnFiJdJaPm-Mk-obM3K0QyOvgHKwnmLn7wvMiizJeXPEWAcSBa4DPUFLAO1mSuaKla0VQ6tzAM4BqFU9LJOG1-zZmPec7wKxQGDcrXoCOKfv2xkLyzECc-oDS0JJgvxxlo&t=AQI8ECKvkemIoVDaDrs\\" width=\\"274\\" height=\\"213\\" scrolling=\\"yes\\" style=\\"border: none;\\"></iframe>"
    }
  \],
  "\_\_www\_request\_id\_\_": "AzCC2RoeSL0rMbSPTYDyDHa"
}
```