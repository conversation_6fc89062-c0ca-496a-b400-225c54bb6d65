{"title": "Ad Account Product Audiences", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_n_ag\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Ad Account Product Audiences</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_8D\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_Jm\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_/y\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_wx\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Ad Account Product Audiences</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>product_audiences</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/product_audiences/\"><code>/act_{ad_account_id}/product_audiences</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> will be created.</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_2/\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_12\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_y+\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_2E\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_Wy\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_u1\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_b_xj\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fproduct_audiences%3Fname%3DTest%2BIphone%2BProduct%2BAudience%26product_set_id%3D%253CPRODUCT_SET_ID%253E%26inclusions%3D%255B%257B%2522retention_seconds%2522%253A86400%252C%2522rule%2522%253A%257B%2522and%2522%253A%255B%257B%2522event%2522%253A%257B%2522eq%2522%253A%2522AddToCart%2522%257D%257D%252C%257B%2522userAgent%2522%253A%257B%2522i_contains%2522%253A%2522iPhone%2522%257D%257D%255D%257D%257D%255D%26exclusions%3D%255B%257B%2522retention_seconds%2522%253A172800%252C%2522rule%2522%253A%257B%2522event%2522%253A%257B%2522eq%2522%253A%2522Purchase%2522%257D%257D%257D%255D&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_c_lx\" style=\"\"><code><span class=\"pln\">POST </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/product_audiences HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com\n\nname</span><span class=\"pun\">=</span><span class=\"typ\">Test</span><span class=\"pun\">+</span><span class=\"typ\">Iphone</span><span class=\"pun\">+</span><span class=\"typ\">Product</span><span class=\"pun\">+</span><span class=\"typ\">Audience</span><span class=\"pun\">&amp;</span><span class=\"pln\">product_set_id</span><span class=\"pun\">=%</span><span class=\"lit\">3CPRODUCT</span><span class=\"pln\">_SET_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">&amp;</span><span class=\"pln\">inclusions</span><span class=\"pun\">=%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22retention</span><span class=\"pln\">_seconds</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A86400</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22rule</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22and</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22event</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22eq</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22AddToCart</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22userAgent</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22i</span><span class=\"pln\">_contains</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22iPhone</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">&amp;</span><span class=\"pln\">exclusions</span><span class=\"pun\">=%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22retention</span><span class=\"pln\">_seconds</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A172800</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22rule</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22event</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22eq</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22Purchase</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_d_7b\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"pln\">post</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences'</span><span class=\"pun\">,</span><span class=\"pln\">\n    array </span><span class=\"pun\">(</span><span class=\"pln\">\n      </span><span class=\"str\">'name'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'Test Iphone Product Audience'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'product_set_id'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'&lt;PRODUCT_SET_ID&gt;'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'inclusions'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'[{\"retention_seconds\":86400,\"rule\":{\"and\":[{\"event\":{\"eq\":\"AddToCart\"}},{\"userAgent\":{\"i_contains\":\"iPhone\"}}]}}]'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'exclusions'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'[{\"retention_seconds\":172800,\"rule\":{\"event\":{\"eq\":\"Purchase\"}}}]'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">),</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_e_mE\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"POST\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"Test Iphone Product Audience\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"product_set_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;PRODUCT_SET_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"inclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":86400,\\\"rule\\\":{\\\"and\\\":[{\\\"event\\\":{\\\"eq\\\":\\\"AddToCart\\\"}},{\\\"userAgent\\\":{\\\"i_contains\\\":\\\"iPhone\\\"}}]}}]\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"exclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":172800,\\\"rule\\\":{\\\"event\\\":{\\\"eq\\\":\\\"Purchase\\\"}}}]\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_f_jf\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"name\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"Test Iphone Product Audience\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"product_set_id\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;PRODUCT_SET_ID&gt;\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"inclusions\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":86400,\\\"rule\\\":{\\\"and\\\":[{\\\"event\\\":{\\\"eq\\\":\\\"AddToCart\\\"}},{\\\"userAgent\\\":{\\\"i_contains\\\":\\\"iPhone\\\"}}]}}]\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"exclusions\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":172800,\\\"rule\\\":{\\\"event\\\":{\\\"eq\\\":\\\"Purchase\\\"}}}]\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">POST</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_g_YT\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"Test Iphone Product Audience\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"product_set_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"&lt;PRODUCT_SET_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"inclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"[{\\\"retention_seconds\\\":86400,\\\"rule\\\":{\\\"and\\\":[{\\\"event\\\":{\\\"eq\\\":\\\"AddToCart\\\"}},{\\\"userAgent\\\":{\\\"i_contains\\\":\\\"iPhone\\\"}}]}}]\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"exclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"[{\\\"retention_seconds\\\":172800,\\\"rule\\\":{\\\"event\\\":{\\\"eq\\\":\\\"Purchase\\\"}}}]\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"POST\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_h_5e\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"Test Iphone Product Audience\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'product_set_id=\"&lt;PRODUCT_SET_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'inclusions=[\n       {\n         \"retention_seconds\": 86400,\n         \"rule\": {\n           \"and\": [\n             {\n               \"event\": {\n                 \"eq\": \"AddToCart\"\n               }\n             },\n             {\n               \"userAgent\": {\n                 \"i_contains\": \"iPhone\"\n               }\n             }\n           ]\n         }\n       }\n     ]'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'exclusions=[\n       {\n         \"retention_seconds\": 172800,\n         \"rule\": {\n           \"event\": {\n             \"eq\": \"Purchase\"\n           }\n         }\n       }\n     ]'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_i_Au\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>associated_audience_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>creation_params</code></span></div><div class=\"_yb\">dictionary { string : &lt;string&gt; }</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>description</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>enable_fetch_or_create</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>enable_fetch_or_create</p>\n</div></div><p></p></td></tr><tr class=\"row_4 _5m27\"><td><div class=\"_yc\"><span><code>event_sources</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>event_sources</p>\n</div></div><p></p></td></tr><tr class=\"row_4-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_4-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {APP, OFFLINE_EVENTS, PAGE, PIXEL}</div></td><td><p class=\"_yd\"></p><div><div><p>type</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_5 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>exclusions</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_5-0 _5m29 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>booking_window</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-0-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-0-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>count</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-3 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-4 _5m29 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-4-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_5-4-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_5-5 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-6 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-7 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>rule</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-8 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6 _5m27\"><td><div class=\"_yc\"><span><code>inclusions</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_6-0 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>booking_window</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-0-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-0-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>count</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-3 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-4 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-4-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_6-4-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_6-5 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-7 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>rule</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-8 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>opt_out_link</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>parent_audience_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>subtype</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div><div class=\"_uoj\"><code>message</code>: string, </div>}</div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/ad-account/product_audiences", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/product_audiences/", "timestamp": "2025-06-25T15:31:45.245Z"}