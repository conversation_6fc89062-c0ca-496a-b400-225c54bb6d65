{"title": "Ad Preview Plugin", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Marketing API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_tB\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_fH\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_GG\"></div></span></div></div>\n\n<h1 id=\"ad-preview-plugin\">Ad Preview Plugin</h1>\n\n<p>The Ad Preview plugin is the easiest way for advertisers to preview ads on their own websites.</p>\n\n<p>The plugin enables you to generate Right Hand Column, Feed, or Mobile previews of an ad by specifying a Creative Spec, Adgroup ID or Creative ID. Previews can either be generated using a Social Plugin or through the Graph API.</p>\n\n\n<h2 id=\"params\">Parameters</h2>\n\n<ul>\n<li>Required: One of <code>creative</code>, <code>creative_id</code>, or <code>adgroup_id</code></li>\n<li>Required: <code>ad_format</code>, which replaces <code>page_type</code> parameter</li>\n<li>Optional: <code>ad_account_id</code>, <code>targeting</code>, <code>post</code></li>\n</ul>\n\n<p>The preview plugin requires you to be logged in with Facebook Login. If <code>creative_id</code>, <code>adgroup_id</code>, or <code>ad_account_id</code> is used, you must also have the permissions to access the Creative, Ad Group, or Ad Account respectively.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nSetting\n</th><th>\nHTML5 Attribute\n</th><th>\nDescription\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_5_nf\"><tr class=\"row_0\"><td><p><code>ad_account_id</code></p>\n</td><td><p><code>data-ad-account-id</code></p>\n</td><td><p>Required when specifying a creative that uses <code>image_hash</code></p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><code>adgroup_id</code></p>\n</td><td><p><code>data-adgroup-id</code></p>\n</td><td><p>Adgroup ID returned from a Graph API call.</p>\n</td></tr><tr class=\"row_2\"><td><p><code>creative</code></p>\n</td><td><p><code>data-creative</code></p>\n</td><td><p>JSON-encoded <a href=\"/docs/reference/ads-api/adcreative/\">creative spec</a>.</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p><code>creative_id</code></p>\n</td><td><p><code>data-creative-id</code></p>\n</td><td><p>Creative ID returned from a Graph API call.</p>\n</td></tr><tr class=\"row_4\"><td><p><code>ad_format</code></p>\n</td><td><p><code>data-ad-format</code></p>\n</td><td><p>One of: <code>RIGHT_COLUMN_STANDARD</code>, <code>DESKTOP_FEED_STANDARD</code>, <code>MOBILE_FEED_STANDARD</code>, or <code>FACEBOOK_STORY_MOBILE</code>.</p>\n</td></tr><tr class=\"row_5 _5m29\"><td><p><code>page_type</code></p>\n</td><td><p><code>data-page-type</code></p>\n</td><td><p>One of: <code>rightcolumn</code>, <code>desktopfeed</code>, or <code>mobile</code>.</p>\n</td></tr><tr class=\"row_6\"><td><p><code>targeting</code></p>\n</td><td><p><code>data-targeting</code></p>\n</td><td><p>JSON-encoded <a href=\"/docs/ads-api/targeting\">targeting spec</a>.</p>\n</td></tr><tr class=\"row_7 _5m29\"><td><p><code>post</code></p>\n</td><td><p><code>data-post</code></p>\n</td><td><p>JSON-encoded post specification according to the <a href=\"/docs/graph-api/reference/page\">Pages API documentation</a>.</p>\n</td></tr></tbody></table></div>\n\n<h2 id=\"graphapi\">Graph API</h2>\n\n<p>Previews can also be generated using the <a href=\"/docs/reference/ads-api/generatepreview/\">Graph API</a>. To generate a plugin-style preview, simply specify the additional parameter, <code>ad_format</code>, as described in the table above.</p>\n\n\n", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog"], "url": "https://developers.facebook.com/docs/marketing-api/ad-preview-plugin/v23.0", "timestamp": "2025-06-25T15:43:21.779Z"}