# Facebook Marketing API - System User Reference

## Summary
Complete reference documentation for the System User endpoint in Facebook Marketing API v23.0. Covers reading system user data, creating new system users, and managing system user permissions within Business Manager.

## Key Points
- System users are automated accounts for managing business assets via API
- Reading system users requires only the system-user-id in the URL path
- Creating system users requires the app to be part of the target business
- System users have various permission roles for different business functions
- Business Manager has limits on total and admin system users

## API Endpoints
- `GET /v23.0/{system-user-id}`
- `POST /{business_id}/system_users`

## Parameters
- system-user-id
- name
- role
- system_user_id
- business_id

## Content
# System User

## Overview

Represents a system user in Facebook's Marketing API. System users are automated accounts that can be used to manage business assets and perform API operations on behalf of a business.

## Reading System Users

### Endpoint
```
GET /v23.0/{system-user-id}
```

### Parameters
This endpoint doesn't require any parameters.

### Fields

| Field | Type | Description | Default |
|-------|------|-------------|----------|
| `id` | numeric string | System user ID | ✓ |
| `created_by` | User | The creator of this system user | |
| `created_time` | datetime | The creation time of this system user | |
| `finance_permission` | string | Financial permission role (Editor, Analyst, etc.) | |
| `ip_permission` | string | Ads right permission role (Reviewer, etc.) | |
| `name` | string | Name used to identify this system user | ✓ |

### Edges

| Edge | Type | Description |
|------|------|-------------|
| `assigned_business_asset_groups` | Edge<BusinessAssetGroup> | Business asset groups assigned to this user |
| `assigned_pages` | Edge<Page> | Pages assigned to this user |
| `assigned_product_catalogs` | Edge<ProductCatalog> | Product catalogs assigned to this user |

## Creating System Users

### Endpoint
```
POST /{business_id}/system_users
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `name` | string | ✓ | Name of system user to be added |
| `role` | enum | | Role of system user (see roles below) |
| `system_user_id` | int | | ID of system user |

### Available Roles
- `FINANCE_EDITOR`
- `FINANCE_ANALYST` 
- `ADS_RIGHTS_REVIEWER`
- `ADMIN`
- `EMPLOYEE`
- `DEVELOPER`
- `PARTNER_CENTER_ADMIN`
- `PARTNER_CENTER_ANALYST`
- `PARTNER_CENTER_OPERATIONS`
- `PARTNER_CENTER_MARKETING`
- `PARTNER_CENTER_EDUCATION`
- `MANAGE`
- `DEFAULT`
- `FINANCE_EDIT`
- `FINANCE_VIEW`

### Return Type
Returns a struct with the created system user ID:
```json
{
  "id": "numeric string"
}
```

## Error Codes

### Reading Errors
- `100`: Invalid parameter
- `110`: Invalid user id

### Creating Errors
- `104001`: App must be part of business to create system user
- `3965`: Maximum admin system user limit reached
- `3949`: Maximum system user limit reached
- `100`: Invalid parameter
- `102`: Session key invalid or no longer valid

## Limitations
- **Updating**: Not supported on this endpoint
- **Deleting**: Not supported on this endpoint

## Examples
GET /v23.0/{system-user-id} HTTP/1.1
Host: graph.facebook.com

---
**Tags:** Facebook Marketing API, System User, Business Manager, API Reference, Graph API, Authentication
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/system-user
**Processed:** 2025-06-25T15:47:50.468Z