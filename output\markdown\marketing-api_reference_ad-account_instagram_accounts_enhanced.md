# Facebook Marketing API - Ad Account Instagram Accounts Reference

## Summary
This reference page documents the Ad Account Instagram Accounts endpoint, which allows you to retrieve Instagram accounts associated with a specific Ad Account. The endpoint only supports read operations and returns a list of IGUser nodes with pagination support.

## Key Points
- Only supports GET operations to retrieve Instagram accounts associated with an Ad Account
- Returns a list of IGUser nodes with pagination support
- No parameters required for the basic request
- Subject to rate limiting for both Instagram accounts and ad accounts
- Create, update, and delete operations are not supported on this endpoint

## API Endpoints
- `GET /v23.0/{ad-account-id}/instagram_accounts`

## Parameters
- ad-account-id (path parameter)
- summary (optional query parameter for aggregated data)
- access-token (required for authentication)

## Content
# Ad Account Instagram Accounts

## Overview

The Ad Account Instagram Accounts endpoint allows you to retrieve Instagram accounts that are associated with a specific Ad Account in the Facebook Marketing API.

## Reading

Retrieve Instagram accounts associated with this Ad Account.

### Endpoint

```
GET /v23.0/{ad-account-id}/instagram_accounts
```

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/instagram_accounts HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/instagram_accounts',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/instagram_accounts",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/instagram_accounts",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/instagram_accounts"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Parameters

This endpoint doesn't have any parameters.

### Response Format

Reading from this edge will return a JSON formatted result:

```json
{
    "data": [],
    "paging": {},
    "summary": {}
}
```

#### Response Fields

- **data**: A list of IGUser nodes representing Instagram accounts
- **paging**: Pagination information for navigating through results
- **summary**: Aggregated information about the edge, such as counts

#### Summary Fields

| Field | Type | Description |
|-------|------|-------------|
| `total_count` | int32 | Total number of objects on this edge |

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 190 | Invalid OAuth 2.0 Access Token |
| 100 | Invalid parameter |
| 80002 | Too many calls to this Instagram account. Rate limiting applied. |
| 80004 | Too many calls to this ad-account. Rate limiting applied. |

## Supported Operations

- **Creating**: Not supported
- **Updating**: Not supported  
- **Deleting**: Not supported

This endpoint only supports read operations to retrieve associated Instagram accounts.

## Examples
HTTP GET request example

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest implementation

iOS SDK FBSDKGraphRequest implementation

---
**Tags:** Facebook Marketing API, Instagram Accounts, Ad Account, Graph API, IGUser, Social Media API
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/instagram_accounts/
**Processed:** 2025-06-25T15:30:42.347Z