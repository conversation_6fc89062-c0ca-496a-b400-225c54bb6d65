const config = {
  baseUrl: 'https://developers.facebook.com',
  
  // Main documentation URLs to scrape
  urls: [
    '/docs/marketing-apis',
    '/docs/marketing-apis/overview',
    '/docs/marketing-api/get-started',
    '/docs/marketing-api/creative',
    '/docs/marketing-api/bidding',
    '/docs/marketing-api/ad-rules',
    '/docs/marketing-api/audiences',
    '/docs/marketing-api/insights',
    '/docs/marketing-api/brand-safety-and-suitability',
    '/docs/marketing-api/best-practices',
    '/docs/marketing-api/troubleshooting',
    '/docs/marketing-api/reference',
    '/docs/marketing-api/reference/ad-account',
    '/docs/marketing-api/reference/ad-account/account_controls/',
    '/docs/marketing-api/reference/ad-account/activities/',
    '/docs/marketing-api/reference/ad-account/ad_place_page_sets/',
    '/docs/marketing-api/reference/ad-account/ad_place_page_sets_async/',
    '/docs/marketing-api/reference/ad-account/adcreatives/',
    '/docs/marketing-api/reference/ad-account/adimages/',
    '/docs/marketing-api/reference/ad-account/adlabels/',
    '/docs/marketing-api/reference/ad-account/adplayables/',
    '/docs/marketing-api/reference/ad-account/adrules_library/',
    '/docs/marketing-api/reference/ad-account/ads/',
    '/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/',
    '/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/',
    '/docs/marketing-api/reference/ad-account/adsets/',
    '/docs/marketing-api/reference/ad-account/adspixels/',
    '/docs/marketing-api/reference/ad-account/advertisable_applications/',
    '/docs/marketing-api/reference/ad-account/advideos/',
    '/docs/marketing-api/reference/ad-account/agencies/',
    '/docs/marketing-api/reference/ad-account/applications/',
    '/docs/marketing-api/reference/ad-account/assigned_users/',
    '/docs/marketing-api/reference/ad-account/async_batch_requests/',
    '/docs/marketing-api/reference/ad-account/asyncadcreatives/',
    '/docs/marketing-api/reference/ad-account/asyncadrequestsets/',
    '/docs/marketing-api/reference/ad-account/broadtargetingcategories/',
    '/docs/marketing-api/reference/ad-account/campaigns/',
    '/docs/marketing-api/reference/ad-account/connected_instagram_accounts/',
    '/docs/marketing-api/reference/ad-account/customaudiences/',
    '/docs/marketing-api/reference/ad-account/customaudiencestos/',
    '/docs/marketing-api/reference/ad-account/customconversions/',
    '/docs/marketing-api/reference/ad-account/delivery_estimate/',
    '/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/',
    '/docs/marketing-api/reference/ad-account/dsa_recommendations/',
    '/docs/marketing-api/reference/ad-account/impacting_ad_studies/',
    '/docs/marketing-api/reference/ad-account/insights/',
    '/docs/marketing-api/reference/ad-account/instagram_accounts/',
    '/docs/marketing-api/reference/ad-account/mcmeconversions/',
    '/docs/marketing-api/reference/ad-account/minimum_budgets/',
    '/docs/marketing-api/reference/ad-account/product_audiences/',
    '/docs/marketing-api/reference/ad-account/promote_pages/',
    '/docs/marketing-api/reference/ad-account/publisher_block_lists/',
    '/docs/marketing-api/reference/ad-account/reachestimate/',
    '/docs/marketing-api/reference/ad-account/reachfrequencypredictions/',
    '/docs/marketing-api/reference/ad-account/saved_audiences/',
    '/docs/marketing-api/reference/ad-account/subscribed_apps/',
    '/docs/marketing-api/reference/ad-account/targetingbrowse/',
    '/docs/marketing-api/reference/ad-account/targetingsearch/',
    '/docs/marketing-api/reference/ad-account/tracking/',
    '/docs/marketing-api/adcreative',
    '/docs/marketing-api/reference/ad-image',
    '/docs/marketing-api/generatepreview',
    '/docs/marketing-api/ad-preview-plugin',
    '/docs/marketing-api/reference/business',
    '/docs/marketing-api/reference/business-role-request',
    '/docs/marketing-api/reference/business-user',
    '/docs/marketing-api/currencies',
    '/docs/marketing-api/reference/high-demand-period',
    '/docs/marketing-api/image-crops',
    '/docs/marketing-api/reference/product-catalog',
    '/docs/marketing-api/reference/system-user',
    '/docs/marketing-api/marketing-api-changelog'
  ],

  // Scraper settings
  settings: {
    outputDir: './output',
    delay: 1000, // Delay between requests in milliseconds
    timeout: 30000, // Page timeout in milliseconds
    retries: 3, // Number of retries for failed requests
    headless: true, // Run browser in headless mode
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  },

  // OpenRouter AI processing settings
  openrouter: {
    apiKey: process.env.OPENROUTER_KEY || '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',
    model: 'anthropic/claude-sonnet-4',
    maxTokens: 8000,
    temperature: 0.1,
    requestDelay: 3000, // 3 seconds between AI requests (Claude Sonnet 4 may have stricter limits)
    enabled: true, // Set to false to disable AI processing
    batchSize: 1 // Process pages one at a time to avoid rate limits
  },

  // Content selectors for extracting main content
  selectors: {
    mainContent: '._4-u2 ._4-u3, [data-click-area="main"], ._588p, main, [role="main"], .content, #content, .documentation-content',
    navigation: 'nav, .nav, .navigation, ._3wm1, ._1dyy',
    title: 'h1, .title, .page-title',
    breadcrumbs: '.breadcrumb, .breadcrumbs, nav[aria-label="breadcrumb"]',
    excludeElements: 'script, style, noscript, .hidden_elem, ._2k32, [data-click-area="to_top_nav"]'
  }
};

module.exports = config;
