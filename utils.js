const fs = require('fs-extra');
const path = require('path');
const TurndownService = require('turndown');
const sanitize = require('sanitize-filename');

class ScraperUtils {
  constructor() {
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced'
    });
    
    // Configure turndown to preserve code blocks and tables
    this.turndownService.addRule('preserveCodeBlocks', {
      filter: ['pre', 'code'],
      replacement: function (content, node) {
        if (node.nodeName === 'PRE') {
          return '\n```\n' + content + '\n```\n';
        }
        return '`' + content + '`';
      }
    });
  }

  /**
   * Create output directory structure
   */
  async createOutputStructure(outputDir) {
    await fs.ensureDir(outputDir);
    await fs.ensureDir(path.join(outputDir, 'html'));
    await fs.ensureDir(path.join(outputDir, 'markdown'));
    await fs.ensureDir(path.join(outputDir, 'json'));
  }

  /**
   * Generate safe filename from URL
   */
  generateFilename(url) {
    let filename = url.replace(/^\/docs\//, '').replace(/\/$/, '');
    filename = filename.replace(/\//g, '_');
    filename = sanitize(filename);
    return filename || 'index';
  }

  /**
   * Extract main content from page
   */
  async extractContent(page, selectors) {
    try {
      // Wait for content to load
      await page.waitForLoadState('networkidle');
      
      // Extract title
      const title = await this.extractTitle(page, selectors);
      
      // Extract breadcrumbs
      const breadcrumbs = await this.extractBreadcrumbs(page, selectors);
      
      // Extract main content
      const content = await this.extractMainContent(page, selectors);
      
      // Extract navigation links for discovery
      const navigationLinks = await this.extractNavigationLinks(page, selectors);
      
      return {
        title,
        breadcrumbs,
        content,
        navigationLinks,
        url: page.url(),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error extracting content from ${page.url()}:`, error.message);
      return null;
    }
  }

  /**
   * Extract page title
   */
  async extractTitle(page, selectors) {
    try {
      const titleElement = await page.locator(selectors.title).first();
      return await titleElement.textContent() || '';
    } catch {
      return await page.title() || '';
    }
  }

  /**
   * Extract breadcrumbs
   */
  async extractBreadcrumbs(page, selectors) {
    try {
      const breadcrumbElements = await page.locator(selectors.breadcrumbs + ' a, ' + selectors.breadcrumbs + ' span').all();
      const breadcrumbs = [];
      
      for (const element of breadcrumbElements) {
        const text = await element.textContent();
        const href = await element.getAttribute('href');
        if (text && text.trim()) {
          breadcrumbs.push({ text: text.trim(), href });
        }
      }
      
      return breadcrumbs;
    } catch {
      return [];
    }
  }

  /**
   * Extract main content
   */
  async extractMainContent(page, selectors) {
    try {
      // Try multiple selectors to find main content
      for (const selector of selectors.mainContent.split(', ')) {
        try {
          const element = await page.locator(selector).first();
          if (await element.count() > 0) {
            return await element.innerHTML();
          }
        } catch {
          continue;
        }
      }
      
      // Fallback: get body content and remove navigation
      const bodyContent = await page.locator('body').innerHTML();
      return bodyContent;
    } catch (error) {
      console.error('Error extracting main content:', error.message);
      return '';
    }
  }

  /**
   * Extract navigation links for URL discovery
   */
  async extractNavigationLinks(page, selectors) {
    try {
      const links = await page.locator('a[href*="/docs/marketing-api"]').all();
      const navigationLinks = [];
      
      for (const link of links) {
        const href = await link.getAttribute('href');
        const text = await link.textContent();
        
        if (href && href.startsWith('/docs/marketing-api')) {
          navigationLinks.push({
            href: href,
            text: text ? text.trim() : ''
          });
        }
      }
      
      return [...new Set(navigationLinks.map(link => link.href))]; // Remove duplicates
    } catch {
      return [];
    }
  }

  /**
   * Convert HTML to Markdown
   */
  htmlToMarkdown(html) {
    try {
      return this.turndownService.turndown(html);
    } catch (error) {
      console.error('Error converting HTML to Markdown:', error.message);
      return html;
    }
  }

  /**
   * Save content in multiple formats
   */
  async saveContent(content, filename, outputDir) {
    try {
      // Save as JSON
      await fs.writeFile(
        path.join(outputDir, 'json', `${filename}.json`),
        JSON.stringify(content, null, 2),
        'utf8'
      );

      // Save HTML content
      if (content.content) {
        await fs.writeFile(
          path.join(outputDir, 'html', `${filename}.html`),
          content.content,
          'utf8'
        );

        // Save as Markdown
        const markdown = this.htmlToMarkdown(content.content);
        const markdownContent = `# ${content.title}\n\n${markdown}`;
        
        await fs.writeFile(
          path.join(outputDir, 'markdown', `${filename}.md`),
          markdownContent,
          'utf8'
        );
      }

      return true;
    } catch (error) {
      console.error(`Error saving content for ${filename}:`, error.message);
      return false;
    }
  }

  /**
   * Load or create progress file
   */
  async loadProgress(outputDir) {
    const progressFile = path.join(outputDir, 'progress.json');
    try {
      if (await fs.pathExists(progressFile)) {
        return await fs.readJson(progressFile);
      }
    } catch (error) {
      console.error('Error loading progress:', error.message);
    }
    
    return {
      completed: [],
      failed: [],
      discovered: [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Save progress
   */
  async saveProgress(progress, outputDir) {
    const progressFile = path.join(outputDir, 'progress.json');
    progress.lastUpdated = new Date().toISOString();
    
    try {
      await fs.writeJson(progressFile, progress, { spaces: 2 });
    } catch (error) {
      console.error('Error saving progress:', error.message);
    }
  }

  /**
   * Create manifest of all scraped content
   */
  async createManifest(outputDir) {
    try {
      const jsonDir = path.join(outputDir, 'json');
      const files = await fs.readdir(jsonDir);
      const manifest = {
        totalPages: 0,
        pages: [],
        generatedAt: new Date().toISOString()
      };

      for (const file of files) {
        if (file.endsWith('.json') && file !== 'manifest.json') {
          try {
            const content = await fs.readJson(path.join(jsonDir, file));
            manifest.pages.push({
              filename: file,
              title: content.title,
              url: content.url,
              timestamp: content.timestamp
            });
          } catch (error) {
            console.error(`Error reading ${file}:`, error.message);
          }
        }
      }

      manifest.totalPages = manifest.pages.length;
      await fs.writeJson(path.join(outputDir, 'manifest.json'), manifest, { spaces: 2 });
      
      return manifest;
    } catch (error) {
      console.error('Error creating manifest:', error.message);
      return null;
    }
  }
}

module.exports = ScraperUtils;
