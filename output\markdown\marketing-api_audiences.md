# Audiences

On This Page

[Audiences](#audiences)

[Common Uses](#common-uses)

[Documentation Contents](#documentation-contents)

[Overview](#overview)

[Guides](#guides)

[Reference](#reference)

[Special Ad Category](#special-ad-category)

# Audiences

Audience targeting helps you show your ads to the people you care about. There are two general approaches — specific or broad — you can take when creating a target audience. The approach you choose depends on what you're trying to accomplish and your available resources.

You can be specific and create audiences based on customer data, conversion data such as activity in your app or website, etc. Or, you can provide broader information, such as demographics or location, and we deliver ads to people who meet those attributes.

## Common Uses

*   [**Lookalike Audiences**](/docs/marketing-api/lookalike-audience-targeting) — Target people most like your established customers.
*   **Custom Audiences** — Build your target custom audience with data from [**mobile app**](/docs/marketing-api/audiences-api/mobile-apps) and [**website**](/docs/marketing-api/audiences-api/websites) behavior, [**CRM**](/docs/marketing-api/audiences-api), and [**engagement signals**](/docs/marketing-api/audiences-api/engagement). You can also build audiences from [**offline conversions**](/docs/marketing-api/audiences-api/offline).
*   [**Dynamic Audiences**](/docs/marketing-api/dynamic-product-ads/product-audiences) — Build an audience from mobile app and website signals.
*   [**Targeting Options**](/docs/marketing-api/buying-api/targeting) — Basic targeting includes demographics and events, location, interests, and behaviors. You can also learn about [**advanced targeting**](/docs/marketing-api/targeting-specs).

[](#)

## Documentation Contents

### [Overview](/docs/marketing-api/audiences/overview)

The basics of audiences and targeting

### [Guides](/docs/marketing-api/audiences/guides)

Build audiences with data and learn more about our broad targeting options

### [Reference](/docs/marketing-api/audiences/reference)

Explore our basic and advanced targeting options, targeting search, and the Custom Audience Terms of Service contracts

### [Special Ad Category](/docs/audiences/special-ad-category)

Targeting options available for advertisers offering housing, employment, or credit opportunities

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)