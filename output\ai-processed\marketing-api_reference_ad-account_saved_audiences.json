{"title": "Facebook Marketing API - Ad Account Saved Audiences Reference", "summary": "Reference documentation for the Facebook Marketing API endpoint to retrieve saved audiences from an ad account. This endpoint supports reading operations only and returns a list of SavedAudience objects with optional filtering and field selection.", "content": "# Ad Account Saved Audiences\n\n## Overview\n\nThis endpoint allows you to retrieve saved audiences associated with a specific ad account in the Facebook Marketing API.\n\n## Reading Saved Audiences\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/saved_audiences\n```\n\n### Example Request\n```http\nGET /v23.0/{ad-account-id}/saved_audiences HTTP/1.1\nHost: graph.facebook.com\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `business_id` | numeric string or integer | Optional parameter to assist with filters such as recently used |\n| `fields` | list<string> | Fields to be retrieved. Default behavior is to return only the IDs |\n| `filtering` | list<Filter Object> | Filters on the report data. This parameter is an array of filter objects |\n\n### Response Format\n\nThe response returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Response Fields\n\n- **`data`**: A list of [SavedAudience](/docs/marketing-api/reference/saved-audience/) nodes\n- **`paging`**: Pagination information (see [Graph API guide](/docs/graph-api/using-graph-api/#paging) for details)\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 80004 | Too many calls to this ad-account. Wait and try again. See [rate limiting documentation](https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management) |\n\n## Supported Operations\n\n- **Reading**: ✅ Supported\n- **Creating**: ❌ Not supported on this endpoint\n- **Updating**: ❌ Not supported on this endpoint\n- **Deleting**: ❌ Not supported on this endpoint\n\n## Additional Resources\n\n- [Using Graph API guide](/docs/graph-api/using-graph-api/)\n- [Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fsaved_audiences&version=v23.0)", "keyPoints": ["This endpoint only supports reading operations for saved audiences", "Responses include pagination support for handling large datasets", "Optional filtering and field selection parameters are available", "Rate limiting applies with specific error code 80004 for too many requests", "Returns SavedAudience objects with configurable field selection"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/saved_audiences"], "parameters": ["business_id", "fields", "filtering", "ad-account-id"], "examples": ["GET /v23.0/{ad-account-id}/saved_audiences HTTP/1.1"], "tags": ["Facebook Marketing API", "Saved Audiences", "Ad Account", "Graph API", "API Reference", "Marketing"], "relatedTopics": ["SavedAudience object reference", "Graph API pagination", "OAuth 2.0 Access Tokens", "Rate limiting", "Filter objects", "Graph API Explorer"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/saved_audiences/", "processedAt": "2025-06-25T15:39:11.870Z", "processor": "openrouter-claude-sonnet-4"}