# Facebook Marketing API - Ad Account Async Batch Requests

## Summary
Documentation for the Ad Account Async Batch Requests endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous batch requests for ad accounts but does not support reading, updating, or deleting operations.

## Key Points
- Only supports POST operations for creating async batch requests
- Requires ad account ID in the endpoint path format /act_{ad_account_id}/async_batch_requests
- All parameters (adbatch, name, relative_url, body) are required
- Returns a numeric string ID upon successful creation
- Supports read-after-write functionality

## API Endpoints
- `/act_{ad_account_id}/async_batch_requests`

## Parameters
- adbatch
- name
- relative_url
- body

## Content
# Ad Account Async Batch Requests

## Overview

The Ad Account Async Batch Requests endpoint allows you to create asynchronous batch requests for ad account operations. This endpoint only supports POST operations for creating batch requests.

## Supported Operations

### Reading
You can't perform this operation on this endpoint.

### Creating
You can make a POST request to the `async_batch_requests` edge from the following path:
- `/act_{ad_account_id}/async_batch_requests`

When posting to this edge, a Campaign will be created.

#### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `adbatch` | list<Object> | JSON encoded batch request | Yes |
| `name` | string | Name of the batch request for tracking purposes | Yes |
| `relative_url` | string | Relative URL for the batch request | Yes |
| `body` | UTF-8 encoded string | Request body content | Yes |

#### Return Type

This endpoint supports read-after-write and will read the node represented by `id` in the return type.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 194 | Missing at least one required parameter |

### Updating
You can't perform this operation on this endpoint.

### Deleting
You can't perform this operation on this endpoint.

---
**Tags:** Facebook Marketing API, Ad Account, Async Batch Requests, POST, Batch Operations  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/async_batch_requests/  
**Processed:** 2025-06-25T16:24:38.829Z