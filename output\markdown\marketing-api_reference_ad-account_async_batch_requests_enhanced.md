# Facebook Marketing API - Ad Account Async Batch Requests

## Summary
Documentation for the Ad Account Async Batch Requests endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous batch requests for ad accounts but does not support reading, updating, or deleting operations.

## Key Points
- Only POST (creating) operations are supported on this endpoint
- Requires JSON encoded batch request data with specific parameters
- Creates Campaign objects when posting to the edge
- Supports read-after-write functionality
- Returns a numeric string ID upon successful creation

## API Endpoints
- `/act_{ad_account_id}/async_batch_requests`

## Parameters
- adbatch (list<Object>, required)
- name (string, required)
- relative_url (string, required)
- body (UTF-8 encoded string, required)

## Content
# Ad Account Async Batch Requests

## Overview

The Ad Account Async Batch Requests endpoint is part of the Facebook Marketing API v23.0 that enables asynchronous batch processing for ad account operations.

## Supported Operations

### Reading
Reading operations are **not supported** on this endpoint.

### Creating
You can make a POST request to the `async_batch_requests` edge from the following path:
- `/act_{ad_account_id}/async_batch_requests`

When posting to this edge, a Campaign will be created.

#### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `adbatch` | list<Object> | JSON encoded batch request | Yes |
| `name` | string | Name of the batch request for tracking purposes | Yes |
| `relative_url` | string | Relative URL for the request | Yes |
| `body` | UTF-8 encoded string | Request body content | Yes |

#### Return Type
This endpoint supports read-after-write and will read the node represented by `id` in the return type.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 194 | Missing at least one required parameter |

### Updating
Updating operations are **not supported** on this endpoint.

### Deleting
Deleting operations are **not supported** on this endpoint.

---
**Tags:** Facebook Marketing API, Ad Account, Async Batch Requests, Campaign Creation, Batch Processing
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/async_batch_requests/
**Processed:** 2025-06-25T15:22:22.881Z