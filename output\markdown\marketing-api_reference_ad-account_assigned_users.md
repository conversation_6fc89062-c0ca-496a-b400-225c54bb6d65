# Ad Account, Assigned Users

On This Page

[Ad Account, Assigned Users](#overview)

[Reading](#Reading)

[Example](#example)

[Parameters](#parameters)

[Fields](#fields)

[Error Codes](#error-codes)

[Creating](#Creating)

[Updating](#Updating)

[Parameters](#parameters-2)

[Return Type](#return-type)

[Error Codes](#error-codes-2)

[Deleting](#Deleting)

[Parameters](#parameters-3)

[Return Type](#return-type-2)

[Error Codes](#error-codes-3)

Graph API Version

[v23.0](#)

# 

Ad Account, Assigned Users

[](#)

## Reading

Business and system users assigned to this Ad Account.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fassigned_users&version=v23.0)

```
`GET /v23.0/{ad-account-id}/assigned_users HTTP/1.1
Host: graph.facebook.com`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->get(
    '/{ad-account-id}/assigned_users',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/{ad-account-id}/assigned_users",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/assigned_users",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/assigned_users"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`business`

numeric string or integer

The business associated with this Ad Account

Required

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {},
    "`summary`": {}
}


```

#### `data`

A list of AssignedUser nodes.

The following fields will be added to each node that is returned:

Field

Description

`permitted_tasks`

list<string>

Tasks that are assignable on this object

`tasks`

list<string>

All unpacked roles/tasks of this particular user on this object

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

#### `summary`

Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like `summary=total_count`).

Field

Description

`total_count`

unsigned int32

Total number of business and system users assigned to this Ad Account

### Error Codes

Error

Description

200

Permissions error

190

Invalid OAuth 2.0 Access Token

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

100

Invalid parameter

368

The action attempted has been deemed abusive or is otherwise disallowed

[](#)

## Creating

You can't perform this operation on this endpoint.

[](#)

## Updating

In v3.1 we introduce the new concept of **task-based permissions** to substitute for the current role-based permission. This affects access to ad accounts managed by Business Manager API and Pages. Role-based access to ad accounts and Pages is still available but will be deprecated in the future. This impacts the following roles and provides the equivalent tasks for ad accounts:

*   Role: `ADMIN`, Tasks: `['MANAGE', 'ADVERTISE', 'ANALYZE'` - Manage all aspects of ad campaigns, reporting, billing and ad account permissions.
    
*   Role: `GENERAL_USER`, Tasks: `['ADVERTISE', 'ANALYZE']` - Create ads using the funding source associated with the ad account. Run reports.
    
*   Role: `GENERAL_USER`, Tasks: `['ANALYZE']` - Run reports.
    

This replaces the following roles in Business Manager API with these tasks:

*   Role: `MANAGER`, Tasks: `['MANAGE', 'CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`
    
*   Role: `CONTENT_CREATOR`, Tasks: `['CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`
    
*   Role: `MODERATOR`, Tasks: `['MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`
    
*   Role: `ADVERTISER`, Tasks: `['ADVERTISE', 'ANALYZE', 'DRAFT']`
    
*   Role: `INSIGHTS_ANALYST`, Tasks: `['ANALYZE', 'DRAFT']`
    
*   Role: `CREATIVE_HUB_MOCKUPS_MANAGER`, Tasks: `['DRAFT']`
    

You can update an [AdAccount](/docs/marketing-api/reference/ad-account/) by making a POST request to [`/act_{ad_account_id}/assigned_users`](/docs/marketing-api/reference/ad-account/assigned_users/).

### Parameters

Parameter

Description

`tasks`

array<enum {MANAGE, ADVERTISE, ANALYZE, DRAFT, AA\_ANALYZE}>

AdAccount permission tasks to assign this user

`user`

UID

Business user id or system user id

Required

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node to which you POSTed.

Struct {

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

2620

Invalid call to update account permissions

368

The action attempted has been deemed abusive or is otherwise disallowed

[](#)

## Deleting

You can dissociate a [User](/docs/graph-api/reference/user/) from an [AdAccount](/docs/marketing-api/reference/ad-account/) by making a DELETE request to [`/act_{ad_account_id}/assigned_users`](/docs/marketing-api/reference/ad-account/assigned_users/).

### Parameters

Parameter

Description

`user`

UID

Business user id or system user id

Required

### Return Type

Struct {

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

3919

There was an unexpected technical issue. Please try again.

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '***************'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)