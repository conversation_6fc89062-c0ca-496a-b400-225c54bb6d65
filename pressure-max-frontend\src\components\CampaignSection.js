import React, { useState, useEffect } from 'react';
import { facebookAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { Target, Plus, DollarSign, Calendar, AlertCircle } from 'lucide-react';

const CAMPAIGN_OBJECTIVES = [
  { value: 'REACH', label: 'Reach' },
  { value: 'TRAFFIC', label: 'Traffic' },
  { value: 'ENGAGEMENT', label: 'Engagement' },
  { value: 'APP_INSTALLS', label: 'App Installs' },
  { value: 'VIDEO_VIEWS', label: 'Video Views' },
  { value: 'LEAD_GENERATION', label: 'Lead Generation' },
  { value: 'MESSAGES', label: 'Messages' },
  { value: 'CONVERSIONS', label: 'Conversions' },
  { value: 'CATALOG_SALES', label: 'Catalog Sales' },
  { value: 'STORE_VISITS', label: 'Store Visits' }
];

const CampaignSection = () => {
  const { isAuthenticated } = useAuth();
  const [campaigns, setCampaigns] = useState([]);
  const [adAccounts, setAdAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState('');

  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  useEffect(() => {
    if (isAuthenticated) {
      loadAdAccounts();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (selectedAccount) {
      loadCampaigns();
    }
  }, [selectedAccount]);

  const loadAdAccounts = async () => {
    try {
      const response = await facebookAPI.getAdAccounts();
      setAdAccounts(response.data || []);
      if (response.data?.length > 0) {
        setSelectedAccount(response.data[0].account_id);
      }
    } catch (error) {
      toast.error('Failed to load ad accounts');
    }
  };

  const loadCampaigns = async () => {
    if (!selectedAccount) return;
    
    try {
      setLoading(true);
      const response = await facebookAPI.getCampaigns(selectedAccount);
      setCampaigns(response.data || []);
    } catch (error) {
      toast.error('Failed to load campaigns');
      setCampaigns([]);
    } finally {
      setLoading(false);
    }
  };

  const onCreateCampaign = async (data) => {
    try {
      setLoading(true);
      const campaignData = {
        name: data.name,
        objective: data.objective,
        status: 'PAUSED', // Always start paused for safety
        specialAdCategories: data.specialAdCategories ? [data.specialAdCategories] : []
      };

      await facebookAPI.createCampaign(selectedAccount, campaignData);
      toast.success('Campaign created successfully!');
      
      // Reset form and reload campaigns
      reset();
      setShowCreateForm(false);
      loadCampaigns();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to create campaign');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="campaign-section">
        <h2>Campaign Management</h2>
        <div className="auth-required">
          <AlertCircle size={20} />
          <p>Please log in to manage campaigns</p>
        </div>
      </div>
    );
  }

  if (adAccounts.length === 0) {
    return (
      <div className="campaign-section">
        <h2>Campaign Management</h2>
        <div className="no-accounts">
          <AlertCircle size={20} />
          <p>No Facebook ad accounts found. Please connect your Facebook account first.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="campaign-section">
      <h2>Campaign Management</h2>
      
      <div className="account-selector">
        <label>Select Ad Account:</label>
        <select 
          value={selectedAccount} 
          onChange={(e) => setSelectedAccount(e.target.value)}
        >
          {adAccounts.map((account) => (
            <option key={account.account_id} value={account.account_id}>
              {account.name} ({account.account_id})
            </option>
          ))}
        </select>
      </div>

      <div className="campaigns-header">
        <h3>
          <Target size={16} />
          Campaigns ({campaigns.length})
        </h3>
        <button 
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="create-btn"
        >
          <Plus size={16} />
          {showCreateForm ? 'Cancel' : 'Create Campaign'}
        </button>
      </div>

      {showCreateForm && (
        <form onSubmit={handleSubmit(onCreateCampaign)} className="create-campaign-form">
          <h4>Create New Campaign</h4>
          
          <div className="form-group">
            <label>Campaign Name:</label>
            <input
              type="text"
              {...register('name', { required: 'Campaign name is required' })}
              placeholder="Enter campaign name"
            />
            {errors.name && <span className="error">{errors.name.message}</span>}
          </div>

          <div className="form-group">
            <label>Objective:</label>
            <select {...register('objective', { required: 'Objective is required' })}>
              <option value="">Select objective</option>
              {CAMPAIGN_OBJECTIVES.map((obj) => (
                <option key={obj.value} value={obj.value}>
                  {obj.label}
                </option>
              ))}
            </select>
            {errors.objective && <span className="error">{errors.objective.message}</span>}
          </div>

          <div className="form-group">
            <label>Special Ad Categories (optional):</label>
            <select {...register('specialAdCategories')}>
              <option value="">None</option>
              <option value="CREDIT">Credit</option>
              <option value="EMPLOYMENT">Employment</option>
              <option value="HOUSING">Housing</option>
              <option value="ISSUES_ELECTIONS_POLITICS">Issues, Elections or Politics</option>
            </select>
          </div>

          <div className="form-actions">
            <button type="submit" disabled={loading} className="submit-btn">
              {loading ? 'Creating...' : 'Create Campaign'}
            </button>
            <button 
              type="button" 
              onClick={() => setShowCreateForm(false)}
              className="cancel-btn"
            >
              Cancel
            </button>
          </div>
        </form>
      )}

      <div className="campaigns-list">
        {loading ? (
          <div className="loading">Loading campaigns...</div>
        ) : campaigns.length > 0 ? (
          campaigns.map((campaign) => (
            <div key={campaign.id} className="campaign-item">
              <div className="campaign-header">
                <h4>{campaign.name}</h4>
                <span className={`campaign-status ${campaign.status?.toLowerCase()}`}>
                  {campaign.status}
                </span>
              </div>
              <div className="campaign-details">
                <div className="detail-item">
                  <Target size={14} />
                  <span>Objective: {campaign.objective}</span>
                </div>
                <div className="detail-item">
                  <Calendar size={14} />
                  <span>Created: {new Date(campaign.created_time).toLocaleDateString()}</span>
                </div>
                {campaign.daily_budget && (
                  <div className="detail-item">
                    <DollarSign size={14} />
                    <span>Daily Budget: ${(campaign.daily_budget / 100).toFixed(2)}</span>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="no-campaigns">
            <p>No campaigns found for this ad account.</p>
            <p>Create your first campaign using the form above.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CampaignSection;
