{"title": "Catalog", "breadcrumbs": [], "content": "<h1 id=\"catalog\">Catalog</h1>\n\n<p>A Facebook catalog is an object (or container) of information about your products and where you can upload your inventory. Learn more about <a href=\"/docs/marketing-api/catalog/overview\">product catalog</a>.</p>\n\n\n<h2 id=\"common-uses\">Common Uses</h2>\n\n<ul>\n<li><strong><a href=\"https://developers.facebook.com/docs/marketing-api/guides/collection\">Collection Ads</a></strong> — Use them in immersive formats. </li>\n<li><strong><a href=\"https://developers.facebook.com/docs/commerce-platform/catalog/\">Commerce</a></strong> — Distribute products in Marketplace.    </li>\n<li><strong><a href=\"https://developers.facebook.com/docs/marketing-api/dynamic-ads\">Advantage+ Catalog Ads</a></strong> — Feature products in different formats to be served dynamically as personalized ads. </li>\n<li><strong>Instagram Shopping</strong> — Feature in Instagram Shopping experiences, such as product tags on Instagram and soon on Instagram Shops.</li>\n<li><strong>WhatsApp</strong> — Feature in conversational commerce in WhatsApp. </li>\n</ul>\n\n\n<h2 id=\"doc_contents\">Documentation Contents</h2>\n<table class=\"uiGrid _51mz _57v1 _5f0n\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"overview\"><a href=\"/docs/marketing-api/catalog/overview\">Overview</a></h3>\n\n<p>Learn more about catalog and its components.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"get-started\"><a href=\"/docs/marketing-api/catalog/getting-started\">Get Started</a></h3>\n\n<p>Learn how to successfully set up a catalog for commerce or Advantage+ catalog ads, and more.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"guides\"><a href=\"/docs/marketing-api/catalog/guides\">Guides</a></h3>\n\n<p>Learn more about the various guides and how to use them in your catalog.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"best-practices\"><a href=\"https://developers.facebook.com/docs/marketing-api/catalog/best-practices\">Best Practices</a></h3>\n\n<p>Tips for using catalog effectively.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"reference\"><a href=\"/docs/marketing-api/catalog/reference\">Reference</a></h3>\n\n<p>Product specifications and endpoint references.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"support\"><a href=\"/docs/marketing-api/catalog/support\">Support</a></h3>\n\n<p>Solutions to common problems and troubleshooting tips.</p>\n</td></tr></tbody></table>\n\n<h2 id=\"see-also\">See Also</h2>\n\n<ul>\n<li><a href=\"/docs/marketing-api/catalog-batch\">Catalog Batch API</a></li>\n</ul>\n\n\n", "navigationLinks": ["/docs/marketing-api/catalog", "/docs/marketing-api/catalog/overview", "/docs/marketing-api/catalog/get-started", "/docs/marketing-api/catalog/guides", "/docs/marketing-api/catalog/reference", "/docs/marketing-api/catalog-batch", "/docs/marketing-api/catalog/best-practices", "/docs/marketing-api/catalog/support", "/docs/marketing-api/catalog/getting-started"], "url": "https://developers.facebook.com/docs/marketing-api/catalog", "timestamp": "2025-06-25T15:50:07.573Z"}