# Facebook Marketing API - Ad Account Targeting Browse Reference

## Summary
Reference documentation for the Ad Account Targetingbrowse endpoint, which provides a unified browse tree as a flat list for audience targeting options. This read-only endpoint allows developers to retrieve targeting categories with optional filtering and pagination.

## Key Points
- Provides a unified browse tree of targeting options as a flat list
- Read-only endpoint - no create, update, or delete operations supported
- Supports filtering by targeting type and regulated categories
- Returns AdAccountTargetingUnified nodes with pagination support
- Subject to rate limiting - error 80004 indicates too many calls

## API Endpoints
- `GET /v23.0/{ad-account-id}/targetingbrowse`

## Parameters
- include_nodes
- limit_type
- regulated_categories
- ad-account-id

## Content
# Ad Account Targetingbrowse

## Overview

The Ad Account Targetingbrowse endpoint provides a unified browse tree as a flat list for audience targeting options. Use the parent key to recreate the tree structure. This is a read-only endpoint that supports filtering by targeting type and regulated categories.

**Graph API Version:** v23.0

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/targetingbrowse
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `include_nodes` | boolean | Include searchable nodes (e.g., work/edu entries). Default: false. Internal use only. |
| `limit_type` | enum | Limit the type of audience to retrieve. Options include: interests, education_schools, education_majors, work_positions, work_employers, relationship_statuses, interested_in, user_adclusters, college_years, education_statuses, family_statuses, industries, life_events, politics, behaviors, income, net_worth, home_type, home_ownership, home_value, ethnic_affinity, generation, household_composition, moms, office_type, location_categories |
| `regulated_categories` | array<enum> | The regulated categories of the campaign. Options: NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES |

### Response Format

The endpoint returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {}
}
```

- **data**: A list of AdAccountTargetingUnified nodes
- **paging**: Pagination information (see Graph API guide for details)

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 80004 | Too many calls to this ad-account. Wait and try again. |
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |

## Limitations

- **Creating**: Not supported
- **Updating**: Not supported  
- **Deleting**: Not supported

This is a read-only endpoint for browsing available targeting options.

## Examples
GET /v23.0/{ad-account-id}/targetingbrowse HTTP/1.1
Host: graph.facebook.com

---
**Tags:** Facebook Marketing API, Ad Account, Targeting, Audience, Browse, Graph API, Reference
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/targetingbrowse/
**Processed:** 2025-06-25T15:40:03.857Z