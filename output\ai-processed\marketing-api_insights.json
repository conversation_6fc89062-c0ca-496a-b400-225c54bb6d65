{"title": "Facebook Marketing API Insights - Comprehensive Guide to Ad Statistics", "summary": "The Facebook Marketing API Insights provides a unified interface for retrieving ad performance statistics across campaigns, ad sets, and ads. This guide covers making API calls, handling responses, attribution windows, field expansion, sorting, and troubleshooting common issues.", "content": "# Facebook Marketing API Insights\n\nThe Insights API provides a single, consistent interface to retrieve ad statistics across all Facebook advertising objects.\n\n## Prerequisites\n\nBefore you begin, you will need:\n- The `ads_read` permission\n- A Meta app (see [Meta App Development](/docs/development))\n- Proper tracking setup using URL Tags, Meta Pixel, or Conversions API\n\n## Core Concepts\n\n### Available Endpoints\n\nThe Insights API is available as an edge on any ads object:\n\n- `act_<AD_ACCOUNT_ID>/insights` - Account level insights\n- `<CAMPAIGN_ID>/insights` - Campaign level insights  \n- `<ADSET_ID>/insights` - Ad set level insights\n- `<AD_ID>/insights` - Ad level insights\n\n### Basic Request Structure\n\n```bash\ncurl -G \\\n  -d \"fields=impressions\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/<AD_ID>/insights\"\n```\n\n### Response Format\n\n```json\n{\n  \"data\": [\n    {\n      \"impressions\": \"2466376\",\n      \"date_start\": \"2009-03-28\",\n      \"date_stop\": \"2016-04-01\"\n    }\n  ],\n  \"paging\": {\n    \"cursors\": {\n      \"before\": \"MAZDZD\",\n      \"after\": \"MAZDZD\"\n    }\n  }\n}\n```\n\n## Advanced Features\n\n### Aggregation Levels\n\nAggregate results at different object levels to automatically deduplicate data:\n\n```bash\ncurl -G \\\n  -d \"level=ad\" \\\n  -d \"fields=impressions,ad_id\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/CAMPAIGN_ID/insights\"\n```\n\n### Attribution Windows\n\nControl conversion attribution timeframes (1-day, 7-day windows):\n\n```bash\nact_10151816772662695/insights?action_attribution_windows=['1d_click','1d_view']\n```\n\nResponse includes attribution data:\n```json\n{\n  \"actions\": [\n    {\n      \"action_type\": \"link_click\",\n      \"value\": 6608,\n      \"1d_view\": 86,\n      \"1d_click\": 6510\n    }\n  ]\n}\n```\n\n### Field Expansion\n\nRequest insights as part of other object queries:\n\n```bash\ncurl -G \\\n  -d \"fields=insights{impressions}\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/AD_ID\"\n```\n\n### Sorting Results\n\nSort by any field in ascending or descending order:\n\n```bash\ncurl -G \\\n  -d \"sort=reach_descending\" \\\n  -d \"level=ad\" \\\n  -d \"fields=reach\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/AD_SET_ID/insights\"\n```\n\n### Filtering by Ad Labels\n\nGet insights for ads with specific labels:\n\n```bash\ncurl -G \\\n  -d \"fields=id,name,insights{unique_clicks,cpm,total_actions}\" \\\n  -d \"level=ad\" \\\n  -d 'filtering=[{\"field\":\"ad.adlabels\",\"operator\":\"ANY\", \"value\":[\"Label Name\"]}]' \\\n  -d 'time_range={\"since\":\"2015-03-01\",\"until\":\"2015-03-31\"}' \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/AD_OBJECT_ID/insights\"\n```\n\n## Click Metrics Definitions\n\n- **Link Clicks** (`actions:link_click`) - Clicks on ad links to destinations on or off Meta properties\n- **Clicks (All)** (`clicks`) - All types of clicks including ad container interactions and expanded experiences\n\n## Handling Deleted and Archived Objects\n\nBy default, only active objects are returned. To include archived objects:\n\n```bash\ncurl -G \\\n  -d \"level=ad\" \\\n  -d \"filtering=[{'field':'ad.effective_status','operator':'IN','value':['ARCHIVED']}]\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/act_AD_ACCOUNT_ID/insights/\"\n```\n\nFor deleted objects:\n\n```bash\nPOST https://graph.facebook.com/VERSION/act_ID/insights?filtering=[{\"field\":\"ad.effective_status\",\"operator\":\"IN\",\"value\":[\"DELETED\"]}]\n```\n\n## Troubleshooting\n\n### Timeouts\n- Break large queries into smaller date ranges\n- Query unique metrics separately for better performance\n- Use asynchronous requests for large datasets\n\n### Rate Limiting\nThe API implements rate limiting for optimal performance. See [Limits & Best Practices](/docs/marketing-api/insights/best-practices/) for details.\n\n### Ads Manager Discrepancy\nStarting June 10, 2025:\n- `use_unified_attribution_setting` and `action_report_time` parameters will be ignored\n- API responses will match Ads Manager settings\n- Attribution values based on ad-set-level settings\n- Mixed reporting time for actions\n\n## Related Resources\n\n- [Breakdowns](/docs/marketing-api/insights/breakdowns) - Group results by dimensions\n- [Action Breakdowns](/docs/marketing-api/insights/action-breakdowns) - Understanding action breakdown responses\n- [Async Jobs](/docs/marketing-api/insights/async) - Handle large result sets\n- [Best Practices](/docs/marketing-api/insights/best-practices/) - Limits and optimization tips", "keyPoints": ["Insights API provides unified access to ad statistics across all Facebook advertising objects", "Supports multiple aggregation levels (account, campaign, ad set, ad) with automatic deduplication", "Attribution windows allow control over conversion timeframes (1-day, 7-day)", "Field expansion enables requesting insights as part of other object queries", "Proper filtering is required to access deleted or archived object statistics"], "apiEndpoints": ["act_<AD_ACCOUNT_ID>/insights", "<CAMPAIGN_ID>/insights", "<ADSET_ID>/insights", "<AD_ID>/insights", "https://graph.facebook.com/v23.0/{object_id}/insights"], "parameters": ["fields", "access_token", "level", "date_preset", "time_range", "action_attribution_windows", "sort", "filtering", "use_unified_attribution_setting", "action_report_time"], "examples": ["Basic insights request with impressions field", "Campaign insights at ad level aggregation", "Attribution windows configuration", "Field expansion for insights within object queries", "Sorting results by reach descending", "Filtering by ad labels", "Querying archived and deleted objects"], "tags": ["facebook-marketing-api", "insights", "ad-statistics", "attribution", "aggregation", "filtering", "api-reference"], "relatedTopics": ["Breakdowns", "Action Breakdowns", "Async Jobs", "Best Practices", "Rate Limiting", "<PERSON><PERSON>", "Conversions API", "Ad Labels", "Attribution Windows"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/insights", "processedAt": "2025-06-25T16:17:39.339Z", "processor": "openrouter-claude-sonnet-4"}