{"title": "Facebook Marketing API Insights - Comprehensive Guide to Ad Statistics", "summary": "The Facebook Marketing API Insights provides a unified interface for retrieving ad performance statistics across campaigns, ad sets, and ads. This guide covers making API calls, handling responses, attribution windows, field expansion, sorting, and troubleshooting common issues.", "content": "# Facebook Marketing API Insights\n\nThe Insights API provides a single, consistent interface to retrieve ad statistics across all Facebook advertising objects.\n\n## Prerequisites\n\nBefore you begin, you will need:\n- The `ads_read` permission\n- A Meta app (see [Meta App Development](/docs/development))\n- Proper tracking setup using URL Tags, Meta Pixel, or Conversions API\n\n## Available Endpoints\n\nThe Insights API is available as an edge on any ads object:\n\n- `act_<AD_ACCOUNT_ID>/insights` - Ad account level insights\n- `<CAMPAIGN_ID>/insights` - Campaign level insights\n- `<ADSET_ID>/insights` - Ad set level insights\n- `<AD_ID>/insights` - Ad level insights\n\n## Basic Usage\n\n### Simple Campaign Statistics\n\nGet the last 7 days performance for a campaign:\n\n```bash\ncurl -G \\\n  -d \"date_preset=last_7d\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/API_VERSION/AD_CAMPAIGN_ID/insights\"\n```\n\n### Requesting Specific Fields\n\n```bash\ncurl -G \\\n  -d \"fields=impressions\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/<AD_ID>/insights\"\n```\n\n**Response:**\n```json\n{\n  \"data\": [\n    {\n      \"impressions\": \"2466376\",\n      \"date_start\": \"2009-03-28\",\n      \"date_stop\": \"2016-04-01\"\n    }\n  ],\n  \"paging\": {\n    \"cursors\": {\n      \"before\": \"MAZDZD\",\n      \"after\": \"MAZDZD\"\n    }\n  }\n}\n```\n\n## Advanced Features\n\n### Aggregation Levels\n\nAggregate results at different object levels to automatically deduplicate data:\n\n```bash\ncurl -G \\\n  -d \"level=ad\" \\\n  -d \"fields=impressions,ad_id\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/CAMPAIGN_ID/insights\"\n```\n\n### Attribution Windows\n\nSpecify different attribution windows for conversion tracking:\n\n```bash\nact_10151816772662695/insights?action_attribution_windows=['1d_click','1d_view']\n```\n\n**Response includes multiple attribution windows:**\n```json\n{\n  \"actions\": [\n    {\n      \"action_type\": \"link_click\",\n      \"value\": 6608,\n      \"1d_view\": 86,\n      \"1d_click\": 6510\n    }\n  ]\n}\n```\n\n### Field Expansion\n\nRequest insights as part of the main object response:\n\n```bash\ncurl -G \\\n  -d \"fields=insights{impressions}\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/AD_ID\"\n```\n\n### Sorting Results\n\nSort by any field in ascending or descending order:\n\n```bash\ncurl -G \\\n  -d \"sort=reach_descending\" \\\n  -d \"level=ad\" \\\n  -d \"fields=reach\" \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/AD_SET_ID/insights\"\n```\n\n### Filtering by Ad Labels\n\nGet insights for ads with specific labels:\n\n```bash\ncurl -G \\\n  -d \"fields=id,name,insights{unique_clicks,cpm,total_actions}\" \\\n  -d \"level=ad\" \\\n  -d 'filtering=[{\"field\":\"ad.adlabels\",\"operator\":\"ANY\", \"value\":[\"Label Name\"]}]' \\\n  -d 'time_range={\"since\":\"2015-03-01\",\"until\":\"2015-03-31\"}' \\\n  -d \"access_token=ACCESS_TOKEN\" \\\n  \"https://graph.facebook.com/v23.0/AD_OBJECT_ID/insights\"\n```\n\n## Working with Deleted and Archived Objects\n\n### Archived Objects\n\nGet insights for archived ads:\n\n```bash\ncurl -G \\\n  -d \"level=ad\" \\\n  -d \"filtering=[{'field':'ad.effective_status','operator':'IN','value':['ARCHIVED']}]\" \\\n  -d \"access_token=<ACCESS_TOKEN>\" \\\n  \"https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/insights/\"\n```\n\n### Deleted Objects\n\nQuery insights on deleted objects using their IDs or status filter:\n\n```bash\nPOST https://graph.facebook.com/<VERSION>/act_ID/insights?access_token=token&fields=ad_id,impressions&date_preset=lifetime&level=ad&filtering=[{\"field\":\"ad.effective_status\",\"operator\":\"IN\",\"value\":[\"DELETED\"]}]\n```\n\n## Click Metrics Definitions\n\n- **Link Clicks (`actions:link_click`)** - Clicks on ad links to select destinations\n- **Clicks (All) (`clicks`)** - Multiple types of clicks including ad container interactions\n\n## Troubleshooting\n\n### Common Issues\n\n1. **Timeouts** - Break large queries into smaller date ranges\n2. **Rate Limiting** - Follow the Insights API rate limits and best practices\n3. **Permission Errors** - Ensure you have access to all requested objects\n\n### Performance Tips\n\n- Query unique metrics separately to improve performance\n- Use appropriate date ranges to avoid timeouts\n- Consider using asynchronous requests for large datasets\n\n### Ads Manager Discrepancy\n\nStarting June 10, 2025:\n- `use_unified_attribution_setting` and `action_report_time` parameters will be ignored\n- API responses will match Ads Manager settings\n- Use `use_unified_attribution_setting=true` for current Ads Manager behavior\n\n## Related Resources\n\n- [Breakdowns](/docs/marketing-api/insights/breakdowns) - Group results by dimensions\n- [Action Breakdowns](/docs/marketing-api/insights/action-breakdowns) - Understanding action breakdown responses\n- [Async Jobs](/docs/marketing-api/insights/async) - Handle large result sets\n- [Limits and Best Practices](/docs/marketing-api/insights/best-practices/) - Optimization guidelines", "keyPoints": ["Insights API provides unified access to ad statistics across all Facebook advertising objects", "Supports multiple aggregation levels (account, campaign, ad set, ad) with automatic deduplication", "Attribution windows allow tracking conversions across different time periods (1d_click, 1d_view, 7d_click)", "Field expansion enables requesting insights as part of main object responses", "Proper filtering and pagination are essential for handling large datasets and avoiding timeouts"], "apiEndpoints": ["act_<AD_ACCOUNT_ID>/insights", "<CAMPAIGN_ID>/insights", "<ADSET_ID>/insights", "<AD_ID>/insights", "https://graph.facebook.com/v23.0/{object_id}/insights"], "parameters": ["fields", "date_preset", "level", "action_attribution_windows", "sort", "filtering", "time_range", "access_token", "use_unified_attribution_setting", "action_report_time"], "examples": ["curl -G -d \"date_preset=last_7d\" -d \"access_token=ACCESS_TOKEN\" \"https://graph.facebook.com/API_VERSION/AD_CAMPAIGN_ID/insights\"", "curl -G -d \"fields=impressions\" -d \"access_token=ACCESS_TOKEN\" \"https://graph.facebook.com/v23.0/<AD_ID>/insights\"", "curl -G -d \"level=ad\" -d \"fields=impressions,ad_id\" \"https://graph.facebook.com/v23.0/CAMPAIGN_ID/insights\"", "curl -G -d \"sort=reach_descending\" -d \"level=ad\" -d \"fields=reach\" \"https://graph.facebook.com/v23.0/AD_SET_ID/insights\""], "tags": ["facebook-marketing-api", "insights", "ad-statistics", "attribution", "performance-metrics", "api-reference"], "relatedTopics": ["Breakdowns", "Action Breakdowns", "Async Jobs", "Rate Limiting", "<PERSON><PERSON>", "Conversions API", "URL Tags", "Ad Labels", "Attribution Windows", "Field Expansion"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/insights", "processedAt": "2025-06-25T15:07:48.072Z", "processor": "openrouter-claude-sonnet-4"}