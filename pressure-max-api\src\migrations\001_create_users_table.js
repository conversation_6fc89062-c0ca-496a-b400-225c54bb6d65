/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email').notNullable().unique();
    table.string('password').notNullable();
    table.string('first_name').notNullable();
    table.string('last_name').notNullable();
    table.enum('role', ['admin', 'user', 'manager']).defaultTo('user');
    table.boolean('is_active').defaultTo(true);
    table.uuid('tenant_id').nullable();
    table.json('permissions').defaultTo('[]');
    table.string('profile_image').nullable();
    table.string('phone').nullable();
    table.string('timezone').defaultTo('UTC');
    table.timestamp('last_login_at').nullable();
    table.timestamp('email_verified_at').nullable();
    table.timestamp('deleted_at').nullable();
    table.timestamps(true, true);

    // Indexes
    table.index('email');
    table.index('tenant_id');
    table.index('role');
    table.index('is_active');
    table.index('created_at');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
