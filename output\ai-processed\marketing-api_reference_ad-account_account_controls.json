{"title": "Facebook Marketing API - Ad Account Account Controls Reference", "summary": "Complete reference for managing Ad Account Account Controls through the Facebook Marketing API, including reading, creating, and updating business constraints for ad accounts. This endpoint allows you to configure audience controls, placement controls, and other account-level restrictions.", "content": "# Ad Account Account Controls\n\nThe Ad Account Account Controls endpoint allows you to manage business constraints and controls for Facebook ad accounts through the Marketing API.\n\n## Reading Account Controls\n\nGet default fields on an AdAccountBusinessConstraints node associated with an AdAccount. This returns information about current account-level restrictions and controls.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/account_controls\n```\n\n### Example Request\n```bash\nGET /v23.0/{ad-account-id}/account_controls HTTP/1.1\nHost: graph.facebook.com\n```\n\n### PHP SDK Example\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/account_controls',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n### JavaScript SDK Example\n```javascript\nFB.api(\n    \"/{ad-account-id}/account_controls\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Response Format\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Response Fields\n- **data**: A list of AdAccountBusinessConstraints nodes\n- **paging**: Pagination information for large result sets\n\n### Reading Error Codes\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 200 | Permissions error |\n| 190 | Invalid OAuth 2.0 Access Token |\n\n## Creating Account Controls\n\nCreate new business constraints for an ad account by making a POST request to the account_controls edge.\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/account_controls\n```\n\n### Parameters\n\n#### Required Parameters\n- **audience_controls** (JSON/object): Audience targeting controls\n  - **age_min** (int64): Minimum age for audience targeting\n  - **geo_locations** (JSON/object): Geographic location targeting\n  - **excluded_geo_locations** (JSON/object): Geographic locations to exclude\n  - **exclusions** (JSON/object): Additional audience exclusions\n\n#### Optional Parameters\n- **placement_controls** (JSON/object): Controls for ad placement\n  - **placement_exclusions** (array): List of placements to exclude\n    - Allowed values:\n      - `AUDIENCE_NETWORK_CLASSIC`: Native, banner & interstitial positions\n      - `AUDIENCE_NETWORK_REWARDED_VIDEO`: Rewarded videos\n      - `AUDIENCE_NETWORK_INSTREAM_VIDEO`: Instream videos\n      - `FACEBOOK_MARKETPLACE`: Marketplace section\n      - `FACEBOOK_RIGHT_HAND_COLUMN`: Right hand column\n\n- **campaign_ids_to_set_ap** (array<numeric string>): Campaign IDs to apply controls to\n\n### Placement Controls Example\n```json\n{\n  \"placement_controls\": {\n    \"placement_exclusions\": [\"audience_network_rewarded_video\"]\n  }\n}\n```\n\n### Return Type\n```json\n{\n  \"id\": \"string\",\n  \"success\": \"bool\",\n  \"error_code\": \"string\",\n  \"error_message\": \"string\"\n}\n```\n\n### Creating Error Codes\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 200 | Permissions error |\n| 2641 | Ad includes or excludes locations that are currently restricted |\n\n## Updating Account Controls\n\nTo update existing account controls, use the POST endpoint with new parameter values. The system will update the existing AdAccountBusinessConstraints associated with the AdAccount.\n\n## Deleting Account Controls\n\nDeletion operations are not supported for this endpoint. Account controls must be updated rather than deleted.\n\n## Important Notes\n\n- Account controls apply business-level constraints to ad accounts\n- Placement exclusions only support specific predefined placements\n- Geographic restrictions may apply based on current platform policies\n- All changes require appropriate permissions for the ad account", "keyPoints": ["Manages business constraints and controls for Facebook ad accounts", "Supports reading current account controls and creating new restrictions", "Allows configuration of audience controls, placement exclusions, and geographic targeting", "Placement exclusions are limited to specific predefined options", "Deletion operations are not supported - only reading, creating, and updating"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/account_controls", "POST /act_{ad_account_id}/account_controls"], "parameters": ["audience_controls", "age_min", "geo_locations", "excluded_geo_locations", "exclusions", "placement_controls", "placement_exclusions", "campaign_ids_to_set_ap"], "examples": ["GET request for reading account controls", "PHP SDK implementation", "JavaScript SDK implementation", "Placement exclusions configuration", "Response format examples"], "tags": ["Facebook Marketing API", "Ad Account", "Business Constraints", "Account Controls", "Audience Targeting", "Placement Controls", "API Reference"], "relatedTopics": ["AdAccountBusinessConstraints", "AdAccount", "Graph API", "Audience Network", "Facebook Marketplace", "Geographic Targeting", "Placement Targeting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/account_controls/", "processedAt": "2025-06-25T16:19:16.244Z", "processor": "openrouter-claude-sonnet-4"}