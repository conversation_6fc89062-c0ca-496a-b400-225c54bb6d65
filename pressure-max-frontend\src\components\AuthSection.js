import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useForm } from 'react-hook-form';
import { facebookAPI } from '../services/api';
import { User, LogOut, Eye, EyeOff, Facebook } from 'lucide-react';
import toast from 'react-hot-toast';

const AuthSection = () => {
  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();
  const [activeTab, setActiveTab] = useState('facebook');
  const [showToken, setShowToken] = useState(false);
  const [loading, setLoading] = useState(false);

  const loginForm = useForm();
  const registerForm = useForm();

  // Check for Facebook OAuth callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state && !isAuthenticated) {
      handleFacebookCallback(code, state);
    }
  }, [isAuthenticated]);

  const onLogin = async (data) => {
    setLoading(true);
    await login(data);
    setLoading(false);
  };

  const onRegister = async (data) => {
    setLoading(true);
    await register(data);
    setLoading(false);
  };

  const handleFacebookLogin = async () => {
    try {
      setLoading(true);
      const redirectUri = `${window.location.origin}${window.location.pathname}`;
      const response = await facebookAPI.getOAuthUrl(redirectUri);

      if (response.data.oauthUrl) {
        // Store state for verification
        localStorage.setItem('facebook_oauth_state', response.data.state);
        window.location.href = response.data.oauthUrl;
      } else {
        throw new Error('No OAuth URL received');
      }
    } catch (error) {
      setLoading(false);
      toast.error('Failed to initiate Facebook login: ' + error.message);
    }
  };

  const handleFacebookCallback = async (code, state) => {
    try {
      setLoading(true);

      // Verify state parameter
      const storedState = localStorage.getItem('facebook_oauth_state');
      if (!storedState || storedState !== state) {
        throw new Error('Invalid state parameter');
      }

      // Clean up stored state
      localStorage.removeItem('facebook_oauth_state');

      const redirectUri = `${window.location.origin}${window.location.pathname}`;
      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);

      if (response.data.user && response.data.tokens) {
        // Store tokens and user data
        localStorage.setItem('accessToken', response.data.tokens.accessToken);
        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
        localStorage.setItem('user', JSON.stringify(response.data.user));

        // Update auth context
        window.location.reload(); // Simple way to update the auth context

        toast.success('Successfully logged in with Facebook!');
      }
    } catch (error) {
      setLoading(false);
      toast.error('Facebook login failed: ' + error.message);

      // Clean up URL parameters
      const url = new URL(window.location);
      url.searchParams.delete('code');
      url.searchParams.delete('state');
      window.history.replaceState({}, document.title, url.toString());
    }
  };

  if (isAuthenticated) {
    return (
      <div className="auth-section">
        <h2>Authentication Status</h2>
        <div className="auth-status authenticated">
          <div className="user-info">
            <User size={20} />
            <div>
              <p><strong>{user.firstName} {user.lastName}</strong></p>
              <p>{user.email}</p>
              <p>Role: {user.role}</p>
            </div>
          </div>
          
          <div className="token-section">
            <div className="token-header">
              <span>JWT Token:</span>
              <button 
                onClick={() => setShowToken(!showToken)}
                className="toggle-token"
              >
                {showToken ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {showToken && (
              <div className="token-display">
                <code>{getToken()}</code>
              </div>
            )}
          </div>

          <button onClick={logout} className="logout-btn">
            <LogOut size={16} />
            Logout
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-section">
      <h2>Authentication</h2>
      
      <div className="auth-tabs">
        <button 
          className={activeTab === 'login' ? 'active' : ''}
          onClick={() => setActiveTab('login')}
        >
          Login
        </button>
        <button 
          className={activeTab === 'register' ? 'active' : ''}
          onClick={() => setActiveTab('register')}
        >
          Register
        </button>
      </div>

      {activeTab === 'login' ? (
        <form onSubmit={loginForm.handleSubmit(onLogin)} className="auth-form">
          <div className="form-group">
            <label>Email:</label>
            <input
              type="email"
              {...loginForm.register('email', { required: 'Email is required' })}
              placeholder="Enter your email"
            />
            {loginForm.formState.errors.email && (
              <span className="error">{loginForm.formState.errors.email.message}</span>
            )}
          </div>

          <div className="form-group">
            <label>Password:</label>
            <input
              type="password"
              {...loginForm.register('password', { required: 'Password is required' })}
              placeholder="Enter your password"
            />
            {loginForm.formState.errors.password && (
              <span className="error">{loginForm.formState.errors.password.message}</span>
            )}
          </div>

          <button type="submit" disabled={loading} className="submit-btn">
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      ) : (
        <form onSubmit={registerForm.handleSubmit(onRegister)} className="auth-form">
          <div className="form-group">
            <label>First Name:</label>
            <input
              type="text"
              {...registerForm.register('firstName', { required: 'First name is required' })}
              placeholder="Enter your first name"
            />
            {registerForm.formState.errors.firstName && (
              <span className="error">{registerForm.formState.errors.firstName.message}</span>
            )}
          </div>

          <div className="form-group">
            <label>Last Name:</label>
            <input
              type="text"
              {...registerForm.register('lastName', { required: 'Last name is required' })}
              placeholder="Enter your last name"
            />
            {registerForm.formState.errors.lastName && (
              <span className="error">{registerForm.formState.errors.lastName.message}</span>
            )}
          </div>

          <div className="form-group">
            <label>Email:</label>
            <input
              type="email"
              {...registerForm.register('email', { required: 'Email is required' })}
              placeholder="Enter your email"
            />
            {registerForm.formState.errors.email && (
              <span className="error">{registerForm.formState.errors.email.message}</span>
            )}
          </div>

          <div className="form-group">
            <label>Password:</label>
            <input
              type="password"
              {...registerForm.register('password', { 
                required: 'Password is required',
                minLength: { value: 8, message: 'Password must be at least 8 characters' }
              })}
              placeholder="Enter your password (min 8 characters)"
            />
            {registerForm.formState.errors.password && (
              <span className="error">{registerForm.formState.errors.password.message}</span>
            )}
          </div>

          <div className="form-group">
            <label>Phone (optional):</label>
            <input
              type="tel"
              {...registerForm.register('phone')}
              placeholder="Enter your phone number"
            />
          </div>

          <button type="submit" disabled={loading} className="submit-btn">
            {loading ? 'Registering...' : 'Register'}
          </button>
        </form>
      )}
    </div>
  );
};

export default AuthSection;
