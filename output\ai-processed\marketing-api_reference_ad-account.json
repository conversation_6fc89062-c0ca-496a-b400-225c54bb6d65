{"title": "Facebook Marketing API - Ad Account Reference", "summary": "Complete reference documentation for the Facebook Marketing API Ad Account object, including fields, edges, CRUD operations, and usage examples. Ad Accounts represent business entities that create and manage ads on Facebook.", "content": "# Facebook Marketing API - Ad Account Reference\n\n## Overview\n\nRepresents a business, person or other entity who creates and manages ads on Facebook. Multiple people can manage an account, and each person can have one or more levels of access to an account.\n\n## Important Updates\n\n### iOS 14.5 Changes\nIn response to Apple's new policy, breaking changes affect SDKAdNetwork, Marketing API and Ads Insights API endpoints. The `agency_client_declaration` field requires Admin privileges for all operations starting with v10.0.\n\n## Ad Volume Management\n\nYou can view the volume of ads running or in review for your ad accounts. These ads count against the ads limit per page.\n\n### Querying Ad Volume\n```bash\ncurl -G \\\n  -d \"access_token=<access_token>\" \\\n  \"https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume\"\n```\n\nResponse:\n```json\n{\"data\":[{\"ads_running_or_in_review_count\":2}]}\n```\n\n### Running or In Review Status\nAn ad is considered running or in review when:\n- `effective_status` is `1` (active)\n- `configured_status` is `active` and `effective_status` is `9` (pending review) or `17` (pending processing)\n- Ad account status is `1` (active), `8` (pending settlement), or `9` (in grace period)\n- Ad set schedule allows current execution\n\n## Account Limits\n\n| Limit | Regular Account | Bulk Account |\n|-------|----------------|-------------|\n| Maximum ads | 6,000 | 50,000 |\n| Maximum ad sets | 6,000 | 10,000 |\n| Maximum campaigns | 6,000 | 10,000 |\n| Maximum archived items | 100,000 each type | 100,000 each type |\n| Maximum people with access | 25 | 25 |\n| Maximum accounts per person | 25 | 25 |\n\n## Reading Ad Accounts\n\n### Basic Read\n```bash\ncurl -G \\\n  -d 'fields=name,account_status,currency' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>\n```\n\n### Finding Users with Access\n```bash\ncurl -G \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/users\n```\n\n### Digital Services Act Information\n```bash\ncurl -X GET \\\n\"https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>?fields=default_dsa_payor%2Cdefault_dsa_beneficiary&access_token=<ACCESS_TOKEN>\"\n```\n\n## Key Fields\n\n### Core Fields\n- `id`: The string `act_{ad_account_id}`\n- `account_id`: The ID of the Ad Account\n- `account_status`: Status code (1=ACTIVE, 2=DISABLED, etc.)\n- `name`: Name of the account\n- `currency`: Currency used for the account\n- `timezone_id`: Timezone ID\n- `business`: Associated Business Manager\n\n### Financial Fields\n- `amount_spent`: Current amount spent\n- `balance`: Bill amount due\n- `spend_cap`: Maximum spending limit\n- `funding_source`: Payment method ID\n- `funding_source_details`: Detailed payment information\n\n### Business Fields\n- `business_name`: Business name\n- `business_city`, `business_state`, `business_country_code`: Business address\n- `end_advertiser`: Entity the ads target\n- `media_agency`: Advertising agency\n- `partner`: Advertising partner\n\n## Creating Ad Accounts\n\n```bash\ncurl \\\n-F \"name=MyAdAccount\" \\\n-F \"currency=USD\" \\\n-F \"timezone_id=1\" \\\n-F \"end_advertiser=<END_ADVERTISER_ID>\" \\\n-F \"media_agency=<MEDIA_AGENCY_ID>\" \\\n-F \"partner=NONE\" \\\n-F \"access_token=<ACCESS_TOKEN>\" \\\n\"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount\"\n```\n\n### Required Parameters\n- `name`: Account name\n- `currency`: ISO 4217 currency code\n- `timezone_id`: Timezone identifier\n- `end_advertiser`: Target entity (Page ID, App ID, or NONE/UNFOUND)\n- `media_agency`: Agency identifier\n- `partner`: Partner identifier\n\n## Updating Ad Accounts\n\n### Update Basic Information\n```bash\ncurl -X POST \\\n  -F 'name=\"Updated Account Name\"' \\\n  -F 'spend_cap=1000.00' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>\n```\n\n### Update DSA Information\n```bash\ncurl -X POST \\\n  -F 'default_dsa_payor=\"payor_info\"' \\\n  -F 'default_dsa_beneficiary=\"beneficiary_info\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>\n```\n\n## Important Edges\n\n- `/activities`: Account activities\n- `/adcreatives`: Ad creatives\n- `/ads`: Ads in the account\n- `/adsets`: Ad sets\n- `/campaigns`: Campaigns\n- `/customaudiences`: Custom audiences\n- `/customconversions`: Custom conversions\n- `/insights`: Performance insights\n- `/users`: Users with access\n\n## Error Codes\n\nCommon error codes:\n- `100`: Invalid parameter\n- `190`: Invalid OAuth 2.0 Access Token\n- `200`: Permissions error\n- `613`: Rate limit exceeded\n- `80004`: Too many calls to ad account\n\n## Best Practices\n\n1. **Rate Limiting**: Respect API rate limits to avoid throttling\n2. **Permissions**: Ensure proper access levels for operations\n3. **Field Selection**: Request only needed fields to optimize performance\n4. **Error Handling**: Implement robust error handling for API calls\n5. **Spend Caps**: Monitor and manage spend caps appropriately", "keyPoints": ["Ad Accounts represent business entities that create and manage Facebook ads", "Account limits vary between regular (6,000 ads) and bulk accounts (50,000 ads)", "iOS 14.5 changes require Admin privileges for agency_client_declaration field", "Ad volume tracking helps manage ads running or in review against page limits", "DSA (Digital Services Act) fields can be set for EU compliance requirements"], "apiEndpoints": ["GET /act_{ad_account_id}", "POST /act_{ad_account_id}", "GET /act_{ad_account_id}/ads_volume", "GET /act_{ad_account_id}/users", "POST /{business_id}/adaccount", "POST /act_{ad_account_id}/assigned_users"], "parameters": ["account_id", "account_status", "currency", "name", "spend_cap", "timezone_id", "end_advertiser", "media_agency", "partner", "default_dsa_payor", "default_dsa_beneficiary", "agency_client_declaration"], "examples": ["curl -G -d 'access_token=<access_token>' 'https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume'", "curl -F 'name=MyAdAccount' -F 'currency=USD' -F 'timezone_id=1' 'https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount'", "curl -X GET 'https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>?fields=default_dsa_payor%2Cdefault_dsa_beneficiary'"], "tags": ["Facebook Marketing API", "Ad Account", "CRUD Operations", "Ad Volume", "Business Manager", "iOS 14.5", "DSA Compliance"], "relatedTopics": ["Business Manager API", "Ad Campaign Management", "Custom Audiences", "Ad Insights API", "Payment Methods", "User Permissions", "Rate Limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account", "processedAt": "2025-06-25T16:18:47.157Z", "processor": "openrouter-claude-sonnet-4"}