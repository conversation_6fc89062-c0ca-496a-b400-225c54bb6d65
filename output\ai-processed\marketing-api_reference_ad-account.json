{"title": "Facebook Marketing API - Ad Account Reference", "summary": "Complete reference documentation for the Facebook Marketing API Ad Account object, including fields, edges, CRUD operations, and usage examples. Ad Accounts represent entities that create and manage ads on Facebook with specific limits and permissions.", "content": "# Facebook Marketing API - Ad Account Reference\n\n## Overview\n\nRepresents a business, person or other entity who creates and manages ads on Facebook. Multiple people can manage an account, and each person can have one or more levels of access to an account.\n\n**Important Notice**: In response to Apple's iOS 14.5 requirements, there are breaking changes affecting SDKAdNetwork, Marketing API and Ads Insights API endpoints.\n\n## Ad Volume Management\n\nYou can view the volume of ads running or in review for your ad accounts. These ads count against the ads limit per page that will be enforced.\n\n### Querying Ad Volume\n\n```bash\ncurl -G \\\n  -d \"access_token=<access_token>\" \\\n  \"https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume\"\n```\n\nResponse:\n```json\n{\"data\":[{\"ads_running_or_in_review_count\":2}]}\n```\n\n### Running or In Review Status\n\nAn ad is considered \"running or in review\" when:\n- `effective_status` is `1` (active)\n- `configured_status` is `active` and `effective_status` is `9` (pending review) or `17` (pending processing)\n- Ad account status is `1` (active), `8` (pending settlement), or `9` (in grace period)\n- Ad set schedule indicates current time is within run period\n\n## Account Limits\n\n| Limit | Value |\n|-------|-------|\n| Maximum ad accounts per person | 25 |\n| Maximum people with access per ad account | 25 |\n| Maximum ads per regular ad account | 6,000 non-archived non-deleted |\n| Maximum ads per bulk ad account | 50,000 non-archived non-deleted |\n| Maximum archived ads per ad account | 100,000 |\n| Maximum ad sets per regular ad account | 6,000 non-archived non-deleted |\n| Maximum ad sets per bulk ad account | 10,000 non-archived non-deleted |\n| Maximum ad campaigns per regular ad account | 6,000 non-archived non-deleted |\n| Maximum ad campaigns per bulk ad account | 10,000 non-archived non-deleted |\n\n## Reading Ad Accounts\n\n### Basic Read\n\n```bash\ncurl -G \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>\n```\n\n### Finding Users with Access\n\n```bash\ncurl -G \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/users\n```\n\n### Digital Services Act Information\n\n```bash\ncurl -X GET \\\n\"https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>?fields=default_dsa_payor%2Cdefault_dsa_beneficiary&access_token=<ACCESS_TOKEN>\"\n```\n\n## Key Fields\n\n### Core Fields\n- `id`: The string `act_{ad_account_id}`\n- `account_id`: The ID of the Ad Account\n- `account_status`: Status of the account (1=ACTIVE, 2=DISABLED, etc.)\n- `name`: Name of the account\n- `currency`: Currency used for the account\n- `timezone_id`: Timezone ID of the ad account\n- `business`: The Business Manager if owned by one\n\n### Financial Fields\n- `amount_spent`: Current amount spent by the account\n- `balance`: Bill amount due for this Ad Account\n- `spend_cap`: Maximum amount that can be spent\n- `funding_source`: ID of the payment method\n- `funding_source_details`: Detailed payment method information\n\n### Configuration Fields\n- `agency_client_declaration`: Agency advertising details (requires Admin privileges)\n- `capabilities`: List of capabilities an Ad Account can have\n- `brand_safety_content_filter_levels`: Content filter levels for ads\n- `default_dsa_beneficiary`: Default DSA beneficiary value\n- `default_dsa_payor`: Default DSA payor value\n\n## Creating Ad Accounts\n\nTo create a new ad account, specify required fields:\n\n```bash\ncurl \\\n-F \"name=MyAdAccount\" \\\n-F \"currency=USD\" \\\n-F \"timezone_id=1\" \\\n-F \"end_advertiser=<END_ADVERTISER_ID>\" \\\n-F \"media_agency=<MEDIA_AGENCY_ID>\" \\\n-F \"partner=NONE\" \\\n-F \"access_token=<ACCESS_TOKEN>\" \\\n\"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount\"\n```\n\n### Required Parameters\n- `name`: The name of the ad account\n- `currency`: ISO 4217 Currency Code\n- `timezone_id`: ID for the timezone\n- `end_advertiser`: Entity the ads will target (Page ID, App ID, or NONE/UNFOUND)\n- `media_agency`: The agency (Page ID, App ID, or NONE/UNFOUND)\n- `partner`: Advertising partner (Page ID, App ID, or NONE/UNFOUND)\n\n**Important**: Once `end_advertiser` is set to a value other than `NONE` or `UNFOUND`, it cannot be changed.\n\n## Updating Ad Accounts\n\nUpdate an ad account with POST request:\n\n```bash\nPOST /act_{ad_account_id}\n```\n\n### Key Update Parameters\n- `name`: Update account name\n- `spend_cap`: Set spending limit\n- `spend_cap_action`: Reset amount_spent or delete spend_cap\n- `default_dsa_beneficiary`: Set DSA beneficiary\n- `default_dsa_payor`: Set DSA payor\n- `is_notifications_enabled`: Enable/disable notifications\n\n**Note**: DSA payor and beneficiary must be set together or both unset with empty strings.\n\n## Important Edges\n\n- `/activities`: Ad account activities\n- `/adcreatives`: Ad creatives\n- `/ads_volume`: Ad volume information\n- `/users`: Users with access\n- `/customaudiences`: Custom audiences\n- `/campaigns`: Ad campaigns\n- `/adsets`: Ad sets\n- `/ads`: Ads\n- `/insights`: Performance insights\n\n## Error Codes\n\nCommon error codes:\n- `100`: Invalid parameter\n- `190`: Invalid OAuth 2.0 Access Token\n- `200`: Permissions error\n- `613`: Rate limit exceeded\n- `80004`: Too many calls to ad account\n- `368`: Action deemed abusive or disallowed", "keyPoints": ["Ad Accounts represent entities that create and manage Facebook ads with specific access levels and permissions", "Ad volume limits are enforced per page, with different limits for regular (6,000) vs bulk (50,000) ad accounts", "The end_advertiser field cannot be changed once set to a value other than NONE or UNFOUND", "DSA payor and beneficiary fields must be set together or unset together for compliance", "Account status and effective_status determine whether ads are considered 'running or in review'"], "apiEndpoints": ["GET /act_{ad_account_id}", "POST /act_{ad_account_id}", "GET /act_{ad_account_id}/ads_volume", "GET /act_{ad_account_id}/users", "POST /{business_id}/adaccount", "GET /act_{ad_account_id}/activities", "GET /act_{ad_account_id}/adcreatives"], "parameters": ["account_id", "account_status", "name", "currency", "timezone_id", "spend_cap", "amount_spent", "end_advertiser", "media_agency", "partner", "agency_client_declaration", "default_dsa_beneficiary", "default_dsa_payor", "funding_source", "business", "capabilities"], "examples": ["curl -G -d 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>", "curl -G -d 'access_token=<access_token>' 'https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume'", "curl -F 'name=MyAdAccount' -F 'currency=USD' -F 'timezone_id=1' 'https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount'"], "tags": ["Facebook Marketing API", "Ad Account", "Account Management", "Ad Volume", "Business Manager", "DSA Compliance", "Account <PERSON>its", "CRUD Operations"], "relatedTopics": ["Business Manager API", "Ad Campaigns", "Ad Sets", "Custom Audiences", "Payment Methods", "User Permissions", "Rate Limiting", "iOS 14 Changes", "Digital Services Act"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account", "processedAt": "2025-06-25T15:10:44.429Z", "processor": "openrouter-claude-sonnet-4"}