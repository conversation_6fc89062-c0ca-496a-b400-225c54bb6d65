# Pressure Max API Engine

A comprehensive, production-ready API backend for Facebook Ads automation and lead management platform with AI-powered calling capabilities.

## 🚀 Features

- **Multi-tenant SaaS Architecture** - Complete tenant isolation and management
- **Facebook Marketing API Integration** - Full OAuth flow, campaign management, and lead processing
- **Real-time WebSocket Support** - Live updates for campaigns and leads
- **JWT Authentication** - Secure token-based auth with refresh tokens
- **Role-based Access Control** - Granular permissions and authorization
- **Rate Limiting & Security** - Production-ready security measures
- **Comprehensive Error Handling** - Structured error responses and logging
- **API Documentation** - Auto-generated Swagger/OpenAPI docs
- **Caching Strategy** - Redis-based caching for performance
- **Audit Logging** - Complete audit trail for compliance

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React/Next.js │    │   Pressure Max  │    │   PostgreSQL    │
│    Frontend     │◄──►│      API        │◄──►│    Database     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │   Cache/Session │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  External APIs  │
                       │ Facebook, VAPI, │
                       │ Stripe, Calendar│
                       └─────────────────┘
```

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 13+
- Redis 6+
- Facebook Developer Account
- Stripe Account (for billing)
- VAPI Account (for AI calling)

## 🛠️ Installation

1. **Clone and setup:**
   ```bash
   cd pressure-max-api
   npm install
   ```

2. **Environment Configuration:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup:**
   ```bash
   # Create PostgreSQL database
   createdb pressure_max
   
   # Run migrations
   npm run migrate
   
   # Optional: Run seeds
   npm run seed
   ```

4. **Start Redis:**
   ```bash
   redis-server
   ```

5. **Start the API:**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## 🔧 Configuration

### Required Environment Variables

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/pressure_max

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-refresh-token-secret

# Facebook API
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your-webhook-verify-token

# VAPI
VAPI_API_KEY=your-vapi-api-key

# Stripe
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Redis
REDIS_URL=redis://localhost:6379

# Email
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

## 📚 API Documentation

Once running, visit:
- **API Docs**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health

## 🔐 Authentication Flow

1. **Register/Login:**
   ```bash
   POST /api/v1/auth/register
   POST /api/v1/auth/login
   ```

2. **Use Bearer Token:**
   ```bash
   Authorization: Bearer <your-jwt-token>
   ```

3. **Refresh Token:**
   ```bash
   POST /api/v1/auth/refresh
   ```

## 📊 Core API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout

### Facebook Integration
- `GET /api/v1/facebook/oauth-url` - Get OAuth URL
- `POST /api/v1/facebook/oauth-callback` - Handle OAuth callback
- `GET /api/v1/facebook/ad-accounts` - Get user's ad accounts
- `GET /api/v1/facebook/pages` - Get user's pages
- `POST /api/v1/facebook/campaigns` - Create campaign

### User Management
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users/team` - Get team members

### Webhooks
- `POST /api/v1/webhooks/facebook` - Facebook lead webhooks
- `POST /api/v1/webhooks/vapi` - VAPI call webhooks
- `POST /api/v1/webhooks/stripe` - Stripe billing webhooks

## 🔄 Real-time Features

WebSocket connection for real-time updates:

```javascript
const socket = io('http://localhost:3000', {
  auth: {
    token: 'your-jwt-token'
  }
});

// Subscribe to campaign updates
socket.emit('subscribe:campaigns', [campaignId]);

// Listen for lead updates
socket.on('new_lead', (lead) => {
  console.log('New lead received:', lead);
});
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📝 Database Migrations

```bash
# Create new migration
npx knex migrate:make migration_name

# Run migrations
npm run migrate

# Rollback last migration
npm run migrate:rollback
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build image
docker build -t pressure-max-api .

# Run with docker-compose
docker-compose up -d
```

### Environment Setup
1. Set all required environment variables
2. Ensure PostgreSQL and Redis are accessible
3. Run database migrations
4. Configure reverse proxy (nginx/Apache)
5. Set up SSL certificates
6. Configure monitoring and logging

## 📊 Monitoring

The API includes built-in monitoring:
- Health check endpoint: `/health`
- Structured logging with Winston
- Performance metrics
- Error tracking
- Audit trails

## 🔒 Security Features

- JWT token authentication
- Rate limiting per user/endpoint
- Input validation and sanitization
- CORS protection
- Helmet security headers
- SQL injection prevention
- XSS protection
- Token blacklisting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting: `npm run lint`
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation at `/api-docs`
- Review the logs in `logs/app.log`
- Open an issue on GitHub
