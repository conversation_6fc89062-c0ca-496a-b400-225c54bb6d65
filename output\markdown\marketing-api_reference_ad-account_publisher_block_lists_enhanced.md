# Facebook Marketing API - Ad Account Publisher Block Lists

## Summary
Documentation for managing publisher block lists within Facebook ad accounts. This endpoint allows creating publisher block lists to control where ads are displayed, but does not support reading, updating, or deleting operations.

## Key Points
- Only CREATE operations are supported - reading, updating, and deleting are not available
- Creates PublisherBlockList objects to control ad placement exclusions
- Requires only a 'name' parameter to create a new block list
- Returns the created object ID and supports read-after-write functionality
- Part of the ad account edge structure in Facebook Marketing API

## API Endpoints
- `POST /act_{ad_account_id}/publisher_block_lists`

## Parameters
- name (string) - Name of the block list

## Content
# Ad Account Publisher Block Lists

## Overview

The Publisher Block Lists endpoint allows you to manage lists of publishers where you don't want your ads to appear within a Facebook ad account.

## Supported Operations

### Reading
Reading operations are **not supported** on this endpoint.

### Creating
You can create a new publisher block list by making a POST request to the `publisher_block_lists` edge.

**Endpoint:**
```
POST /act_{ad_account_id}/publisher_block_lists
```

When posting to this edge, a [PublisherBlockList](/docs/marketing-api/reference/publisher-block-list/) object will be created.

#### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `name` | string | Name of the block list |

#### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will return the node represented by `id`.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |

### Updating
Updating operations are **not supported** on this endpoint.

### Deleting
Deleting operations are **not supported** on this endpoint.

## API Version
This documentation is for Graph API version **v23.0**.

## Examples
POST request to create publisher block list with name parameter

Return structure with numeric string ID

---
**Tags:** Facebook Marketing API, Publisher Block Lists, Ad Account, Graph API, Ad Placement Control
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/publisher_block_lists/
**Processed:** 2025-06-25T15:33:10.774Z