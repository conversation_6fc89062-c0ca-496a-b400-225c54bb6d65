# Bidding

On This Page

[Bidding](#bidding)

[Documentation Contents](#documentation-contents)

[Overview](#overview)

[Guides](#guides)

[Support](#support)

# Bidding

Learn how your bids and budget work with Facebook's ad auction and delivery. This covers bidding options, placing bids for desired action, setting budget limits and tracking ads delivery. Facebook's auction functions the same way for API-created ads as they do for ads from Facebook tools. See [Ads Help Center, Auction](https://www.facebook.com/business/help/delivery).

## Main Concepts

*   [**Bid Strategies**](/docs/marketing-api/bidding/overview/bid-strategy) — Provide your bid preferences.
*   [**Optimization Goals**](/docs/marketing-api/bidding-and-optimization#opt) — Define advertising goals you want to achieve when Facebook delivers your ads.
*   [**Budgets**](/docs/marketing-api/bidding/overview/budgets)
*   [**Pacing and Scheduling**](/docs/marketing-api/pacing) — Determine how your ads budget is spent over time.
*   [**Billing Events**](/docs/marketing-api/bidding-and-optimization/billing-events) - Defines events you want to pay for, including impressions, clicks, or various actions.

## Common Use Cases

*   [**Campaign Budget Optimization**](/docs/marketing-api/bidding-and-optimization/campaign-budget-optimization) — Optimize the distribution of a campaign budget across your campaign's ad sets.
*   [**Optimized Cost Per Mille Ads**](/docs/marketing-api/bidding/optimized-cost-per-mille) — Prioritize your marketing goals. Then, automatically deliver ads towards these goals in the most effective way possible.
*   [**Cost Per Action Ads**](/docs/marketing-api/cost-per-action-ads) — Specify conversion events and get charged by the amount of conversions.
*   [**Reach and Frequency**](/docs/marketing-api/reachandfrequency) — Bid on a predicted unique audience reach for your ads on Facebook and Instagram and control display frequency.
*   [**Bid Multipliers**](/docs/marketing-api/bidding-and-optimization/bid-multiplier) — Allows you to maintain a nuanced bidding strategy within a single ad set with one targeted audience. **Available on a limited basis.**

[](#)

## Documentation Contents

### [Overview](/docs/marketing-api/bidding-and-optimization)

Core concepts and usage requirements. Learn about [**Budgets**](/docs/marketing-api/bidding/overview/budgetsg), [**Optimization Goals**](/docs/marketing-api/bidding-and-optimization#opt), and [**Bid Strategies**](/docs/marketing-api/bidding/overview/bid-strategy).

### [Guides](/docs/marketing-api/bidding/guides)

Use case based guides to help you perform specific actions.

### [Support](/docs/marketing-api/support)

Get support: FAQs, API updates, helpful links, Reference pages, and Ads Help Center.

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)