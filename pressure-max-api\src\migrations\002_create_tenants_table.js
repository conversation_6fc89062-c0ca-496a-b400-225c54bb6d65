/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('tenants', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name').notNullable();
    table.string('slug').notNullable().unique();
    table.text('description').nullable();
    table.string('logo').nullable();
    table.string('website').nullable();
    table.string('industry').nullable();
    table.json('settings').defaultTo('{}');
    table.enum('plan', ['free', 'starter', 'professional', 'enterprise']).defaultTo('free');
    table.boolean('is_active').defaultTo(true);
    table.timestamp('trial_ends_at').nullable();
    table.timestamps(true, true);

    // Indexes
    table.index('slug');
    table.index('plan');
    table.index('is_active');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('tenants');
};
