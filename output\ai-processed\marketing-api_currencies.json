{"title": "Facebook Marketing API Currency Codes Reference", "summary": "Complete reference guide for all currency codes supported by Facebook Marketing API ad accounts. Includes currency names, ISO codes, and offset values that determine how Facebook handles currency subdivisions and minimum bid calculations.", "content": "# Currency Codes\n\nThe Marketing API supports all currencies that are supported by [ad accounts](/docs/reference/ads-api/adaccount/).\n\n## Supported Currencies\n\n| Name | Code | Offset |\n|------|------|--------|\n| Algerian Dinar | DZD | 100 |\n| Argentine Peso | ARS | 100 |\n| Australian Dollar | AUD | 100 |\n| Bahraini Dinar | BHD | 100 |\n| Bangladeshi Taka | BDT | 100 |\n| Bolivian Boliviano | BOB | 100 |\n| Bulgarian Lev | BGN | 100 |\n| Brazilian Real | BRL | 100 |\n| British Pound | GBP | 100 |\n| Canadian Dollar | CAD | 100 |\n| Chilean Peso | CLP | 1 |\n| Chinese Yuan | CNY | 100 |\n| Colombian Peso | COP | 1 |\n| Costa Rican Colon | CRC | 1 |\n| Croatian Kuna | HRK | 100 |\n| Czech Koruna | CZK | 100 |\n| Danish Krone | DKK | 100 |\n| Egyptian Pound | EGP | 100 |\n| Euro | EUR | 100 |\n| Guatemalan Quetzal | GTQ | 100 |\n| Honduran Lempira | HNL | 100 |\n| Hong Kong Dollar | HKD | 100 |\n| Hungarian Forint | HUF | 1 |\n| Iceland Krona | ISK | 1 |\n| Indian Rupee | INR | 100 |\n| Indonesian Rupiah | IDR | 1 |\n| Israeli New Shekel | ILS | 100 |\n| Japanese Yen | JPY | 1 |\n| Jordanian Dinar | JOD | 100 |\n| Kenyan Shilling | KES | 100 |\n| Korean Won | KRW | 1 |\n| Latvian Lats | LVL | 100 |\n| Lithuanian Litas | LTL | 100 |\n| Macau Patacas | MOP | 100 |\n| Malaysian Ringgit | MYR | 100 |\n| Mexican Peso | MXN | 100 |\n| New Zealand Dollar | NZD | 100 |\n| Nicaraguan Cordoba | NIO | 100 |\n| Nigerian Naira | NGN | 100 |\n| Norwegian Krone | NOK | 100 |\n| Pakistani Rupee | PKR | 100 |\n| Paraguayan Guarani | PYG | 1 |\n| Peruvian Nuevo Sol | PEN | 100 |\n| Philippine Peso | PHP | 100 |\n| Polish Zloty | PLN | 100 |\n| Qatari Rials | QAR | 100 |\n| Romanian Leu | RON | 100 |\n| Russian Ruble | RUB | 100 |\n| Saudi Arabian Riyal | SAR | 100 |\n| Serbian Dinar | RSD | 100 |\n| Singapore Dollar | SGD | 100 |\n| Slovak Koruna | SKK | 100 |\n| South African Rand | ZAR | 100 |\n| Swedish Krona | SEK | 100 |\n| Swiss Franc | CHF | 100 |\n| Taiwan Dollar | TWD | 1 |\n| Thai Baht | THB | 100 |\n| Turkish Lira | TRY | 100 |\n| UAE Dirham | AED | 100 |\n| Ukrainian Hryvnia | UAH | 100 |\n| US Dollars | USD | 100 |\n| Uruguay Peso | UYU | 100 |\n| Venezuelan Bolivar | VEF | 100 |\n| Vietnamese Dong | VND | 1 |\n| Credits | FBZ | 100 |\n| Bolivar Soberano | VES | 100 |\n\n## Currency Offset\n\nEach currency has an offset value that specifies how Facebook handles currency subdivisions. This ensures that the minimum bid value of \"1\" is usable for each currency.\n\n### Offset 100 Example\n\nWhen a currency has an offset of 100, the minimum allowed bid represents 1/100 of the currency base unit.\n\n**Example:** For USD (offset 100), a bid of \"1\" equals $0.01 USD.\n\n### Offset 1 Example\n\nWhen a currency has an offset of 1, the minimum allowed bid represents the currency base unit.\n\n**Example:** For JPY (offset 1), a bid of \"1\" equals ¥1 JPY.", "keyPoints": ["Facebook Marketing API supports 70+ currencies for ad accounts worldwide", "Each currency has a specific offset value (1 or 100) that determines bid calculation", "Offset 100 means minimum bid represents 1/100 of base unit (e.g., cents for USD)", "Offset 1 means minimum bid represents the full base unit (e.g., whole yen for JPY)", "Currency codes follow standard ISO format (USD, EUR, JPY, etc.)"], "apiEndpoints": ["/docs/reference/ads-api/adaccount/"], "parameters": ["currency_code", "offset", "bid_amount"], "examples": ["USD with offset 100: bid \"1\" = $0.01", "JPY with offset 1: bid \"1\" = ¥1"], "tags": ["currency", "marketing-api", "ad-accounts", "bidding", "international", "reference"], "relatedTopics": ["Ad Accounts", "Bidding", "Campaign Management", "International Advertising", "Payment Methods"], "difficulty": "beginner", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/currencies", "processedAt": "2025-06-25T15:45:42.041Z", "processor": "openrouter-claude-sonnet-4"}