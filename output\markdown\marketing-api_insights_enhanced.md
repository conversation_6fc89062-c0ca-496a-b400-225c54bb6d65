# Facebook Marketing API Insights - Comprehensive Guide to Ad Statistics

## Summary
The Facebook Marketing API Insights provides a unified interface for retrieving ad performance statistics across campaigns, ad sets, and ads. This guide covers making API calls, handling responses, attribution windows, field expansion, sorting, and troubleshooting common issues.

## Key Points
- Insights API provides unified access to ad statistics across all Facebook advertising objects
- Supports multiple aggregation levels (account, campaign, ad set, ad) with automatic deduplication
- Attribution windows allow tracking conversions across different time periods (1d_click, 1d_view, 7d_click)
- Field expansion enables requesting insights as part of main object responses
- Proper filtering and pagination are essential for handling large datasets and avoiding timeouts

## API Endpoints
- `act_<AD_ACCOUNT_ID>/insights`
- `<CAMPAIGN_ID>/insights`
- `<ADSET_ID>/insights`
- `<AD_ID>/insights`
- `https://graph.facebook.com/v23.0/{object_id}/insights`

## Parameters
- fields
- date_preset
- level
- action_attribution_windows
- sort
- filtering
- time_range
- access_token
- use_unified_attribution_setting
- action_report_time

## Content
# Facebook Marketing API Insights

The Insights API provides a single, consistent interface to retrieve ad statistics across all Facebook advertising objects.

## Prerequisites

Before you begin, you will need:
- The `ads_read` permission
- A Meta app (see [Meta App Development](/docs/development))
- Proper tracking setup using URL Tags, Meta Pixel, or Conversions API

## Available Endpoints

The Insights API is available as an edge on any ads object:

- `act_<AD_ACCOUNT_ID>/insights` - Ad account level insights
- `<CAMPAIGN_ID>/insights` - Campaign level insights
- `<ADSET_ID>/insights` - Ad set level insights
- `<AD_ID>/insights` - Ad level insights

## Basic Usage

### Simple Campaign Statistics

Get the last 7 days performance for a campaign:

```bash
curl -G \
  -d "date_preset=last_7d" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/API_VERSION/AD_CAMPAIGN_ID/insights"
```

### Requesting Specific Fields

```bash
curl -G \
  -d "fields=impressions" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/<AD_ID>/insights"
```

**Response:**
```json
{
  "data": [
    {
      "impressions": "2466376",
      "date_start": "2009-03-28",
      "date_stop": "2016-04-01"
    }
  ],
  "paging": {
    "cursors": {
      "before": "MAZDZD",
      "after": "MAZDZD"
    }
  }
}
```

## Advanced Features

### Aggregation Levels

Aggregate results at different object levels to automatically deduplicate data:

```bash
curl -G \
  -d "level=ad" \
  -d "fields=impressions,ad_id" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/CAMPAIGN_ID/insights"
```

### Attribution Windows

Specify different attribution windows for conversion tracking:

```bash
act_10151816772662695/insights?action_attribution_windows=['1d_click','1d_view']
```

**Response includes multiple attribution windows:**
```json
{
  "actions": [
    {
      "action_type": "link_click",
      "value": 6608,
      "1d_view": 86,
      "1d_click": 6510
    }
  ]
}
```

### Field Expansion

Request insights as part of the main object response:

```bash
curl -G \
  -d "fields=insights{impressions}" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/AD_ID"
```

### Sorting Results

Sort by any field in ascending or descending order:

```bash
curl -G \
  -d "sort=reach_descending" \
  -d "level=ad" \
  -d "fields=reach" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/AD_SET_ID/insights"
```

### Filtering by Ad Labels

Get insights for ads with specific labels:

```bash
curl -G \
  -d "fields=id,name,insights{unique_clicks,cpm,total_actions}" \
  -d "level=ad" \
  -d 'filtering=[{"field":"ad.adlabels","operator":"ANY", "value":["Label Name"]}]' \
  -d 'time_range={"since":"2015-03-01","until":"2015-03-31"}' \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/AD_OBJECT_ID/insights"
```

## Working with Deleted and Archived Objects

### Archived Objects

Get insights for archived ads:

```bash
curl -G \
  -d "level=ad" \
  -d "filtering=[{'field':'ad.effective_status','operator':'IN','value':['ARCHIVED']}]" \
  -d "access_token=<ACCESS_TOKEN>" \
  "https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/insights/"
```

### Deleted Objects

Query insights on deleted objects using their IDs or status filter:

```bash
POST https://graph.facebook.com/<VERSION>/act_ID/insights?access_token=token&fields=ad_id,impressions&date_preset=lifetime&level=ad&filtering=[{"field":"ad.effective_status","operator":"IN","value":["DELETED"]}]
```

## Click Metrics Definitions

- **Link Clicks (`actions:link_click`)** - Clicks on ad links to select destinations
- **Clicks (All) (`clicks`)** - Multiple types of clicks including ad container interactions

## Troubleshooting

### Common Issues

1. **Timeouts** - Break large queries into smaller date ranges
2. **Rate Limiting** - Follow the Insights API rate limits and best practices
3. **Permission Errors** - Ensure you have access to all requested objects

### Performance Tips

- Query unique metrics separately to improve performance
- Use appropriate date ranges to avoid timeouts
- Consider using asynchronous requests for large datasets

### Ads Manager Discrepancy

Starting June 10, 2025:
- `use_unified_attribution_setting` and `action_report_time` parameters will be ignored
- API responses will match Ads Manager settings
- Use `use_unified_attribution_setting=true` for current Ads Manager behavior

## Related Resources

- [Breakdowns](/docs/marketing-api/insights/breakdowns) - Group results by dimensions
- [Action Breakdowns](/docs/marketing-api/insights/action-breakdowns) - Understanding action breakdown responses
- [Async Jobs](/docs/marketing-api/insights/async) - Handle large result sets
- [Limits and Best Practices](/docs/marketing-api/insights/best-practices/) - Optimization guidelines

## Examples
curl -G -d "date_preset=last_7d" -d "access_token=ACCESS_TOKEN" "https://graph.facebook.com/API_VERSION/AD_CAMPAIGN_ID/insights"

curl -G -d "fields=impressions" -d "access_token=ACCESS_TOKEN" "https://graph.facebook.com/v23.0/<AD_ID>/insights"

curl -G -d "level=ad" -d "fields=impressions,ad_id" "https://graph.facebook.com/v23.0/CAMPAIGN_ID/insights"

curl -G -d "sort=reach_descending" -d "level=ad" -d "fields=reach" "https://graph.facebook.com/v23.0/AD_SET_ID/insights"

---
**Tags:** facebook-marketing-api, insights, ad-statistics, attribution, performance-metrics, api-reference
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/insights
**Processed:** 2025-06-25T15:07:48.072Z