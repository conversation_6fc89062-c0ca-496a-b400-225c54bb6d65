# Automating Ad Creation

# Automating Ad Creation

Creating ads using the Marketing API involves a systematic approach that includes setting up campaigns, ad sets, and ad creatives. This document provides detailed guidance on programmatically creating these components, along with code samples to illustrate the implementation process.

## Ad Creation Endpoints

The Marketing API offers a variety of key endpoints that serve as essential tools for developers to create, manage, and analyze advertising campaigns. The primary creation endpoints include `campaigns`, `adsets`, and `ads`. Understanding these endpoints and their functionalities is crucial for both new and experienced developers looking to optimize their advertising strategies.

### The `campaigns` endpoint

The [`campaigns` endpoint](/docs/marketing-api/reference/ad-account/campaigns) is used to create and manage advertising campaigns. This endpoint allows users to set the overall objectives for their marketing efforts, such as brand awareness or conversions.

**Example API Request:**

```
curl \-X POST \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/campaigns \\
  \-F 'name=My Campaign' \\
  \-F 'objective=LINK\_CLICKS' \\
  \-F 'status=PAUSED' \\
  \-F 'access\_token=<ACCESS\_TOKEN>'
```

### The `adsets` endpoint

The [`adsets` endpoint](/docs/marketing-api/reference/ad-account/adsets) organizes ads within campaigns based on specific targeting criteria and budget allocation. This allows for more granular control over audience targeting and spending.

**Example API Request:**

```
curl \-X POST \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/adsets \\
  \-F 'name=My Ad Set' \\
  \-F 'campaign\_id=<CAMPAIGN\_ID>' \\
  \-F 'daily\_budget=1000' \\
  \-F 'targeting={"geo\_locations":{"countries":\["US"\]}}' \\
  \-F 'access\_token=<ACCESS\_TOKEN>'
```

### The `ads` endpoint

The [`ads` endpoint](/docs/marketing-api/reference/ad-account/ads) is where the actual advertisements are created, allowing you to define creative elements and link them to the appropriate ad set.

**Example API Request:**

```
curl \-X POST \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/ads \\
  \-F 'name=My Ad' \\
  \-F 'adset\_id=<AD\_SET\_ID>' \\
  \-F 'creative={"creative\_id": "<CREATIVE\_ID>"}' \\
  \-F 'status=ACTIVE' \\
  \-F 'access\_token=<ACCESS\_TOKEN>'
```

[

→

Next

Create an Ad Campaign

](/docs/marketing-api/get-started/basic-ad-creation/create-an-ad-campaign)