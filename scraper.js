const { chromium } = require('playwright');
const cliProgress = require('cli-progress');
const config = require('./config');
const ScraperUtils = require('./utils');

class FacebookMarketingAPIScraper {
  constructor() {
    this.utils = new ScraperUtils(config);
    this.browser = null;
    this.page = null;
    this.progress = null;
    this.progressBar = null;
  }

  /**
   * Initialize the scraper
   */
  async initialize() {
    console.log('🚀 Initializing Facebook Marketing API Documentation Scraper...');

    // Create output directory structure
    await this.utils.createOutputStructure(config.settings.outputDir);

    // Test AI connection if enabled
    if (config.openrouter.enabled) {
      console.log('🤖 Testing OpenRouter AI connection...');
      const aiConnected = await this.utils.testAIConnection();
      if (aiConnected) {
        console.log('✅ AI processing enabled with Claude 3.5 Haiku');
      } else {
        console.log('⚠️ AI processing disabled - continuing with basic scraping');
      }
    } else {
      console.log('ℹ️ AI processing disabled in config');
    }

    // Load progress
    this.progress = await this.utils.loadProgress(config.settings.outputDir);

    // Launch browser
    this.browser = await chromium.launch({
      headless: config.settings.headless,
      timeout: config.settings.timeout
    });

    // Create page with custom settings
    this.page = await this.browser.newPage({
      userAgent: config.settings.userAgent
    });

    // Set timeout
    this.page.setDefaultTimeout(config.settings.timeout);

    console.log('✅ Scraper initialized successfully');
  }

  /**
   * Discover additional URLs from navigation
   */
  async discoverUrls() {
    console.log('🔍 Discovering additional URLs...');
    
    const discoveredUrls = new Set();
    
    // Visit main pages to discover additional URLs
    const mainPages = [
      '/docs/marketing-apis',
      '/docs/marketing-api/reference'
    ];
    
    for (const url of mainPages) {
      try {
        await this.page.goto(config.baseUrl + url, { waitUntil: 'networkidle' });
        const content = await this.utils.extractContent(this.page, config.selectors);
        
        if (content && content.navigationLinks) {
          content.navigationLinks.forEach(link => discoveredUrls.add(link));
        }
      } catch (error) {
        console.error(`Error discovering URLs from ${url}:`, error.message);
      }
    }
    
    // Merge with existing URLs
    const allUrls = [...new Set([...config.urls, ...discoveredUrls])];
    console.log(`📋 Total URLs to scrape: ${allUrls.length}`);
    
    return allUrls;
  }

  /**
   * Scrape a single page
   */
  async scrapePage(url, retryCount = 0) {
    const fullUrl = config.baseUrl + url;
    
    try {
      // Check if already completed
      if (this.progress.completed.includes(url)) {
        return { success: true, cached: true };
      }
      
      // Navigate to page
      await this.page.goto(fullUrl, { waitUntil: 'networkidle' });
      
      // Wait a bit to ensure content is loaded
      await this.page.waitForTimeout(config.settings.delay);
      
      // Extract content
      const content = await this.utils.extractContent(this.page, config.selectors);
      
      if (!content) {
        throw new Error('Failed to extract content');
      }
      
      // Generate filename and save content
      const filename = this.utils.generateFilename(url);
      const saved = await this.utils.saveContent(content, filename, config.settings.outputDir);
      
      if (!saved) {
        throw new Error('Failed to save content');
      }
      
      // Update progress
      this.progress.completed.push(url);
      this.progress.failed = this.progress.failed.filter(failedUrl => failedUrl !== url);
      
      return { success: true, content };
      
    } catch (error) {
      console.error(`❌ Error scraping ${url}: ${error.message}`);
      
      // Retry logic
      if (retryCount < config.settings.retries) {
        console.log(`🔄 Retrying ${url} (attempt ${retryCount + 1}/${config.settings.retries})`);
        await this.page.waitForTimeout(config.settings.delay * 2); // Wait longer before retry
        return await this.scrapePage(url, retryCount + 1);
      }
      
      // Add to failed list
      if (!this.progress.failed.includes(url)) {
        this.progress.failed.push(url);
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Run the scraper
   */
  async run() {
    try {
      await this.initialize();
      
      // Discover all URLs
      const urls = await this.discoverUrls();
      
      // Filter out already completed URLs
      const remainingUrls = urls.filter(url => !this.progress.completed.includes(url));
      
      if (remainingUrls.length === 0) {
        console.log('✅ All URLs have already been scraped!');
        await this.createFinalManifest();
        return;
      }
      
      console.log(`📄 Scraping ${remainingUrls.length} remaining pages...`);
      
      // Initialize progress bar
      this.progressBar = new cliProgress.SingleBar({
        format: 'Progress |{bar}| {percentage}% | {value}/{total} | Current: {current_url}',
        barCompleteChar: '\u2588',
        barIncompleteChar: '\u2591',
        hideCursor: true
      });
      
      this.progressBar.start(remainingUrls.length, 0, { current_url: 'Starting...' });
      
      // Scrape each URL
      let completed = 0;
      for (const url of remainingUrls) {
        this.progressBar.update(completed, { current_url: url });
        
        const result = await this.scrapePage(url);
        
        if (result.success) {
          completed++;
          if (!result.cached) {
            console.log(`\n✅ Scraped: ${url}`);
          }
        } else {
          console.log(`\n❌ Failed: ${url} - ${result.error}`);
        }
        
        // Save progress periodically
        if (completed % 10 === 0) {
          await this.utils.saveProgress(this.progress, config.settings.outputDir);
        }
        
        // Add delay between requests
        await this.page.waitForTimeout(config.settings.delay);
      }
      
      this.progressBar.stop();
      
      // Save final progress
      await this.utils.saveProgress(this.progress, config.settings.outputDir);
      
      // Create manifest
      await this.createFinalManifest();
      
      // Print summary
      this.printSummary();
      
    } catch (error) {
      console.error('💥 Scraper failed:', error.message);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Create final manifest
   */
  async createFinalManifest() {
    console.log('\n📋 Creating manifest...');
    const manifest = await this.utils.createManifest(config.settings.outputDir);
    
    if (manifest) {
      console.log(`✅ Manifest created with ${manifest.totalPages} pages`);
    }
  }

  /**
   * Print scraping summary
   */
  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 SCRAPING SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Successfully scraped: ${this.progress.completed.length} pages`);
    console.log(`❌ Failed to scrape: ${this.progress.failed.length} pages`);

    if (config.openrouter.enabled) {
      console.log(`🤖 AI processing: ${config.openrouter.enabled ? 'Enabled' : 'Disabled'}`);
      console.log(`🧠 AI model: ${config.openrouter.model}`);
    }

    if (this.progress.failed.length > 0) {
      console.log('\n❌ Failed URLs:');
      this.progress.failed.forEach(url => console.log(`   - ${url}`));
    }

    console.log(`\n📁 Output directory: ${config.settings.outputDir}`);
    console.log('📋 Files generated:');
    console.log('   - JSON files: ./output/json/');
    console.log('   - HTML files: ./output/html/');
    console.log('   - Markdown files: ./output/markdown/');
    if (config.openrouter.enabled) {
      console.log('   - AI-processed files: ./output/ai-processed/');
      console.log('   - Enhanced markdown: ./output/markdown/*_enhanced.md');
    }
    console.log('   - Manifest: ./output/manifest.json');
    console.log('   - Progress: ./output/progress.json');

    console.log('\n🎯 Perfect for AI agent consumption!');
    if (config.openrouter.enabled) {
      console.log('   - Structured JSON with summaries and key points');
      console.log('   - Enhanced markdown with metadata');
      console.log('   - Extracted API endpoints and parameters');
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  /**
   * Test mode - scrape only a few pages
   */
  async test() {
    console.log('🧪 Running in test mode...');
    
    const testUrls = [
      '/docs/marketing-apis',
      '/docs/marketing-apis/overview',
      '/docs/marketing-api/get-started'
    ];
    
    await this.initialize();
    
    for (const url of testUrls) {
      console.log(`Testing: ${url}`);
      const result = await this.scrapePage(url);
      console.log(`Result: ${result.success ? '✅ Success' : '❌ Failed'}`);
    }
    
    await this.cleanup();
  }
}

// Main execution
async function main() {
  const scraper = new FacebookMarketingAPIScraper();
  
  // Check for test mode
  if (process.argv.includes('--test')) {
    await scraper.test();
  } else {
    await scraper.run();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received interrupt signal. Cleaning up...');
  process.exit(0);
});

// Run the scraper
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = FacebookMarketingAPIScraper;
