<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Marketing API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_tB"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_fH"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_GG"></div></span></div></div>

<h1 id="ad-preview-plugin">Ad Preview Plugin</h1>

<p>The Ad Preview plugin is the easiest way for advertisers to preview ads on their own websites.</p>

<p>The plugin enables you to generate Right Hand Column, Feed, or Mobile previews of an ad by specifying a Creative Spec, Adgroup ID or Creative ID. Previews can either be generated using a Social Plugin or through the Graph API.</p>


<h2 id="params">Parameters</h2>

<ul>
<li>Required: One of <code>creative</code>, <code>creative_id</code>, or <code>adgroup_id</code></li>
<li>Required: <code>ad_format</code>, which replaces <code>page_type</code> parameter</li>
<li>Optional: <code>ad_account_id</code>, <code>targeting</code>, <code>post</code></li>
</ul>

<p>The preview plugin requires you to be logged in with Facebook Login. If <code>creative_id</code>, <code>adgroup_id</code>, or <code>ad_account_id</code> is used, you must also have the permissions to access the Creative, Ad Group, or Ad Account respectively.</p>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>
Setting
</th><th>
HTML5 Attribute
</th><th>
Description
</th></tr></thead><tbody class="_5m37" id="u_0_5_nf"><tr class="row_0"><td><p><code>ad_account_id</code></p>
</td><td><p><code>data-ad-account-id</code></p>
</td><td><p>Required when specifying a creative that uses <code>image_hash</code></p>
</td></tr><tr class="row_1 _5m29"><td><p><code>adgroup_id</code></p>
</td><td><p><code>data-adgroup-id</code></p>
</td><td><p>Adgroup ID returned from a Graph API call.</p>
</td></tr><tr class="row_2"><td><p><code>creative</code></p>
</td><td><p><code>data-creative</code></p>
</td><td><p>JSON-encoded <a href="/docs/reference/ads-api/adcreative/">creative spec</a>.</p>
</td></tr><tr class="row_3 _5m29"><td><p><code>creative_id</code></p>
</td><td><p><code>data-creative-id</code></p>
</td><td><p>Creative ID returned from a Graph API call.</p>
</td></tr><tr class="row_4"><td><p><code>ad_format</code></p>
</td><td><p><code>data-ad-format</code></p>
</td><td><p>One of: <code>RIGHT_COLUMN_STANDARD</code>, <code>DESKTOP_FEED_STANDARD</code>, <code>MOBILE_FEED_STANDARD</code>, or <code>FACEBOOK_STORY_MOBILE</code>.</p>
</td></tr><tr class="row_5 _5m29"><td><p><code>page_type</code></p>
</td><td><p><code>data-page-type</code></p>
</td><td><p>One of: <code>rightcolumn</code>, <code>desktopfeed</code>, or <code>mobile</code>.</p>
</td></tr><tr class="row_6"><td><p><code>targeting</code></p>
</td><td><p><code>data-targeting</code></p>
</td><td><p>JSON-encoded <a href="/docs/ads-api/targeting">targeting spec</a>.</p>
</td></tr><tr class="row_7 _5m29"><td><p><code>post</code></p>
</td><td><p><code>data-post</code></p>
</td><td><p>JSON-encoded post specification according to the <a href="/docs/graph-api/reference/page">Pages API documentation</a>.</p>
</td></tr></tbody></table></div>

<h2 id="graphapi">Graph API</h2>

<p>Previews can also be generated using the <a href="/docs/reference/ads-api/generatepreview/">Graph API</a>. To generate a plugin-style preview, simply specify the additional parameter, <code>ad_format</code>, as described in the table above.</p>


