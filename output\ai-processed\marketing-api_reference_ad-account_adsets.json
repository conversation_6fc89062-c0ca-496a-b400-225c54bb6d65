{"title": "Facebook Marketing API - Ad Account Adsets Reference", "summary": "Complete reference documentation for managing ad sets within Facebook ad accounts, including reading, creating, updating, and deleting ad sets through the Marketing API. Covers all parameters, fields, examples, and error codes for ad set operations.", "content": "# Ad Account Adsets\n\n## Overview\n\nDue to the iOS 14.5 launch, changes have been made to this endpoint:\n- Mobile App Custom Audiences for inclusion targeting is no longer supported for the `POST /{ad-account-id}/adsets` endpoint for iOS 14.5 SKAdNetwork campaigns\n- New iOS 14.5 app install campaigns will no longer be able to use app connections targeting\n\n## Reading Ad Sets\n\nRetrieve the ad sets of an ad account.\n\n### Endpoint\n```\nGET /v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Example Request\n```bash\ncurl -X GET -G \\\n  -d 'fields=\"name,id,status\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Query Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `date_preset` | enum | Predefined date range for aggregating insights metrics |\n| `effective_status` | list<enum> | Filter by effective status (ACTIVE, PAUSED, DELETED, etc.) |\n| `is_completed` | boolean | Filter ad sets by completed status |\n| `time_range` | object | Custom date range with 'since' and 'until' fields |\n| `updated_since` | integer | Time since the ad set has been updated |\n\n### Response Fields\n\nThe response returns a JSON object with:\n- `data`: Array of AdSet nodes\n- `paging`: Pagination information\n- `summary`: Aggregated information (insights, total_count)\n\n## Creating Ad Sets\n\nCreate a new ad set within an ad account.\n\n### Endpoint\n```\nPOST /v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Example Request\n```bash\ncurl -X POST \\\n  -F 'name=\"My First AdSet\"' \\\n  -F 'daily_budget=10000' \\\n  -F 'bid_amount=300' \\\n  -F 'billing_event=\"IMPRESSIONS\"' \\\n  -F 'optimization_goal=\"REACH\"' \\\n  -F 'campaign_id=\"<AD_CAMPAIGN_ID>\"' \\\n  -F 'promoted_object={\"page_id\":\"<PAGE_ID>\"}' \\\n  -F 'targeting={\"geo_locations\":{\"countries\":[\"US\"]},\"age_min\":20,\"age_max\":24}' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Required Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `name` | string | Ad set name (max 400 characters) |\n| `campaign_id` | numeric string | The ad campaign ID to add this ad set to |\n| `targeting` | object | Targeting specifications (countries required) |\n| `optimization_goal` | enum | What the ad set optimizes for |\n| `billing_event` | enum | The billing event (IMPRESSIONS, CLICKS, etc.) |\n\n### Budget Parameters\n\nEither `daily_budget` or `lifetime_budget` must be specified:\n- `daily_budget`: Daily budget in account currency\n- `lifetime_budget`: Lifetime budget (requires `end_time`)\n\n### Bidding Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `bid_strategy` | enum | Bidding strategy (LOWEST_COST_WITHOUT_CAP, etc.) |\n| `bid_amount` | integer | Bid cap or target cost |\n\n### Targeting Object\n\nThe targeting object supports:\n- Geographic targeting (`geo_locations`)\n- Demographic targeting (`age_min`, `age_max`, `genders`)\n- Interest targeting (`flexible_spec`)\n- Platform targeting (`publisher_platforms`, `device_platforms`)\n- Placement targeting (`facebook_positions`)\n\n### Promoted Object\n\nRequired for certain campaign objectives:\n- **CONVERSIONS**: `pixel_id`, `custom_event_type`\n- **PAGE_LIKES**: `page_id`\n- **APP_INSTALLS**: `application_id`, `object_store_url`\n- **LEAD_GENERATION**: `page_id`\n\n### Optimization Goals\n\nSupported optimization goals include:\n- `APP_INSTALLS`: Optimize for app installations\n- `LINK_CLICKS`: Optimize for link clicks\n- `IMPRESSIONS`: Maximize impressions\n- `REACH`: Optimize for unique reach\n- `CONVERSIONS`: Optimize for conversions\n- `THRUPLAY`: Optimize for video completion\n\n### Response\n\nReturns an object with:\n```json\n{\n  \"id\": \"<ADSET_ID>\",\n  \"success\": true\n}\n```\n\n## Updating Ad Sets\n\nUpdating ad sets is not supported on this endpoint.\n\n## Deleting Ad Sets\n\nDeleting ad sets has been deprecated with Marketing API V8.\n\n## Error Codes\n\nCommon error codes:\n- `100`: Invalid parameter\n- `200`: Permissions error\n- `190`: Invalid OAuth 2.0 Access Token\n- `80004`: Rate limit exceeded\n- `2641`: Restricted locations in targeting\n- `368`: Abusive or disallowed action", "keyPoints": ["iOS 14.5 changes affect mobile app custom audiences and app connections targeting", "Either daily_budget or lifetime_budget must be specified when creating ad sets", "Targeting object requires 'countries' field at minimum", "Promoted object requirements vary by campaign objective", "Updating and deleting operations are not supported on this endpoint"], "apiEndpoints": ["GET /v23.0/act_<AD_ACCOUNT_ID>/adsets", "POST /v23.0/act_<AD_ACCOUNT_ID>/adsets"], "parameters": ["name", "campaign_id", "targeting", "optimization_goal", "billing_event", "daily_budget", "lifetime_budget", "bid_strategy", "bid_amount", "promoted_object", "status", "start_time", "end_time"], "examples": ["GET request to retrieve ad sets with fields", "POST request to create ad set with targeting and budget", "Targeting object with geo_locations and demographics", "Promoted object for different campaign objectives"], "tags": ["Facebook Marketing API", "Ad Sets", "Advertising", "Campaign Management", "Targeting", "Bidding", "iOS 14.5"], "relatedTopics": ["Ad Campaigns", "Targeting Specifications", "Bidding Strategies", "Optimization Goals", "Billing Events", "Promoted Objects", "iOS 14.5 Changes", "Rate Limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/", "processedAt": "2025-06-25T15:18:32.994Z", "processor": "openrouter-claude-sonnet-4"}