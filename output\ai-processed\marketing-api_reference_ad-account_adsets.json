{"title": "Facebook Marketing API - Ad Account Adsets Reference", "summary": "Complete reference documentation for managing ad sets within Facebook ad accounts, including reading, creating, updating, and deleting ad sets through the Marketing API. Covers all parameters, fields, examples, and error codes for ad set operations.", "content": "# Ad Account Adsets\n\n## Overview\n\nDue to the iOS 14.5 launch, changes have been made to this endpoint:\n- Mobile App Custom Audiences for inclusion targeting is no longer supported for the `POST /{ad-account-id}/adsets` endpoint for iOS 14.5 SKAdNetwork campaigns\n- New iOS 14.5 app install campaigns will no longer be able to use app connections targeting\n\n## Reading Ad Sets\n\nRetrieve the ad sets of an ad account.\n\n### Endpoint\n```\nGET /v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Example Request\n```bash\ncurl -X GET -G \\\n  -d 'fields=\"name,id,status\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Query Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `date_preset` | enum | Predefined date range for aggregating insights metrics |\n| `effective_status` | list<enum> | Filter by effective status (ACTIVE, PAUSED, DELETED, etc.) |\n| `is_completed` | boolean | Filter ad sets by completed status |\n| `time_range` | object | Custom date range with 'since' and 'until' fields |\n| `updated_since` | integer | Time since the ad set has been updated |\n\n### Response Fields\n\nThe response returns a JSON object with:\n- `data`: Array of AdSet nodes\n- `paging`: Pagination information\n- `summary`: Aggregated information (insights, total_count)\n\n## Creating Ad Sets\n\nCreate a new ad set within an ad account.\n\n### Important Notes\n- Mobile App Install CPA Billing is no longer supported\n- The billing event cannot be App Install if the optimization goal is App Install\n\n### Endpoint\n```\nPOST /v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Example Request\n```bash\ncurl -X POST \\\n  -F 'name=\"My First AdSet\"' \\\n  -F 'daily_budget=10000' \\\n  -F 'bid_amount=300' \\\n  -F 'billing_event=\"IMPRESSIONS\"' \\\n  -F 'optimization_goal=\"REACH\"' \\\n  -F 'campaign_id=\"<AD_CAMPAIGN_ID>\"' \\\n  -F 'promoted_object={\"page_id\":\"<PAGE_ID>\"}' \\\n  -F 'targeting={\"facebook_positions\":[\"feed\"],\"geo_locations\":{\"countries\":[\"US\"]}}' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Required Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `name` | string | Ad set name (max 400 characters) |\n| `campaign_id` | numeric string | The ad campaign ID to add this ad set to |\n| `daily_budget` or `lifetime_budget` | int64 | Budget in account currency |\n| `billing_event` | enum | How you pay for ads (IMPRESSIONS, CLICKS, etc.) |\n| `optimization_goal` | enum | What the ad set optimizes for |\n| `targeting` | object | Targeting specifications |\n\n### Key Optional Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `bid_amount` | integer | Bid cap or target cost |\n| `bid_strategy` | enum | Bidding strategy (LOWEST_COST_WITHOUT_CAP, etc.) |\n| `start_time` | datetime | When the ad set starts |\n| `end_time` | datetime | When the ad set ends |\n| `promoted_object` | object | What the ad set promotes |\n| `attribution_spec` | list | Conversion attribution specifications |\n| `frequency_control_specs` | list | Frequency control settings |\n\n### Bid Strategies\n\n- `LOWEST_COST_WITHOUT_CAP`: Get most results for budget without bid limits\n- `LOWEST_COST_WITH_BID_CAP`: Get most results while limiting bid amount\n- `COST_CAP`: Maintain stable costs while optimizing\n\n### Billing Events\n\n- `APP_INSTALLS`: Pay when people install your app\n- `CLICKS`: Deprecated\n- `IMPRESSIONS`: Pay when ads are shown\n- `LINK_CLICKS`: Pay when people click ad links\n- `THRUPLAY`: Pay for 15+ second video views or completions\n\n### Optimization Goals\n\n- `APP_INSTALLS`: Optimize for app installations\n- `CONVERSIONS`: Optimize for website conversions\n- `LINK_CLICKS`: Optimize for link clicks\n- `REACH`: Optimize for unique reach\n- `IMPRESSIONS`: Maximize impressions\n- `THRUPLAY`: Optimize for video completion\n\n### Targeting Object\n\nThe targeting object supports:\n- Geographic targeting (countries, regions, cities)\n- Demographic targeting (age, gender)\n- Interest targeting\n- Behavioral targeting\n- Custom audiences\n- Lookalike audiences\n\n### Response\n\nReturns an object with:\n```json\n{\n  \"id\": \"<ADSET_ID>\",\n  \"success\": true\n}\n```\n\n## Error Codes\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 613 | Rate limit exceeded |\n| 80004 | Too many calls to ad account |\n| 2641 | Restricted locations in targeting |\n| 368 | Abusive or disallowed action |\n| 2695 | Campaign group limit reached |\n\n## Updating and Deleting\n\n- **Updating**: Not supported on this endpoint\n- **Deleting**: Deprecated as of Marketing API V8", "keyPoints": ["iOS 14.5 changes affect mobile app custom audiences and app connections targeting", "Either daily_budget or lifetime_budget must be specified when creating ad sets", "Bid strategy and optimization goal work together to determine ad delivery", "Targeting object is required and must include at least country-level geographic targeting", "Mobile App Install CPA billing is no longer supported"], "apiEndpoints": ["GET /v23.0/act_<AD_ACCOUNT_ID>/adsets", "POST /v23.0/act_<AD_ACCOUNT_ID>/adsets"], "parameters": ["name", "campaign_id", "daily_budget", "lifetime_budget", "billing_event", "optimization_goal", "targeting", "bid_amount", "bid_strategy", "promoted_object", "start_time", "end_time", "status"], "examples": ["GET request to retrieve ad sets with name, id, and status fields", "POST request to create ad set with targeting, budget, and optimization settings", "Targeting object with geographic, demographic, and interest specifications", "Promoted object configuration for different campaign objectives"], "tags": ["Facebook Marketing API", "Ad Sets", "Advertising", "Campaign Management", "Targeting", "Bidding", "iOS 14.5"], "relatedTopics": ["Ad Campaigns", "Ad Creative", "Targeting Specifications", "Bidding Strategies", "Campaign Objectives", "Attribution", "Frequency Control", "Budget Management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/", "processedAt": "2025-06-25T16:21:42.946Z", "processor": "openrouter-claude-sonnet-4"}