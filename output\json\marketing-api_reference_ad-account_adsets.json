{"title": "Ad Account Adsets", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_13_4U\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Ad Account Adsets</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#fields\">Fields</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example-2\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-2\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-2\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_D6\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_EK\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_T0\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_bs\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Ad Account Adsets</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><div class=\"_57yz _5s-k _3-8p\"><div class=\"_57y-\"><p>Due to the iOS 14.5 launch, changes have been made to this endpoint.</p>\n\n<ul>\n<li>Mobile App Custom Audiences for inclusion targeting is no longer supported for the <code>POST /{ad-account-id}/adsets</code> endpoint for iOS 14.5 SKAdNetwork campaigns.</li>\n<li>New iOS 14.5 app install campaigns will no longer be able to use app connections targeting.</li>\n</ul>\n</div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>The adsets of this ad account</p>\n</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_MT\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_Gc\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_yU\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_uV\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_PF\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_se\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_b_TE\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets%3Ffields%3Dname%252Cid%252Cstatus&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_c_lF\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/adsets?fields=name%2Cid%2Cstatus HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_d_th\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"kwd\">get</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;/adsets?fields=name%2Cid%2Cstatus'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_e_nE\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/adsets\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"name,id,status\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_f_xI\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"fields\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"name,id,status\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/adsets\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">GET</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_g_gb\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"name,id,status\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/adsets\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"GET\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_h_SA\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=\"name,id,status\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/adsets</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_i_dY\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>date_preset</code></span></div><div class=\"_yb\">enum {TODAY, YESTERDAY, THIS_MONTH, LAST_MONTH, THIS_QUARTER, MAXIMUM, DATA_MAXIMUM, LAST_3D, LAST_7D, LAST_14D, LAST_28D, LAST_30D, LAST_90D, LAST_WEEK_MON_SUN, LAST_WEEK_SUN_SAT, LAST_QUARTER, LAST_YEAR, THIS_WEEK_MON_TODAY, THIS_WEEK_SUN_TODAY, THIS_YEAR}</div></td><td><p class=\"_yd\"></p><div><div><p>Predefine date range used to aggregate insights metrics</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>effective_status</code></span></div><div class=\"_yb\">list&lt;enum{ACTIVE, PAUSED, DELETED, PENDING_REVIEW, DISAPPROVED, PREAPPROVED, PENDING_BILLING_INFO, CAMPAIGN_PAUSED, ARCHIVED, ADSET_PAUSED, IN_PROCESS, WITH_ISSUES}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Effective status of adset</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>is_completed</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>Filter adset by completed status</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>time_range</code></span></div><div class=\"_yb\">{'since':YYYY-MM-DD,'until':YYYY-MM-DD}</div></td><td><p class=\"_yd\"></p><div><div><p>Date range used to aggregate insights metrics</p>\n</div></div><p></p></td></tr><tr class=\"row_3-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>since</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means from the beginning midnight of that day.</p>\n</div></div><p></p></td></tr><tr class=\"row_3-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>until</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means to the beginning midnight of the following day.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>updated_since</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>Time since the Adset has been updated.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class=\"_3hux\"><p>{\n    \"<code>data</code>\": [],\n    \"<code>paging</code>\": {},\n    \"<code>summary</code>\": {}\n}</p>\n</pre><div class=\"_3-8o\"><h4><code>data</code></h4>A list of <a target=\"_blank\" href=\"/docs/marketing-api/reference/ad-campaign/\">AdSet</a> nodes.</div><div class=\"_3-8o\"><h4><code>paging</code></h4>For more details about pagination, see the <a href=\"/docs/graph-api/using-graph-api/#paging\">Graph API guide</a>.</div><div class=\"_3-8o\"><h4><code>summary</code></h4><p>Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like <code>summary=insights</code>).</p><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>insights</code></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdsInsights&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>Analytics summary for all objects. Use <a href=\"https://developers.facebook.com/docs/graph-api/advanced#fieldexpansion\">nested parameters</a> with this field.\n<code>insights.time_range({'until':'2018-01-01', 'since':'2017-12-12'}).time_increment(1)</code></p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>total_count</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><p>Total number of objects</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr></tbody></table></div></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>3018</td><td>The start date of the time range cannot be beyond 37 months from the current date</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div><span><div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>Mobile App Install CPA Billing will no longer be supported. The <a href=\"https://developers.facebook.com/docs/marketing-api/bidding/overview/billing-events\">billing event</a> cannot be App Install if the Optimization goal is App Install.</p>\n</div></div></span></div><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>adsets</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/adsets/\"><code>/act_{ad_account_id}/adsets</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-campaign/\">AdSet</a> will be created.</div><div><h3 id=\"example-2\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_j_iV\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_k_WW\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_l_BY\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_m_mM\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_n_WI\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_o_YE\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_p_Oj\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets%3Fname%3DMy%2BFirst%2BAdSet%26daily_budget%3D10000%26bid_amount%3D300%26billing_event%3DIMPRESSIONS%26optimization_goal%3DREACH%26campaign_id%3D%253CAD_CAMPAIGN_ID%253E%26promoted_object%3D%257B%2522page_id%2522%253A%2522%253CPAGE_ID%253E%2522%257D%26targeting%3D%257B%2522facebook_positions%2522%253A%255B%2522feed%2522%255D%252C%2522geo_locations%2522%253A%257B%2522countries%2522%253A%255B%2522US%2522%255D%252C%2522regions%2522%253A%255B%257B%2522key%2522%253A%********%2522%257D%255D%252C%2522cities%2522%253A%255B%257B%2522key%2522%253A777934%252C%2522radius%2522%253A10%252C%2522distance_unit%2522%253A%2522mile%2522%257D%255D%257D%252C%2522genders%2522%253A%255B1%255D%252C%2522age_max%2522%253A24%252C%2522age_min%2522%253A20%252C%2522publisher_platforms%2522%253A%255B%2522facebook%2522%252C%2522audience_network%2522%255D%252C%2522device_platforms%2522%253A%255B%2522mobile%2522%255D%252C%2522flexible_spec%2522%253A%255B%257B%2522interests%2522%253A%255B%257B%2522id%2522%253A%2522%253CINTEREST_ID%253E%2522%252C%2522name%2522%253A%2522%253CINTEREST_NAME%253E%2522%257D%255D%257D%255D%257D%26status%3DPAUSED&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_q_Qy\" style=\"\"><code><span class=\"pln\">POST </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/adsets HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com\n\nname</span><span class=\"pun\">=</span><span class=\"typ\">My</span><span class=\"pun\">+</span><span class=\"typ\">First</span><span class=\"pun\">+</span><span class=\"typ\">AdSet</span><span class=\"pun\">&amp;</span><span class=\"pln\">daily_budget</span><span class=\"pun\">=</span><span class=\"lit\">10000</span><span class=\"pun\">&amp;</span><span class=\"pln\">bid_amount</span><span class=\"pun\">=</span><span class=\"lit\">300</span><span class=\"pun\">&amp;</span><span class=\"pln\">billing_event</span><span class=\"pun\">=</span><span class=\"pln\">IMPRESSIONS</span><span class=\"pun\">&amp;</span><span class=\"pln\">optimization_goal</span><span class=\"pun\">=</span><span class=\"pln\">REACH</span><span class=\"pun\">&amp;</span><span class=\"pln\">campaign_id</span><span class=\"pun\">=%</span><span class=\"lit\">3CAD</span><span class=\"pln\">_CAMPAIGN_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">&amp;</span><span class=\"pln\">promoted_object</span><span class=\"pun\">=%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22page</span><span class=\"pln\">_id</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3CPAGE</span><span class=\"pln\">_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">&amp;</span><span class=\"pln\">targeting</span><span class=\"pun\">=%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22facebook</span><span class=\"pln\">_positions</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22feed</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22geo</span><span class=\"pln\">_locations</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22countries</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22US</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22regions</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22key</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">224081</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22cities</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22key</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A777934</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22radius</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A10</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22distance</span><span class=\"pln\">_unit</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22mile</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22genders</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B1</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22age</span><span class=\"pln\">_max</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A24</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22age</span><span class=\"pln\">_min</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A20</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22publisher</span><span class=\"pln\">_platforms</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22facebook</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22audience</span><span class=\"pln\">_network</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22device</span><span class=\"pln\">_platforms</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22mobile</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22flexible</span><span class=\"pln\">_spec</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22interests</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22id</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3CINTEREST</span><span class=\"pln\">_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22name</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3CINTEREST</span><span class=\"pln\">_NAME</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">&amp;</span><span class=\"pln\">status</span><span class=\"pun\">=</span><span class=\"pln\">PAUSED</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_r_Md\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"pln\">post</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;/adsets'</span><span class=\"pun\">,</span><span class=\"pln\">\n    array </span><span class=\"pun\">(</span><span class=\"pln\">\n      </span><span class=\"str\">'name'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'My First AdSet'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'daily_budget'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'10000'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'bid_amount'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'300'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'billing_event'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'IMPRESSIONS'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'optimization_goal'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'REACH'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'campaign_id'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'&lt;AD_CAMPAIGN_ID&gt;'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'promoted_object'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'{\"page_id\":\"&lt;PAGE_ID&gt;\"}'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'targeting'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'{\"facebook_positions\":[\"feed\"],\"geo_locations\":{\"countries\":[\"US\"],\"regions\":[{\"key\":\"4081\"}],\"cities\":[{\"key\":777934,\"radius\":10,\"distance_unit\":\"mile\"}]},\"genders\":[1],\"age_max\":24,\"age_min\":20,\"publisher_platforms\":[\"facebook\",\"audience_network\"],\"device_platforms\":[\"mobile\"],\"flexible_spec\":[{\"interests\":[{\"id\":\"&lt;INTEREST_ID&gt;\",\"name\":\"&lt;INTEREST_NAME&gt;\"}]}]}'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'status'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'PAUSED'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">),</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_s_9c\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/adsets\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"POST\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"My First AdSet\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"daily_budget\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"10000\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"bid_amount\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"300\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"billing_event\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"IMPRESSIONS\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"optimization_goal\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"REACH\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"campaign_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;AD_CAMPAIGN_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"promoted_object\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"{\\\"page_id\\\":\\\"&lt;PAGE_ID&gt;\\\"}\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"targeting\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"{\\\"facebook_positions\\\":[\\\"feed\\\"],\\\"geo_locations\\\":{\\\"countries\\\":[\\\"US\\\"],\\\"regions\\\":[{\\\"key\\\":\\\"4081\\\"}],\\\"cities\\\":[{\\\"key\\\":777934,\\\"radius\\\":10,\\\"distance_unit\\\":\\\"mile\\\"}]},\\\"genders\\\":[1],\\\"age_max\\\":24,\\\"age_min\\\":20,\\\"publisher_platforms\\\":[\\\"facebook\\\",\\\"audience_network\\\"],\\\"device_platforms\\\":[\\\"mobile\\\"],\\\"flexible_spec\\\":[{\\\"interests\\\":[{\\\"id\\\":\\\"&lt;INTEREST_ID&gt;\\\",\\\"name\\\":\\\"&lt;INTEREST_NAME&gt;\\\"}]}]}\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"status\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"PAUSED\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_t_BC\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"name\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"My First AdSet\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"daily_budget\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"10000\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"bid_amount\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"300\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"billing_event\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"IMPRESSIONS\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"optimization_goal\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"REACH\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"campaign_id\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;AD_CAMPAIGN_ID&gt;\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"promoted_object\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"{\\\"page_id\\\":\\\"&lt;PAGE_ID&gt;\\\"}\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"targeting\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"{\\\"facebook_positions\\\":[\\\"feed\\\"],\\\"geo_locations\\\":{\\\"countries\\\":[\\\"US\\\"],\\\"regions\\\":[{\\\"key\\\":\\\"4081\\\"}],\\\"cities\\\":[{\\\"key\\\":777934,\\\"radius\\\":10,\\\"distance_unit\\\":\\\"mile\\\"}]},\\\"genders\\\":[1],\\\"age_max\\\":24,\\\"age_min\\\":20,\\\"publisher_platforms\\\":[\\\"facebook\\\",\\\"audience_network\\\"],\\\"device_platforms\\\":[\\\"mobile\\\"],\\\"flexible_spec\\\":[{\\\"interests\\\":[{\\\"id\\\":\\\"&lt;INTEREST_ID&gt;\\\",\\\"name\\\":\\\"&lt;INTEREST_NAME&gt;\\\"}]}]}\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"status\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"PAUSED\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/adsets\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">POST</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_u_0c\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"My First AdSet\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"daily_budget\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"10000\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"bid_amount\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"300\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"billing_event\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"IMPRESSIONS\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"optimization_goal\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"REACH\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"campaign_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"&lt;AD_CAMPAIGN_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"promoted_object\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"{\\\"page_id\\\":\\\"&lt;PAGE_ID&gt;\\\"}\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"targeting\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"{\\\"facebook_positions\\\":[\\\"feed\\\"],\\\"geo_locations\\\":{\\\"countries\\\":[\\\"US\\\"],\\\"regions\\\":[{\\\"key\\\":\\\"4081\\\"}],\\\"cities\\\":[{\\\"key\\\":777934,\\\"radius\\\":10,\\\"distance_unit\\\":\\\"mile\\\"}]},\\\"genders\\\":[1],\\\"age_max\\\":24,\\\"age_min\\\":20,\\\"publisher_platforms\\\":[\\\"facebook\\\",\\\"audience_network\\\"],\\\"device_platforms\\\":[\\\"mobile\\\"],\\\"flexible_spec\\\":[{\\\"interests\\\":[{\\\"id\\\":\\\"&lt;INTEREST_ID&gt;\\\",\\\"name\\\":\\\"&lt;INTEREST_NAME&gt;\\\"}]}]}\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"status\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"PAUSED\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/adsets\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"POST\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_v_oE\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"My First AdSet\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'daily_budget=10000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'bid_amount=300'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'billing_event=\"IMPRESSIONS\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'optimization_goal=\"REACH\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'campaign_id=\"&lt;AD_CAMPAIGN_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'promoted_object={\n       \"page_id\": \"&lt;PAGE_ID&gt;\"\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'targeting={\n       \"facebook_positions\": [\n         \"feed\"\n       ],\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ],\n         \"regions\": [\n           {\n             \"key\": \"4081\"\n           }\n         ],\n         \"cities\": [\n           {\n             \"key\": 777934,\n             \"radius\": 10,\n             \"distance_unit\": \"mile\"\n           }\n         ]\n       },\n       \"genders\": [\n         1\n       ],\n       \"age_max\": 24,\n       \"age_min\": 20,\n       \"publisher_platforms\": [\n         \"facebook\",\n         \"audience_network\"\n       ],\n       \"device_platforms\": [\n         \"mobile\"\n       ],\n       \"flexible_spec\": [\n         {\n           \"interests\": [\n             {\n               \"id\": \"&lt;INTEREST_ID&gt;\",\n               \"name\": \"&lt;INTEREST_NAME&gt;\"\n             }\n           ]\n         }\n       ]\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=\"PAUSED\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/adsets</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_w_iO\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>adlabels</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Specifies list of labels to be associated with this object. This field is optional</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>adset_schedule</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Ad set schedule, representing a delivery schedule for a single day</p>\n</div></div><p></p></td></tr><tr class=\"row_1-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>start_minute</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>A 0 based minute of the day representing when the schedule starts</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>end_minute</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>A 0 based minute of the day representing when the schedule ends</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>days</code></span></div><div class=\"_yb\">list&lt;int64&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Array of ints representing which days the schedule is active. Valid values are 0-6 with 0 representing Sunday, 1 representing Monday, ... and 6 representing Saturday.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1-3 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>timezone_type</code></span></div><div class=\"_yb\">enum {USER, ADVERTISER}</div></td><td><div>Default value: <code>USER</code></div><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_2 _5m27\"><td><div class=\"_yc\"><span><code>attribution_spec</code></span></div><div class=\"_yb\">list&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Conversion attribution spec used for attributing conversions for optimization. Supported window lengths differ by optimization goal and campaign objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_2-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event_type</code></span></div><div class=\"_yb\">enum {CLICK_THROUGH, VIEW_THROUGH, ENGAGED_VIDEO_VIEW}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_2-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>window_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>bid_amount</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>Bid cap or target cost for this ad set. The bid cap used in a <em>lowest cost bid strategy</em> is defined as the maximum bid you want to pay for a result based on your <code>optimization_goal</code>. The target cost used in a <em>target cost bid strategy</em> lets Facebook bid to meet your target on average and keep costs stable as you spend. If an ad level <code>bid_amount</code> is specified, updating this value will overwrite the previous ad level bid. Unless you are using <a href=\"/docs/marketing-api/reachandfrequency\">Reach and Frequency</a>, <code>bid_amount</code> is required if <code>bid_strategy</code> is set to <code>LOWEST_COST_WITH_BID_CAP</code> or <code>COST_CAP</code>.\n<br>\n        The bid amount's unit is cents for currencies like USD, EUR, and the basic unit for currencies like JPY, KRW. The bid amount for ads with <code>IMPRESSION</code> or <code>REACH</code> as <code>billing_event</code> is per 1,000 occurrences, and has to be at least 2 US cents or more. For ads with other <code>billing_event</code>s, the bid amount is for each occurrence, and has a minimum value 1 US cents. The minimum bid amounts of other currencies are of similar value to the US Dollar values provided.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>bid_strategy</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_x_E8\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">enum{LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</div></td><td><p class=\"_yd\"></p><div><div><p>Choose bid strategy for this ad set to suit your specific business goals.\n        Each strategy has tradeoffs and may be available for certain <code>optimization_goal</code>s:<br>\n        <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on\n        your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy\n        if you care most about cost efficiency. However with this strategy it may be harder to get\n        stable average costs as you spend. This strategy is also known as <em>automatic bidding</em>.\n        Learn more in <a href=\"https://www.facebook.com/business/help/721453268045071\">Ads Help Center, About bid strategies: Lowest cost</a>.<br>\n        <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on\n        your ad set <code>optimization_goal</code> while limiting actual bid to your specified\n        amount. With a bid cap you have more control over your\n        cost per actual optimization event. However if you set a limit which is too low you may\n        get less ads delivery. If you select this, you must provide\n        a bid cap with the <code>bid_amount</code> field.\n        Note: during creation this bid strategy is set if you provide <code>bid_amount</code> only.\n        This strategy is also known as <em>manual maximum-cost bidding</em>.\n        Learn more in <a href=\"https://www.facebook.com/business/help/721453268045071\">Ads Help Center, About bid strategies: Lowest cost</a>.<br></p>\n\n<p>Notes:</p>\n\n<ul>\n<li><p>If you enable campaign budget optimization, you should set <code>bid_strategy</code> at the parent campaign level.</p></li>\n<li><p><code>TARGET_COST</code> bidding strategy has been deprecated with <a href=\"/docs/graph-api/changelog/version9.0\">Marketing API v9</a>.</p></li>\n</ul>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>billing_event</code></span></div><div class=\"_yb\">enum{APP_INSTALLS, CLICKS, IMPRESSIONS, LINK_CLICKS, NONE, OFFER_CLAIMS, PAGE_LIKES, POST_ENGAGEMENT, THRUPLAY, PURCHASE, LISTING_INTERACTION}</div></td><td><p class=\"_yd\"></p><div><div><p>The billing event that this ad set is using:<br>APP_INSTALLS: Pay when people install your app.<br>CLICKS: Deprecated.<br>IMPRESSIONS: Pay when the ads are shown to people.<br>LINK_CLICKS: Pay when people click on the link of the ad.<br>OFFER_CLAIMS: Pay when people claim the offer.<br>PAGE_LIKES: Pay when people like your page.<br>POST_ENGAGEMENT: Pay when people engage with your post.<br>VIDEO_VIEWS: Pay when people watch your video ads for at least 10 seconds.<br>THRUPLAY: Pay for ads that are played to completion, or played for at least 15 seconds.</p>\n</div></div><p></p></td></tr><tr class=\"row_6 _5m27\"><td><div class=\"_yc\"><span><code>budget_schedule_specs</code></span></div><div class=\"_yb\">list&lt;JSON or object-like arrays&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Initial high demand periods to be created with the ad set.<br>\nProvide list of <code>time_start</code>, <code>time_end</code>,<code>budget_value</code>, and <code>budget_value_type</code>.<br>For example,<br>-F 'budget_schedule_specs=[{<br>\n\"time_start\":1699081200,<br>\n\"time_end\":1699167600,<br>\n\"budget_value\":100,<br>\n\"budget_value_type\":\"ABSOLUTE\"<br>\n}]'\n<br>\nSee <a href=\"https://developers.facebook.com/docs/graph-api/reference/high-demand-period/\">High Demand Period</a> for more details on each field.</p>\n</div></div><p></p></td></tr><tr class=\"row_6-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>time_start</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>time_end</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-3 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>budget_value</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-4 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>budget_value_type</code></span></div><div class=\"_yb\">enum{ABSOLUTE, MULTIPLIER}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-5 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>recurrence_type</code></span></div><div class=\"_yb\">enum{ONE_TIME, WEEKLY}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>weekly_schedule</code></span></div><div class=\"_yb\">list&lt;JSON or object-like arrays&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>days</code></span></div><div class=\"_yb\">list&lt;int64&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>minute_start</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>minute_end</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6-3 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>timezone_type</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>budget_source</code></span></div><div class=\"_yb\">enum{NONE, RMN}</div></td><td><p class=\"_yd\"></p><div><div><p>budget_source</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>budget_split_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>budget_split_set_id</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>campaign_attribution</code></span></div><div class=\"_yb\">enum{}</div></td><td><p class=\"_yd\"></p><div><div><p>campaign_attribution</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>campaign_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ad campaign you wish to add this ad set to.</p>\n</div></div><p></p></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>campaign_spec</code></span></div><div class=\"_yb\">Campaign spec</div></td><td><p class=\"_yd\"></p><div><div><p>Provide <code>name</code>, <code>objective</code> and <code>buying_type</code> for a campaign you want to create. Otherwise you need to provide <code>campaign_id</code> for an existing ad campaign. For example:<br>-F 'campaign_spec={<br><span>&nbsp;&nbsp;</span>\"name\": \"Inline created campaign\",<br><span>&nbsp;&nbsp;</span>\"objective\": \"CONVERSIONS\",<br><span>&nbsp;&nbsp;</span>\"buying_type\": \"AUCTION\"<br>}'\n<br><br>\nPlease refer to the <a href=\"/docs/marketing-api/reference/ad-campaign-group#odax-mapping\">Outcome-Driven Ads Experiences mapping table</a> to find new objectives and their corresponding destination types, optimization goals and promoted objects.</p>\n</div></div><p></p></td></tr><tr class=\"row_12 _5m27\"><td><div class=\"_yc\"><span><code>contextual_bundling_spec</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>settings of Contextual Bundle to support ads serving in Facebook contextual surfaces</p>\n</div></div><p></p></td></tr><tr class=\"row_12-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb\">enum{OPT_OUT, OPT_IN}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>creative_sequence</code></span></div><div class=\"_yb\">list&lt;numeric string or integer&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Order of the adgroup sequence to be shown to users</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>daily_budget</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>The daily budget defined in your <a href=\"/docs/marketing-api/adset/budget-limits\">account currency</a>, allowed only for ad sets with a duration (difference between <code>end_time</code> and <code>start_time</code>) longer than 24 hours. <br>Either <code>daily_budget</code> or <code>lifetime_budget</code> must be greater than 0.</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29\"><td><div class=\"_yc\"><span><code>daily_imps</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Daily impressions. Available only for campaigns with <code>buying_type=FIXED_CPM</code></p>\n</div></div><p></p></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>daily_min_spend_target</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Daily minimum spend target of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. This target is not a guarantee but our best effort.</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29\"><td><div class=\"_yc\"><span><code>daily_spend_cap</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Daily spend cap of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. Set the value to *************** to remove the spend cap.</p>\n</div></div><p></p></td></tr><tr class=\"row_18\"><td><div class=\"_yc\"><span><code>destination_type</code></span></div><div class=\"_yb\">enum{WEBSITE, APP, MESSENGER, APPLINKS_AUTOMATIC, WHATSAPP, INSTAGRAM_DIRECT, FACEBOOK, MESSAGING_MESSENGER_WHATSAPP, MESSAGING_INSTAGRAM_DIRECT_MESSENGER, MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP, MESSAGING_INSTAGRAM_DIRECT_WHATSAPP, SHOP_AUTOMATIC, ON_AD, ON_POST, ON_EVENT, ON_VIDEO, ON_PAGE, INSTAGRAM_PROFILE, FACEBOOK_PAGE, INSTAGRAM_PROFILE_AND_FACEBOOK_PAGE, INSTAGRAM_LIVE, FACEBOOK_LIVE, IMAGINE}</div></td><td><p class=\"_yd\"></p><div><div><p>Destination of ads in this Ad Set. Options include: Website, App, Messenger, <code>INSTAGRAM_DIRECT</code>, <code>INSTAGRAM_PROFILE</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_19 _5m29\"><td><div class=\"_yc\"><span><code>dsa_beneficiary</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dsa_beneficiary</p>\n</div></div><p></p></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>dsa_payor</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dsa_payor</p>\n</div></div><p></p></td></tr><tr class=\"row_21 _5m29\"><td><div class=\"_yc\"><span><code>end_time</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>End time, required when <code>lifetime_budget</code> is specified. e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. When creating a set with a daily budget, specify <code>end_time=0</code> to set the set to be ongoing and have no end date. UTC UNIX timestamp</p>\n</div></div><p></p></td></tr><tr class=\"row_22\"><td><div class=\"_yc\"><span><code>execution_options</code></span></div><div class=\"_yb\">list&lt;enum{validate_only, include_recommendations}&gt;</div></td><td><div>Default value: <code>Set</code></div><p class=\"_yd\"></p><div><div><p>An execution setting<br> <code>validate_only</code>: when this option is specified, the API call will not perform the mutation but will run through the validation rules against values of each field. <br><code>include_recommendations</code>: this option cannot be used by itself. When this option is used, recommendations  for ad object's configuration will be included. A separate section <a href=\"/docs/marketing-api/reference/ad-recommendation\">recommendations</a> will be included in the response, but only if recommendations for this specification exist.<br>If the call passes validation or review, response will be <code>{\"success\": true}</code>. If the call does not pass, an error will be returned with more details. These options can be used to improve any UI to display errors to the user much sooner, e.g. as soon as a new value is typed into any field corresponding to this ad object, rather than at the upload/save stage, or after review.</p>\n</div></div><p></p></td></tr><tr class=\"row_23 _5m29\"><td><div class=\"_yc\"><span><code>existing_customer_budget_percentage</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>existing_customer_budget_percentage</p>\n</div></div><p></p></td></tr><tr class=\"row_24 _5m27\"><td><div class=\"_yc\"><span><code>frequency_control_specs</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>An array of frequency control specs for this ad set. As there is only one event type currently supported, this array has no more than one element. Writes to this field are only available in ad sets where <code>REACH</code> is the objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_24-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event</code></span></div><div class=\"_yb\">enum{IMPRESSIONS, VIDEO_VIEWS, VIDEO_VIEWS_2S, VIDEO_VIEWS_15S}</div></td><td><p class=\"_yd\"></p><div><div><p>Event name, only <code>IMPRESSIONS</code> currently.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_24-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>interval_days</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>Interval period in days, between 1 and 90\n      (inclusive)</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_24-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>max_frequency</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>The maximum frequency, between 1 and 90\n      (inclusive)</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_25 _5m29\"><td><div class=\"_yc\"><span><code>is_dynamic_creative</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_y_QX\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>Indicates the ad set must only be used for dynamic creatives. Dynamic creative ads can be created in this ad set. Defaults to <code>false</code></p>\n</div></div><p></p></td></tr><tr class=\"row_26\"><td><div class=\"_yc\"><span><code>is_sac_cfca_terms_certified</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>is_sac_cfca_terms_certified</p>\n</div></div><p></p></td></tr><tr class=\"row_27 _5m29\"><td><div class=\"_yc\"><span><code>lifetime_budget</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime budget, defined in  your <a href=\"/docs/marketing-api/adset/budget-limits\">account currency</a>. If specified, you must also specify an <code>end_time</code>.<br>Either <code>daily_budget</code> or <code>lifetime_budget</code> must be greater than 0.</p>\n</div></div><p></p></td></tr><tr class=\"row_28\"><td><div class=\"_yc\"><span><code>lifetime_imps</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime impressions. Available only for campaigns with <code>buying_type=FIXED_CPM</code></p>\n</div></div><p></p></td></tr><tr class=\"row_29 _5m29\"><td><div class=\"_yc\"><span><code>lifetime_min_spend_target</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime minimum spend target of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. This target is not a guarantee but our best effort.</p>\n</div></div><p></p></td></tr><tr class=\"row_30\"><td><div class=\"_yc\"><span><code>lifetime_spend_cap</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime spend cap of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. Set the value to *************** to remove the spend cap.</p>\n</div></div><p></p></td></tr><tr class=\"row_31 _5m29\"><td><div class=\"_yc\"><span><code>max_budget_spend_percentage</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>max_budget_spend_percentage</p>\n</div></div><p></p></td></tr><tr class=\"row_32\"><td><div class=\"_yc\"><span><code>min_budget_spend_percentage</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>min_budget_spend_percentage</p>\n</div></div><p></p></td></tr><tr class=\"row_33 _5m29\"><td><div class=\"_yc\"><span><code>multi_optimization_goal_weight</code></span></div><div class=\"_yb\">enum{UNDEFINED, BALANCED, PREFER_INSTALL, PREFER_EVENT}</div></td><td><p class=\"_yd\"></p><div><div><p>multi_optimization_goal_weight</p>\n</div></div><p></p></td></tr><tr class=\"row_34\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Ad set name, max length of 400 characters.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span><span class=\"_1vet\">Supports Emoji</span></div></td></tr><tr class=\"row_35 _5m29\"><td><div class=\"_yc\"><span><code>optimization_goal</code></span></div><div class=\"_yb\">enum{NONE, APP_INSTALLS, AD_RECALL_LIFT, ENGAGED_USERS, EVENT_RESPONSES, IMPRESSIONS, LEAD_GENERATION, QUALITY_LEAD, LINK_CLICKS, OFFSITE_CONVERSIONS, PAGE_LIKES, POST_ENGAGEMENT, QUALITY_CALL, REACH, LANDING_PAGE_VIEWS, VISIT_INSTAGRAM_PROFILE, VALUE, THRUPLAY, DERIVED_EVENTS, APP_INSTALLS_AND_OFFSITE_CONVERSIONS, CONVERSATIONS, IN_APP_VALUE, MESSAGING_PURCHASE_CONVERSION, SUBSCRIBERS, REMINDERS_SET, MEANINGFUL_CALL_ATTEMPT, PROFILE_VISIT, PROFILE_AND_PAGE_ENGAGEMENT, ADVERTISER_SILOED_VALUE, AUTOMATIC_OBJECTIVE, MESSAGING_APPOINTMENT_CONVERSION}</div></td><td><p class=\"_yd\"></p><div><div><p>What the ad set is optimizing for. <br><code>APP_INSTALLS</code>: Will optimize for people more likely to install your app.<br><code>ENGAGED_USERS</code>: Will optimize for people more likely to take a particular action in your app.<br><code>EVENT_RESPONSES</code>: Will optimize for people more likely to attend your event.<br><code>IMPRESSIONS</code>: Will show the ads as many times as possible.<br><code>LEAD_GENERATION</code>: Will optimize for people more likely to fill out a lead generation form.<br><code>LINK_CLICKS</code>: Will optimize for people more likely to click in the link of the ad.<br><code>OFFER_CLAIMS</code>: Will optimize for people more likely to claim the offer.<br><code>OFFSITE_CONVERSIONS</code>: Will optimize for people more likely to make a conversion in the site<br><code>PAGE_ENGAGEMENT</code>: Will optimize for people more likely to engage with your page.<br><code>PAGE_LIKES</code>: Will optimize for people more likely to like your page.<br><code>POST_ENGAGEMENT</code>: Will optimize for people more likely to engage with your post.<br><code>REACH</code>: Optimize to reach the most unique users of each day or interval specified in <code>frequency_control_specs</code>.<br><code>SOCIAL_IMPRESSIONS</code>: Increase the number of impressions with social context. For example, with the names of one or more of the user's friends attached to the ad who have already liked the page or installed the app.<br><code>VALUE</code>: Will optimize for maximum total purchase value within the specified attribution window.<br><code>THRUPLAY</code>: Will optimize delivery of your ads to people are more likely to play your ad to completion, or play it for at least 15 seconds.<br><code>AD_RECALL_LIFT</code>: Optimize for people more likely to remember seeing your ads.<br><code>VISIT_INSTAGRAM_PROFILE</code>: Optimize for visits to the advertiser's instagram profile.</p>\n</div></div><p></p></td></tr><tr class=\"row_36\"><td><div class=\"_yc\"><span><code>optimization_sub_event</code></span></div><div class=\"_yb\">enum{NONE, VIDEO_SOUND_ON, TRIP_CONSIDERATION, TRAVEL_INTENT, TRAVEL_INTENT_NO_DESTINATION_INTENT, TRAVEL_INTENT_BUCKET_01, TRAVEL_INTENT_BUCKET_02, TRAVEL_INTENT_BUCKET_03, TRAVEL_INTENT_BUCKET_04, TRAVEL_INTENT_BUCKET_05}</div></td><td><p class=\"_yd\"></p><div><div><p>Optimization sub event for a specific optimization goal (ex: Sound-On event for Video-View-2s optimization goal)</p>\n</div></div><p></p></td></tr><tr class=\"row_37 _5m29\"><td><div class=\"_yc\"><span><code>pacing_type</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Defines the pacing type, standard by default or using <a href=\"/docs/marketing-api/adset/pacing\">ad scheduling</a></p>\n</div></div><p></p></td></tr><tr class=\"row_38 _5m27\"><td><div class=\"_yc\"><span><code>promoted_object</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>The object this ad set is promoting across all its ads.\n       Required with certain campaign objectives.<br>\n       <b>CONVERSIONS</b>\n       </p><ul>\n       <li><code>pixel_id</code> (Conversion pixel ID)</li>\n       <li><code>pixel_id</code> (Facebook pixel ID) and <code>custom_event_type</code></li>\n       <li><code>pixel_id</code> (Facebook pixel ID) and <code>pixel_rule</code> and <code>custom_event_type</code></li>\n       <li><code>event_id</code> (Facebook event ID) and <code>custom_event_type</code></li>\n       <li><code>application_id</code>, <code>object_store_url</code>, and <code>custom_event_type</code> for\n            mobile app events</li>\n       <li><code>offline_conversion_data_set_id</code> (Offline dataset ID) and\n            <code>custom_event_type</code> for offline conversions</li>\n       </ul>\n     <b>PAGE_LIKES</b>\n     <ul>\n     <li><code>page_id</code></li>\n     </ul>\n    <b>OFFER_CLAIMS</b>\n     <ul>\n     <li><code>page_id</code></li>\n     </ul>\n     <b>LINK_CLICKS</b>\n     <ul>\n     <li><code>application_id</code> and <code>object_store_url</code> for mobile app or Canvas app engagement link clicks</li>\n     </ul>\n     <b>APP_INSTALLS</b>\n     <ul>\n     <li><code>application_id</code> and <code>object_store_url</code></li>\n     </ul>\n     <b> if the <code>optimization_goal</code> is <code>OFFSITE_CONVERSIONS</code></b>\n     <ul>\n     <li><code>application_id</code>, <code>object_store_url</code>, and <code>custom_event_type</code> (Standard Events)</li>\n     <li><code>application_id</code>, <code>object_store_url</code>, <code>custom_event_type = OTHER</code> and <code>custom_event_str</code> (Custom Events)</li>\n     </ul>\n     <b>PRODUCT_CATALOG_SALES</b>\n     <ul>\n     <li><code>product_set_id</code></li>\n     <li><code>product_set_id</code> and <code>custom_event_type</code></li>\n     </ul>\nWhen <code>optimization_goal</code> is <code>LEAD_GENERATION</code>, <code>page_id</code> needs to be passed as promoted_object.\n<br><br>\nPlease refer to the <a href=\"/docs/marketing-api/reference/ad-campaign-group#odax-mapping\">Outcome-Driven Ads Experiences mapping table</a> to find new objectives and their corresponding destination types, optimization goals and promoted objects.\n</div></div><p></p></td></tr><tr class=\"row_38-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>application_id</code></span></div><div class=\"_yb\">int</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Application. Usually related to mobile or canvas games being promoted on Facebook for installs or engagement</p>\n</div></div><p></p></td></tr><tr class=\"row_38-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook conversion pixel.  Used with offsite conversion campaigns.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>custom_event_type</code></span></div><div class=\"_yb\">enum{AD_IMPRESSION, RATE, TUTORIAL_COMPLETION, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, ADD_TO_CART, ADD_TO_WISHLIST, INITIATED_CHECKOUT, ADD_PAYMENT_INFO, PURCHASE, LEAD, COMPLETE_REGISTRATION, CONTENT_VIEW, SEARCH, SERVICE_BOOKING_REQUEST, MESSAGING_CONVERSATION_STARTED_7D, LEVEL_ACHIEVED, ACHIEVEMENT_UNLOCKED, SPENT_CREDITS, LISTING_INTERACTION, D2_RETENTION, D7_RETENTION, OTHER}</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-3 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>object_store_url</code></span></div><div class=\"_yb\">URL</div></td><td><p class=\"_yd\"></p><div><div><p>The uri of the mobile / digital store where an application can be bought / downloaded. This is platform specific. When combined with the \"application_id\" this uniquely specifies an object which can be the subject of a Facebook advertising campaign.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-4 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>offer_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of an Offer from a Facebook Page.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-5 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>page_id</code></span></div><div class=\"_yb\">Page ID</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Page</p>\n</div></div><p></p></td></tr><tr class=\"row_38-6 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_catalog_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Product Catalog. Used with\n      <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Product Ads</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-7 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_item_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the product item.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-8 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>instagram_profile_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the instagram profile id.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-9 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Product Set within an Ad Set level Product\n      Catalog. Used with\n      <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Product Ads</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-10 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Event</p>\n</div></div><p></p></td></tr><tr class=\"row_38-11 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>offline_conversion_data_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the offline dataset.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-12 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>fundraiser_campaign_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the fundraiser campaign.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-13 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>custom_event_str</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-14 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>mcme_conversion_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a MCME conversion.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-15 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>conversion_goal_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Conversion Goal.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-16 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>offsite_conversion_event_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Offsite Conversion Event</p>\n</div></div><p></p></td></tr><tr class=\"row_38-17 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>boosted_product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the Boosted Product Set within an Ad Set level Product\n      Catalog. Should only be present when the advertiser has\n      opted into Product Set Boosting.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-18 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_form_event_source_type</code></span></div><div class=\"_yb\">enum{inferred, offsite_crm, offsite_web, onsite_crm, onsite_crm_single_event, onsite_web, onsite_p2b_call, onsite_messaging}</div></td><td><p class=\"_yd\"></p><div><div><p>The event source of lead ads form.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-19 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_custom_event_type</code></span></div><div class=\"_yb\">enum{AD_IMPRESSION, RATE, TUTORIAL_COMPLETION, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, ADD_TO_CART, ADD_TO_WISHLIST, INITIATED_CHECKOUT, ADD_PAYMENT_INFO, PURCHASE, LEAD, COMPLETE_REGISTRATION, CONTENT_VIEW, SEARCH, SERVICE_BOOKING_REQUEST, MESSAGING_CONVERSATION_STARTED_7D, LEVEL_ACHIEVED, ACHIEVEMENT_UNLOCKED, SPENT_CREDITS, LISTING_INTERACTION, D2_RETENTION, D7_RETENTION, OTHER}</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-20 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_custom_event_str</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_38-21 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_offsite_conversion_type</code></span></div><div class=\"_yb\">enum{default, clo}</div></td><td><p class=\"_yd\"></p><div><div><p>The offsite conversion type for lead ads</p>\n</div></div><p></p></td></tr><tr class=\"row_38-22 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>value_semantic_type</code></span></div><div class=\"_yb\">enum {VALUE, MARGIN, LIFETIME_VALUE}</div></td><td><p class=\"_yd\"></p><div><div><p>The semantic of the event value to be using for optimization</p>\n</div></div><p></p></td></tr><tr class=\"row_38-23 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>variation</code></span></div><div class=\"_yb\">enum {OMNI_CHANNEL_SHOP_AUTOMATIC_DATA_COLLECTION, PRODUCT_SET_AND_APP, PRODUCT_SET_AND_IN_STORE, PRODUCT_SET_AND_OMNICHANNEL, PRODUCT_SET_AND_PHONE_CALL, PRODUCT_SET_AND_WEBSITE, PRODUCT_SET_WEBSITE_APP_AND_INSTORE}</div></td><td><p class=\"_yd\"></p><div><div><p>Variation of the promoted object for a PCA ad</p>\n</div></div><p></p></td></tr><tr class=\"row_38-24 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_set_optimization</code></span></div><div class=\"_yb\">enum{enabled, disabled}</div></td><td><p class=\"_yd\"></p><div><div><p>Enum defining whether or not the ad should be optimized for the promoted product set</p>\n</div></div><p></p></td></tr><tr class=\"row_38-25 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>full_funnel_objective</code></span></div><div class=\"_yb\">enum{6, 8, 12, 14, 15, 19, 24, 26, 29, 31, 32, 35, 36, 37, 39, 41, 42, 40, 43, 44, 46}</div></td><td><p class=\"_yd\"></p><div><div><p>Enum defining the full funnel objective of the campaign</p>\n</div></div><p></p></td></tr><tr class=\"row_38-26 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>omnichannel_object</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_38-26-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>app</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_38-26-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>pixel</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_38-26-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>onsite</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_38-27 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>whats_app_business_phone_number_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_38-28 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>whatsapp_phone_number</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_39 _5m29\"><td><div class=\"_yc\"><span><code>rf_prediction_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Reach and frequency prediction ID</p>\n</div></div><p></p></td></tr><tr class=\"row_40\"><td><div class=\"_yc\"><span><code>source_adset_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The source adset id that this ad is copied from (if applicable).</p>\n</div></div><p></p></td></tr><tr class=\"row_41 _5m29\"><td><div class=\"_yc\"><span><code>start_time</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>The start time of the set, e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. UTC UNIX timestamp</p>\n</div></div><p></p></td></tr><tr class=\"row_42\"><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb\">enum{ACTIVE, PAUSED, DELETED, ARCHIVED}</div></td><td><p class=\"_yd\"></p><div><div><p>Only <code>ACTIVE</code> and <code>PAUSED</code> are valid for creation. The other statuses\n        can be used for update. If it is set to <code>PAUSED</code>, all its active ads\n        will be paused and have an effective status <code>ADSET_PAUSED</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_43 _5m29\"><td><div class=\"_yc\"><span><code>targeting</code></span></div><div class=\"_yb\">Targeting object</div></td><td><p class=\"_yd\"></p><div><div><p>An ad set's targeting structure.  \"countries\" is required. See <a href=\"/docs/marketing-api/targeting-specs\">targeting</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_44\"><td><div class=\"_yc\"><span><code>time_based_ad_rotation_id_blocks</code></span></div><div class=\"_yb\">list&lt;list&lt;int64&gt;&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Specify ad creative that displays at custom date ranges in a campaign\n        as an array. A list of Adgroup IDs. The list of ads to display for each\n        time range in a given schedule. For example display first ad in Adgroup\n        for first date range, second ad for second date range, and so on. You\n        can display more than one ad per date range by providing more than\n        one ad ID per array. For example set\n        <code>time_based_ad_rotation_id_blocks</code> to [[1], [2, 3], [1, 4]]. On the\n        first date range show ad 1, on the second date range show ad 2 and ad 3\n        and on the last date range show ad 1 and ad 4. Use with\n        <code>time_based_ad_rotation_intervals</code> to specify date ranges.</p>\n</div></div><p></p></td></tr><tr class=\"row_45 _5m29\"><td><div class=\"_yc\"><span><code>time_based_ad_rotation_intervals</code></span></div><div class=\"_yb\">list&lt;int64&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Date range when specific ad creative displays during a campaign.\n        Provide date ranges in an array of UNIX timestamps where each\n        timestamp represents the start time for each date range. For example a\n        3-day campaign from May 9 12am to  May 11 11:59PM PST can have three\n        date ranges, the first date range starts from May 9 12:00AM to\n        May 9 11:59PM, second date range starts from May 10 12:00AM to\n        May 10 11:59PM and last starts from  May 11 12:00AM to  May 11 11:59PM.\n        The first timestamp should match the campaign start time. The last\n        timestamp should be at least 1 hour before the campaign end time. You\n        must provide at least two date ranges. All date ranges must cover the\n        whole campaign length, so any date range cannot exceed campaign length.\n        Use with <code>time_based_ad_rotation_id_blocks</code> to specify ad creative for\n        each date range.</p>\n</div></div><p></p></td></tr><tr class=\"row_46\"><td><div class=\"_yc\"><span><code>time_start</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>Time start</p>\n</div></div><p></p></td></tr><tr class=\"row_47 _5m29\"><td><div class=\"_yc\"><span><code>time_stop</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>Time stop</p>\n</div></div><p></p></td></tr><tr class=\"row_48\"><td><div class=\"_yc\"><span><code>tune_for_category</code></span></div><div class=\"_yb\">enum{NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}</div></td><td><p class=\"_yd\"></p><div><div><p>tune_for_category</p>\n</div></div><p></p></td></tr><tr class=\"row_49 _5m29\"><td><div class=\"_yc\"><span><code>value_rule_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Value Rule Set ID</p>\n</div></div><p></p></td></tr><tr class=\"row_50\"><td><div class=\"_yc\"><span><code>value_rules_applied</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>value_rules_applied</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div><div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>2641</td><td>Your ad includes or excludes locations that are currently restricted</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>2695</td><td>The ad set creation reached its campaign group(ios14) limit.</td></tr><tr><td>900</td><td>No such application exists.</td></tr><tr><td>300</td><td>Edit failure</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div><span><p>This operation has been deprecated with <a href=\"/docs/graph-api/changelog/version8.0/#ad-accounts\">Marketing API V8</a>.</p>\n</span></div><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/ad-account/adsets", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/ad-campaign/", "/docs/marketing-api/reachandfrequency", "/docs/marketing-api/reference/ad-campaign-group#odax-mapping", "/docs/marketing-api/adset/budget-limits", "/docs/marketing-api/reference/ad-recommendation", "/docs/marketing-api/adset/pacing", "/docs/marketing-api/dynamic-product-ads", "/docs/marketing-api/targeting-specs"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/", "timestamp": "2025-06-25T15:18:03.213Z"}