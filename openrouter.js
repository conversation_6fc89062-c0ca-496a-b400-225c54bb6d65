const axios = require('axios');
const TurndownService = require('turndown');

class OpenRouterProcessor {
  constructor(apiKey, options = {}) {
    this.apiKey = apiKey;
    this.baseURL = 'https://openrouter.ai/api/v1';
    this.model = options.model || 'anthropic/claude-3.5-haiku';
    this.maxTokens = options.maxTokens || 4000;
    this.temperature = options.temperature || 0.1;
    this.requestDelay = options.requestDelay || 2000; // 2 seconds between requests
    this.lastRequestTime = 0;
    
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced'
    });
    
    // Configure axios instance
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://github.com/your-repo', // Optional: for OpenRouter analytics
        'X-Title': 'FB Marketing API Scraper' // Optional: for OpenRouter analytics
      },
      timeout: 60000 // 60 second timeout
    });
  }

  /**
   * Rate limiting - ensure we don't exceed API limits
   */
  async enforceRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.requestDelay) {
      const waitTime = this.requestDelay - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Process documentation content with AI
   */
  async processContent(content, url, title) {
    try {
      await this.enforceRateLimit();
      
      // Convert HTML to markdown first for better processing
      const markdown = this.turndownService.turndown(content);
      
      // Create the prompt for processing
      const prompt = this.createProcessingPrompt(markdown, url, title);
      
      const response = await this.client.post('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert technical documentation processor. Your job is to clean, structure, and enhance API documentation for optimal consumption by AI agents and developers.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: this.maxTokens,
        temperature: this.temperature
      });

      const processedContent = response.data.choices[0].message.content;
      
      // Parse the structured response
      return this.parseProcessedContent(processedContent, url, title);
      
    } catch (error) {
      console.error(`❌ OpenRouter processing failed for ${url}:`, error.message);
      
      // Return fallback content if AI processing fails
      return this.createFallbackContent(content, url, title);
    }
  }

  /**
   * Create processing prompt for the AI
   */
  createProcessingPrompt(markdown, url, title) {
    return `Please process this Facebook Marketing API documentation page and return a structured JSON response.

URL: ${url}
Title: ${title}

Raw Content:
${markdown}

Please return a JSON object with the following structure:
{
  "title": "Clean, descriptive title",
  "summary": "2-3 sentence summary of what this page covers",
  "content": "Cleaned and well-formatted markdown content",
  "keyPoints": ["Array of 3-5 key points or takeaways"],
  "apiEndpoints": ["Array of API endpoints mentioned (if any)"],
  "parameters": ["Array of important parameters or fields (if any)"],
  "examples": ["Array of code examples or important examples (if any)"],
  "tags": ["Array of relevant tags/categories"],
  "relatedTopics": ["Array of related topics mentioned"],
  "difficulty": "beginner|intermediate|advanced",
  "contentType": "overview|reference|guide|tutorial|changelog"
}

Guidelines:
1. Clean up any HTML artifacts or formatting issues
2. Preserve all technical details and code examples
3. Make the content more readable and well-structured
4. Extract key information into the structured fields
5. Ensure the markdown is properly formatted with headers, lists, and code blocks
6. Focus on accuracy and completeness
7. If this is an API reference page, pay special attention to endpoints, parameters, and examples

Return only the JSON object, no additional text.`;
  }

  /**
   * Parse the AI-processed content
   */
  parseProcessedContent(processedContent, url, title) {
    try {
      // Clean the response - remove any markdown code blocks if present
      let cleanedContent = processedContent.trim();

      // Remove markdown code block markers if present
      if (cleanedContent.startsWith('```json')) {
        cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedContent.startsWith('```')) {
        cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Try to parse the JSON response
      const parsed = JSON.parse(cleanedContent);

      // Validate required fields
      const required = ['title', 'summary', 'content'];
      for (const field of required) {
        if (!parsed[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Add metadata
      parsed.originalUrl = url;
      parsed.processedAt = new Date().toISOString();
      parsed.processor = 'openrouter-claude-3.5-haiku';

      return {
        success: true,
        processed: parsed
      };

    } catch (error) {
      console.error(`❌ Failed to parse AI response for ${url}:`, error.message);

      // Return the raw response if parsing fails
      return {
        success: false,
        processed: {
          title: title,
          summary: 'AI processing failed - raw content preserved',
          content: processedContent,
          originalUrl: url,
          processedAt: new Date().toISOString(),
          processor: 'openrouter-claude-3.5-haiku-fallback'
        }
      };
    }
  }

  /**
   * Create fallback content when AI processing fails
   */
  createFallbackContent(content, url, title) {
    const markdown = this.turndownService.turndown(content);
    
    return {
      success: false,
      processed: {
        title: title,
        summary: 'Content extracted without AI processing',
        content: markdown,
        keyPoints: [],
        apiEndpoints: [],
        parameters: [],
        examples: [],
        tags: ['facebook-marketing-api'],
        relatedTopics: [],
        difficulty: 'unknown',
        contentType: 'unknown',
        originalUrl: url,
        processedAt: new Date().toISOString(),
        processor: 'fallback-turndown'
      }
    };
  }

  /**
   * Process multiple pages in batch (with rate limiting)
   */
  async processBatch(pages) {
    const results = [];
    
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      console.log(`🤖 Processing with AI: ${page.url} (${i + 1}/${pages.length})`);
      
      const result = await this.processContent(page.content, page.url, page.title);
      results.push({
        ...page,
        aiProcessed: result.processed,
        aiSuccess: result.success
      });
      
      // Show progress
      if ((i + 1) % 5 === 0) {
        console.log(`✅ Processed ${i + 1}/${pages.length} pages with AI`);
      }
    }
    
    return results;
  }

  /**
   * Test the OpenRouter connection
   */
  async testConnection() {
    try {
      console.log('🧪 Testing OpenRouter connection...');
      
      const testResponse = await this.client.post('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: 'Please respond with "OpenRouter connection successful" to confirm the API is working.'
          }
        ],
        max_tokens: 50,
        temperature: 0
      });

      const response = testResponse.data.choices[0].message.content;
      
      if (response.includes('successful')) {
        console.log('✅ OpenRouter connection test passed');
        return true;
      } else {
        console.log('⚠️ OpenRouter responded but with unexpected content:', response);
        return false;
      }
      
    } catch (error) {
      console.error('❌ OpenRouter connection test failed:', error.message);
      return false;
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStats() {
    try {
      // Note: OpenRouter doesn't have a direct usage endpoint in their API
      // This is a placeholder for potential future functionality
      return {
        model: this.model,
        requestsProcessed: 'N/A',
        tokensUsed: 'N/A',
        estimatedCost: 'N/A'
      };
    } catch (error) {
      console.error('❌ Failed to get usage stats:', error.message);
      return null;
    }
  }
}

module.exports = OpenRouterProcessor;
