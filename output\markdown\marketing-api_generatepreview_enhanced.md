# Facebook Marketing API - Ad Previews Generation

## Summary
This documentation covers how to generate previews of Facebook ads using the Marketing API v23.0. It explains multiple methods for creating ad previews including using existing ad IDs, ad creative IDs, or creative specifications, with support for various ad formats and placements.

## Key Points
- Use user access tokens, not Page access tokens for ad previews
- Multiple methods available: Ad ID, Ad Creative ID, or Creative Spec
- Account-specific previews are only visible to account role holders
- General generatepreviews endpoint creates publicly visible previews
- Supports various ad formats including Instagram placements

## API Endpoints
- `https://graph.facebook.com/v23.0/<CREATIVE_ID>/previews`
- `https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews`
- `https://graph.facebook.com/v23.0/generatepreviews`
- `https://graph.facebook.com/v23.0/<AD_ID>/previews`
- `https://graph.facebook.com/v23.0/<AD_CREATIVE_ID>/previews`

## Parameters
- ad_format
- access_token
- creative
- object_story_spec
- object_story_id
- link_data
- page_id
- product_item_ids
- call_to_action

## Content
# Ad Previews

Preview existing ads and generate previews of ads you want to create. Generated previews are based on your ad creative. For ad preview **provide a user access token**, not a Page access token.

## Preview Existing Ad Creative

```bash
curl -X GET \
  -d 'ad_format="DESKTOP_FEED_STANDARD"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>/previews
```

## Preview Using Creative Spec

```bash
curl -X GET \
  -d 'creative="<CREATIVE_SPEC>"' \
  -d 'ad_format="<AD_FORMAT>"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

## Generating Previews

There are several ways to generate a preview:

- **Ad ID**: Use existing ad's `previews` endpoint
- **Ad Creative ID**: Use existing ad creative's `previews` endpoint  
- **Creative Spec**: Supply a creative specification

### Using Ad ID
```
https://graph.facebook.com/<API_VERSION>/<AD_ID>/previews
```

### Using Ad Creative ID
```
https://graph.facebook.com/<API_VERSION>/<AD_CREATIVE_ID>/previews
```

### Using Creative Spec
Two endpoint options:
- `/act_<AD_ACCOUNT_ID>/generatepreviews` - Account-specific previews
- `/generatepreviews` - General previews (not account-specific)

**Note**: Previews from an ad account are only visible to people with a role on the ad account. Previews generated using the general `generatepreviews` endpoint are visible to anyone.

### Advantage+ Catalog Ads
For Advantage+ catalog ads, pass the entire `object_story_spec` into the `/generatepreviews` endpoint and use `product_item_ids`.

## Examples

### Object Story Spec Preview (PHP)

```php
use FacebookAds\Object\AdAccount;
use FacebookAds\Object\AdCreative;
use FacebookAds\Object\Fields\AdCreativeFields;
use FacebookAds\Object\Fields\AdPreviewFields;
use FacebookAds\Object\Fields\AdCreativeLinkDataFields;
use FacebookAds\Object\Fields\AdCreativeObjectStorySpecFields;
use FacebookAds\Object\AdCreativeLinkData;
use FacebookAds\Object\AdCreativeObjectStorySpec;
use FacebookAds\Object\Values\AdPreviewAdFormatValues;
use FacebookAds\Object\Values\AdCreativeCallToActionTypeValues;

$link_data = new AdCreativeLinkData();
$link_data->setData(array(
  AdCreativeLinkDataFields::LINK => '<URL>',
  AdCreativeLinkDataFields::MESSAGE => 'Message',
  AdCreativeLinkDataFields::NAME => 'Name',
  AdCreativeLinkDataFields::DESCRIPTION => 'Description',
  AdCreativeLinkDataFields::CALL_TO_ACTION => array(
    'type' => AdCreativeCallToActionTypeValues::SIGN_UP,
    'value' => array(
      'link' => '<URL>',
    ),
  ),
));

$story = new AdCreativeObjectStorySpec();
$story->setData(array(
  AdCreativeObjectStorySpecFields::PAGE_ID => <PAGE_ID>,
  AdCreativeObjectStorySpecFields::LINK_DATA => $link_data,
));

$creative = new AdCreative();
$creative->setData(array(
  AdCreativeFields::OBJECT_STORY_SPEC => $story,
));

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$account->getGeneratePreviews(array(), array(
  AdPreviewFields::CREATIVE => $creative,
  AdPreviewFields::AD_FORMAT => AdPreviewAdFormatValues::DESKTOP_FEED_STANDARD,
));
```

### Multi-Product Ad Preview

```bash
curl -X GET \
  -d 'creative={
        "object_story_id": "<PAGE_ID>_<POST_ID>"
      }' \
  -d 'ad_format="DESKTOP_FEED_STANDARD"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

### App Ad Preview

```bash
curl -X GET \
  -d 'creative={
        "object_story_spec": {
          "link_data": {
            "call_to_action": {
              "type": "USE_APP",
              "value": {
                "link": "<URL>"
              }
            },
            "description": "Description",
            "link": "<URL>",
            "message": "Message",
            "name": "Name",
            "picture": "<IMAGE_URL>"
          },
          "page_id": "<PAGE_ID>"
        }
      }' \
  -d 'ad_format="MOBILE_FEED_STANDARD"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

### Instagram Explore Home Ad Preview

```bash
curl -X GET \
  -d 'ad_format="INSTAGRAM_EXPLORE_GRID_HOME"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_ID>/previews
```

### Instagram Search Results Ad Preview

```bash
curl -X GET \
  -d 'ad_format="INSTAGRAM_SEARCH_CHAIN"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_ID>/previews
```

## Response Format

All endpoints return an ad preview object containing an iframe with the rendered ad preview:

```json
{
  "data": [
    {
      "body": "<iframe src=\"https://www.facebook.com/ads/api/preview_iframe.php?d=...\" width=\"274\" height=\"213\" scrolling=\"yes\" style=\"border: none;\"></iframe>"
    }
  ]
}
```

## Examples
Basic creative preview with DESKTOP_FEED_STANDARD format

Multi-product ad preview using object_story_id

App ad preview with USE_APP call-to-action

Instagram Explore Grid Home ad format

Instagram Search Chain ad format

PHP SDK implementation with object_story_spec

---
**Tags:** Facebook Marketing API, Ad Previews, Ad Creative, Instagram Ads, API v23.0, generatepreviews
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/generatepreview/v23.0
**Processed:** 2025-06-25T15:43:15.898Z