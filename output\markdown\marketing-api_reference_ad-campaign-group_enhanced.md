# Facebook Marketing API Campaign Reference

## Summary
Complete reference documentation for the Facebook Marketing API Campaign object, which represents the highest level organizational structure within an ad account. Covers reading, creating, updating, and deleting campaigns, along with field definitions, parameters, and objective validation rules.

## Key Points
- Campaigns are the highest level organizational structure in Facebook ad accounts
- All campaigns must specify special_ad_categories field (required)
- iOS 14.5+ campaigns must set is_skadnetwork_attribution to true
- Campaign objectives determine valid ad types, optimization goals, and promoted objects
- New ODAX objectives are replacing legacy objectives starting from v17.0

## API Endpoints
- `GET /v23.0/{campaign_id}`
- `POST /act_{ad_account_id}/campaigns`
- `POST /{campaign_id}`
- `DELETE /{campaign_id}`
- `POST /{campaign_id}/copies`

## Parameters
- special_ad_categories
- objective
- name
- status
- bid_strategy
- daily_budget
- lifetime_budget
- is_skadnetwork_attribution
- promoted_object
- spend_cap
- buying_type

## Content
# Campaign

A campaign is the highest level organizational structure within an ad account and should represent a single objective for an advertiser, for example, to drive page post engagement. Setting objective of the campaign will enforce validation on any ads added to the campaign to ensure they also have the correct objective.

## Important Notes

- Facebook will no longer be able to aggregate non-inline conversion metric values across iOS 14.5 and non-iOS 14.5 campaigns due to differences in attribution logic
- Ad campaigns that target iOS 14.5 must set the new `is_skadnetwork_attribution` field to `true`
- The `date_preset = lifetime` parameter is disabled in Graph API v10.0 and replaced with `date_preset = maximum`, which returns a maximum of 37 months of data

## Limits

- You can only create 200 ad sets per ad campaign
- If your campaign has more than 70 ad sets and uses Campaign Budget Optimization, you are not able to edit your current bid strategy or turn off CBO

## Special Ad Categories

All businesses using the Marketing API must identify whether or not new and edited campaigns belong to a Special Ad Category. Current available categories are: housing, employment, credit, or issues, elections, and politics. Businesses whose ads do not belong to a Special Ad Category must indicate NONE or send an empty array in the `special_ad_categories` field.

## Reading

A campaign is a grouping of ad sets which are organized by the same business objective. Each campaign has an objective that must be valid across the ad sets within that campaign.

### Parameters

| Parameter | Description |
|-----------|-------------|
| `date_preset` | enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year} |
| `time_range` | {'since':YYYY-MM-DD,'until':YYYY-MM-DD} |

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | numeric string | Campaign's ID (Default) |
| `account_id` | numeric string | ID of the ad account that owns this campaign |
| `adlabels` | list<AdLabel> | Ad Labels associated with this campaign |
| `bid_strategy` | enum | Bid strategy for this campaign when you enable campaign budget optimization |
| `boosted_object_id` | numeric string | The Boosted Object this campaign has associated, if any |
| `brand_lift_studies` | list<AdStudy> | Automated Brand Lift V2 studies for this ad set |
| `budget_remaining` | numeric string | Remaining budget |
| `buying_type` | string | Buying type (AUCTION or RESERVED) |
| `configured_status` | enum | Campaign status (ACTIVE, PAUSED, DELETED, ARCHIVED) |
| `created_time` | datetime | Created Time |
| `daily_budget` | numeric string | The daily budget of the campaign |
| `effective_status` | enum | Effective status including IN_PROCESS and WITH_ISSUES |
| `is_skadnetwork_attribution` | bool | Indicates campaign will include SKAdNetwork, iOS 14+ |
| `lifetime_budget` | numeric string | The lifetime budget of the campaign |
| `name` | string | Campaign's name |
| `objective` | string | Campaign's objective |
| `promoted_object` | AdPromotedObject | The object this campaign is promoting across all its ads |
| `special_ad_categories` | list<enum> | Special ad categories |
| `spend_cap` | numeric string | A spend cap for the campaign |
| `start_time` | datetime | Campaign start time |
| `status` | enum | Campaign status |
| `stop_time` | datetime | Campaign stop time |
| `updated_time` | datetime | Updated Time |

## Creating

You can create a campaign by making a POST request to `/act_{ad_account_id}/campaigns`.

### Required Parameters

| Parameter | Description |
|-----------|-------------|
| `name` | Campaign name |
| `objective` | Campaign objective |
| `status` | Campaign status (ACTIVE or PAUSED) |
| `special_ad_categories` | Array of special ad categories (required) |

### Optional Parameters

| Parameter | Description |
|-----------|-------------|
| `bid_strategy` | Bid strategy for campaign budget optimization |
| `daily_budget` | Daily budget (int64) |
| `lifetime_budget` | Lifetime budget (int64) |
| `buying_type` | AUCTION (default) or RESERVED |
| `is_skadnetwork_attribution` | Boolean for iOS 14+ campaigns |
| `promoted_object` | Object being promoted |
| `spend_cap` | Spend cap for the campaign |

## Updating

You can update a campaign by making a POST request to `/{campaign_id}`.

## Deleting

You can delete a campaign by making a DELETE request to `/{campaign_id}`.

## Objective Validation

### Outcome-Driven Ads Experiences (ODAX)

Newer objectives that replace legacy objectives:
- `OUTCOME_APP_PROMOTION`
- `OUTCOME_AWARENESS`
- `OUTCOME_ENGAGEMENT`
- `OUTCOME_LEADS`
- `OUTCOME_SALES`
- `OUTCOME_TRAFFIC`

### Legacy Objectives

Deprecated objectives include:
- `APP_INSTALLS`
- `BRAND_AWARENESS`
- `CONVERSIONS`
- `EVENT_RESPONSES`
- `LEAD_GENERATION`
- `LINK_CLICKS`
- `MESSAGES`
- `PAGE_LIKES`
- `POST_ENGAGEMENT`
- `PRODUCT_CATALOG_SALES`
- `REACH`
- `STORE_VISITS`
- `VIDEO_VIEWS`

### Objective Requirements

Certain objectives require specific `promoted_object` fields:

- **APP_INSTALLS**: `application_id` and `object_store_url`
- **CONVERSIONS**: `pixel_id` and optionally `custom_event_type`
- **PRODUCT_CATALOG_SALES**: `product_set_id`
- **PAGE_LIKES**: `page_id`

## Error Codes

Common error codes:
- 100: Invalid parameter
- 190: Invalid OAuth 2.0 Access Token
- 200: Permissions error
- 368: Action deemed abusive or disallowed
- 80004: Too many calls to ad account

## Examples
POST /v23.0/act_<AD_ACCOUNT_ID>/campaigns HTTP/1.1
name=My+campaign&objective=OUTCOME_TRAFFIC&status=PAUSED&special_ad_categories=%5B%5D

curl -X POST \
  -F 'name="New ODAX Campaign"' \
  -F 'objective="OUTCOME_ENGAGEMENT"' \
  -F 'status="PAUSED"' \
  -F 'special_ad_categories=[]' \
  -F 'access_token=ACCESS_TOKEN \
  https://graph.facebook.com/v11.0/act_AD_ACCOUNT_ID/campaigns

---
**Tags:** Facebook Marketing API, Campaign, Advertising, Graph API, CRUD Operations, Objectives, Special Ad Categories, iOS 14.5, SKAdNetwork
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group
**Processed:** 2025-06-25T15:53:24.288Z