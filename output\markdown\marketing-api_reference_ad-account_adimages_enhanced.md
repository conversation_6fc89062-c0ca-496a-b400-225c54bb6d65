# Facebook Marketing API - Ad Account Ad Images Reference

## Summary
Complete reference documentation for managing ad images within Facebook Ad Accounts through the Marketing API. Covers reading, creating, and deleting ad images with detailed parameters, examples, and error handling.

## Key Points
- Supports reading, creating, and deleting ad images within Facebook Ad Accounts
- Provides filtering options by business tag, dimensions, name, and hash
- Supports copying images between ad accounts using source account ID and hash
- Returns detailed image metadata including URLs, dimensions, and hash values
- Implements rate limiting and comprehensive error handling

## API Endpoints
- `GET /v23.0/{ad-account-id}/adimages`
- `POST /act_{ad_account_id}/adimages`
- `DELETE /act_{ad_account_id}/adimages`

## Parameters
- biz_tag_id
- business_id
- hashes
- minheight
- minwidth
- name
- bytes
- copy_from
- source_account_id
- hash

## Content
# Ad Account Ad Images

Manage ad images that belong to a Facebook Ad Account through the Marketing API.

## Reading Ad Images

Retrieve ad images associated with an ad account.

### Endpoint
```
GET /v23.0/{ad-account-id}/adimages
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `biz_tag_id` | int64 | Business tag ID to filter images |
| `business_id` | numeric string or integer | Optional. Assists with filters such as recently used |
| `hashes` | list<string> | Hash of the image |
| `minheight` | int64 | Minimum height of the image |
| `minwidth` | int64 | Minimum width of the image |
| `name` | string | Image name used in image names filter |

### Response Format

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Data Field
A list of AdImage nodes.

#### Summary Field
- `total_count` (int32): Total number of images in the Ad Account

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/adimages HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/adimages',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/adimages",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

## Creating Ad Images

Upload new ad images to an ad account.

### Endpoint
```
POST /act_{ad_account_id}/adimages
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `bytes` | Base64 UTF-8 string | Image file content in bytes format |
| `copy_from` | JSON object | Copy Ad Image from source to destination account |
| `source_account_id` | numeric string | Source account ID for copying |
| `hash` | string | Hash of the image to copy |

### Copy From Object Structure
```json
{
  "source_account_id": "<SOURCE_ACCOUNT_ID>",
  "hash": "02bee5277ec507b6fd0f9b9ff2f22d9c"
}
```

### Return Type

Returns a map structure with image details:

```json
{
  "images": {
    "<filename>": {
      "hash": "string",
      "url": "string",
      "url_128": "string",
      "url_256": "string",
      "url_256_height": "string",
      "url_256_width": "string",
      "height": "int32",
      "width": "int32",
      "name": "string"
    }
  }
}
```

## Deleting Ad Images

Remove ad images from an ad account.

### Endpoint
```
DELETE /act_{ad_account_id}/adimages
```

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `hash` | string | Hash of the image to delete | Yes |

### Return Type

```json
{
  "success": true
}
```

## Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | The action attempted has been deemed abusive or is otherwise disallowed |
| 2500 | Error parsing graph query |
| 2635 | You are calling a deprecated version of the Ads API |
| 80004 | Too many calls to this ad-account. Rate limiting applied |

## Notes

- Updating ad images is not supported through this endpoint
- The API supports read-after-write for creation operations
- All operations require proper OAuth 2.0 access tokens
- Rate limiting applies to prevent abuse

## Examples
HTTP GET request for retrieving ad images

PHP SDK implementation for reading images

JavaScript SDK API call

Android SDK GraphRequest example

iOS SDK FBSDKGraphRequest implementation

JSON structure for copying images between accounts

---
**Tags:** Facebook Marketing API, Ad Images, Ad Account, Image Management, Graph API, REST API  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adimages/  
**Processed:** 2025-06-25T16:12:06.928Z