# Facebook Marketing API - Ad Account Subscribed Apps Reference

## Summary
Documentation for managing app subscriptions to Facebook ad accounts through the Marketing API. Covers reading subscribed apps, subscribing new apps, and unsubscribing apps from ad accounts.

## Key Points
- Endpoint allows reading, subscribing, and unsubscribing apps from ad accounts
- GET requests return a list of currently subscribed apps with pagination support
- POST requests can subscribe new apps using the app_id parameter
- DELETE requests can unsubscribe apps using the app_id parameter
- All operations return a success boolean and support standard error codes

## API Endpoints
- `GET /v23.0/{ad-account-id}/subscribed_apps`
- `POST /act_{ad_account_id}/subscribed_apps`
- `DELETE /act_{ad_account_id}/subscribed_apps`

## Parameters
- app_id (string) - ID of app to subscribe/unsubscribe
- ad-account-id - Target ad account identifier
- data - Array of AdAccountSubscribedApps nodes
- paging - Pagination information object
- success - Boolean return value for operations

## Content
# Ad Account Subscribed Apps

*Graph API Version: v23.0*

## Overview

This endpoint allows you to manage app subscriptions to Facebook ad accounts. You can retrieve a list of apps currently subscribed to an ad account, subscribe new apps, or unsubscribe existing apps.

## Reading Subscribed Apps

Get a list of apps subscribed to the ad account.

### Endpoint
```
GET /v23.0/{ad-account-id}/subscribed_apps
```

### Parameters
This endpoint doesn't require any parameters.

### Response Format
```json
{
  "data": [],
  "paging": {}
}
```

#### Response Fields
- **data**: A list of AdAccountSubscribedApps nodes
- **paging**: Pagination information (see Graph API guide for details)

### Example Request
```http
GET /v23.0/{ad-account-id}/subscribed_apps HTTP/1.1
Host: graph.facebook.com
```

## Subscribing Apps (Creating)

You cannot create new subscriptions through this endpoint directly.

## Updating Subscriptions

Subscribe an app to an ad account by making a POST request.

### Endpoint
```
POST /act_{ad_account_id}/subscribed_apps
```

### Parameters
| Parameter | Type | Description |
|-----------|------|--------------|
| `app_id` | string | The ID of the app to be subscribed to the ad account |

### Return Type
```json
{
  "success": bool
}
```

This endpoint supports read-after-write functionality.

## Unsubscribing Apps (Deleting)

Remove an app subscription from an ad account by making a DELETE request.

### Endpoint
```
DELETE /act_{ad_account_id}/subscribed_apps
```

### Parameters
| Parameter | Type | Description |
|-----------|------|--------------|
| `app_id` | string | The ID of the app to be unsubscribed from the ad account |

### Return Type
```json
{
  "success": bool
}
```

## Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |

## Additional Resources

- [Using Graph API Guide](https://developers.facebook.com/docs/graph-api/using-graph-api/)
- [Graph API Pagination](https://developers.facebook.com/docs/graph-api/using-graph-api/#paging)
- [Application Reference](https://developers.facebook.com/docs/graph-api/reference/application/)
- [AdAccount Reference](https://developers.facebook.com/docs/marketing-api/reference/ad-account/)

## Examples
GET /v23.0/{ad-account-id}/subscribed_apps HTTP/1.1

Response format: {"data": [], "paging": {}}

Return type: {"success": bool}

---
**Tags:** Facebook Marketing API, Ad Account, App Subscriptions, Graph API, REST API, CRUD Operations
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/subscribed_apps/
**Processed:** 2025-06-25T15:39:43.962Z