# Facebook Marketing API: Ad Creative

## Summary
This documentation covers the creation and management of ad creatives in the Facebook Marketing API. It explains how to construct ad creatives, specify placements, and generate ad previews across different platforms and formats.

## Key Points
- Ad creatives define the visual attributes of an advertisement
- Supports multiple ad placements and formats
- Requires object_story_id for page post ads
- Previews can be generated for different platforms
- Follows Facebook Graph API versioning

## API Endpoints
- `/act_{AD_ACCOUNT_ID}/adcreatives`
- `/act_{AD_ACCOUNT_ID}/generatepreviews`

## Parameters
- name
- object_story_id
- access_token
- ad_format
- creative_spec

## Content
## Ad Creative Overview

An ad creative is an object that contains all the visual rendering data for an ad in the Facebook Marketing API. Developers can create various types of ad units with different appearances, placements, and creative options.

### Key Components
- Ad creative object
- Placement specifications
- Ad preview generation

### Creating an Ad Creative

```bash
curl -X POST \
  -F 'name="Sample Promoted Post"' \
  -F 'object_story_id="<PAGE_ID>_<POST_ID>"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Placement Options
Facebook supports multiple placements including:
- Mobile Feed
- Desktop Feed
- Right column

### Ad Preview Generation

```bash
curl -G \
  --data-urlencode 'creative="<CREATIVE_SPEC>"' \
  -d 'ad_format="<AD_FORMAT>"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

## Examples
Creating a page post ad

Generating ad previews for different placements

Reading ad creative details

---
**Tags:** Marketing API, Ad Creatives, Facebook Advertising, API Reference
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/creative
**Processed:** 2025-06-25T14:58:02.060Z