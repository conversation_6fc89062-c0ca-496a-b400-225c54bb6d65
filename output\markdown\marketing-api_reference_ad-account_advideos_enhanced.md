# Facebook Marketing API - Ad Videos Reference

## Summary
Complete reference documentation for the Facebook Marketing API Ad Videos endpoint, covering video upload, management, and deletion operations for ad accounts. Includes detailed parameters, return types, and error codes for video operations.

## Key Points
- Supports video upload via direct file or URL with chunked upload capability
- Includes specialized support for 360° videos and slideshow creation
- Reading and updating operations are not supported - only create and delete
- Comprehensive error handling for upload issues and permissions
- Returns detailed upload session information and transcoding parameters

## API Endpoints
- `POST /act_{ad_account_id}/advideos`
- `DELETE /act_{ad_account_id}/advideos`

## Parameters
- name
- title
- description
- source
- file_url
- file_size
- upload_phase
- upload_session_id
- start_offset
- end_offset
- video_file_chunk
- slideshow_spec
- fisheye_video_cropped
- original_projection_type
- unpublished_content_type
- video_id

## Content
# Ad Videos

The Ad Videos endpoint allows you to manage video assets within Facebook ad accounts. This endpoint supports creating (uploading) and deleting video content for use in advertising campaigns.

## Operations Overview

### Reading
Reading operations are not supported on this endpoint.

### Creating (Video Upload)
You can upload videos by making a POST request to the `advideos` edge:
- **Endpoint**: `/act_{ad_account_id}/advideos`
- **Creates**: A Video object

#### Key Parameters

**Basic Video Properties:**
- `name` (string): The name of the video in the library
- `title` (UTF-8 string): Video name (must be < 255 characters, supports emoji)
- `description` (UTF-8 string): Video description (supports emoji)
- `source` (string): The video file encoded as form data
- `file_url` (string): URL to video file
- `file_size` (int64): Video file size in bytes (for chunked upload)

**Chunked Upload Parameters:**
- `upload_phase` (enum): Phase during chunked upload (start, transfer, finish, cancel)
- `upload_session_id` (numeric string): Session ID for chunked upload
- `start_offset` (int64): Start position in bytes of chunk being sent
- `end_offset` (int64): End position in bytes of chunk
- `video_file_chunk` (string): The actual video chunk data

**360° Video Parameters:**
- `fisheye_video_cropped` (boolean): Whether single fisheye video is cropped
- `front_z_rotation` (float): Front z rotation in degrees
- `original_fov` (int64): Original field of view of source camera
- `original_projection_type` (enum): Original projection type (equirectangular, cubemap, half_equirectangular)

**Slideshow Parameters:**
- `slideshow_spec` (JSON object):
  - `images_urls` (list<URL>): 3-7 element array of image URLs (required)
  - `duration_ms` (integer): Duration of each image (default: 1000ms)
  - `transition_ms` (integer): Crossfade transition duration (default: 1000ms)
  - `reordering_opt_in` (boolean): Default false
  - `music_variations_opt_in` (boolean): Default false

**Content Type:**
- `unpublished_content_type` (enum): SCHEDULED, SCHEDULED_RECURRING, DRAFT, ADS_POST, INLINE_CREATED, PUBLISHED, REVIEWABLE_BRANDED_CONTENT

#### Return Type
Returns a struct containing:
- `id` (numeric string): Video ID
- `upload_session_id` (numeric string): Upload session identifier
- `video_id` (numeric string): Created video ID
- `start_offset`, `end_offset` (numeric string): Byte offsets
- `success` (bool): Upload success status
- `skip_upload` (bool): Whether upload was skipped
- `upload_domain` (string): Upload domain used
- `region_hint` (string): Suggested region
- Transcoding information: `transcode_bit_rate_bps`, `transcode_dimension`, etc.

### Updating
Update operations are not supported on this endpoint.

### Deleting
You can remove a video from an ad account by making a DELETE request:
- **Endpoint**: `/act_{ad_account_id}/advideos`
- **Required Parameter**: `video_id` (video ID) - Ad account library video ID
- **Returns**: `{"success": bool}`

## Error Codes

**Upload Errors:**
- 381: Problem uploading video file
- 382: Video file too small
- 389: Unable to fetch video from URL
- 352: Unsupported video format
- 6001: General upload problem

**General Errors:**
- 100: Invalid parameter
- 190: Invalid OAuth 2.0 Access Token
- 200: Permissions error
- 222: Video not visible
- 368: Action deemed abusive or disallowed
- 613: Rate limit exceeded

---
**Tags:** Facebook Marketing API, Video Upload, Ad Videos, Chunked Upload, 360 Video, Slideshow, Video Management
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/
**Processed:** 2025-06-25T15:20:14.383Z