const fs = require('fs-extra');
const path = require('path');

class ContentQualityAnalyzer {
  constructor(outputDir = './output') {
    this.outputDir = outputDir;
    this.qualityThreshold = 60; // Pages scoring below this need reprocessing
  }

  /**
   * Analyze all scraped content and identify pages needing reprocessing
   */
  async analyzeAllContent() {
    console.log('🔍 Analyzing content quality across all scraped pages...');
    
    const htmlDir = path.join(this.outputDir, 'html');
    const jsonDir = path.join(this.outputDir, 'json');
    const aiProcessedDir = path.join(this.outputDir, 'ai-processed');
    
    if (!await fs.pathExists(htmlDir)) {
      throw new Error('HTML directory not found. Please run the scraper first.');
    }
    
    const htmlFiles = await fs.readdir(htmlDir);
    const results = [];
    
    for (const htmlFile of htmlFiles) {
      if (!htmlFile.endsWith('.html')) continue;
      
      const baseName = htmlFile.replace('.html', '');
      const htmlPath = path.join(htmlDir, htmlFile);
      const jsonPath = path.join(jsonDir, `${baseName}.json`);
      const aiProcessedPath = path.join(aiProcessedDir, `${baseName}.json`);
      
      try {
        const analysis = await this.analyzePageQuality(htmlPath, jsonPath, aiProcessedPath, baseName);
        results.push(analysis);
      } catch (error) {
        console.error(`Error analyzing ${baseName}:`, error.message);
        results.push({
          filename: baseName,
          score: 0,
          needsReprocessing: true,
          issues: ['Analysis failed'],
          error: error.message
        });
      }
    }
    
    // Sort by score (lowest first - these need the most help)
    results.sort((a, b) => a.score - b.score);
    
    const needsReprocessing = results.filter(r => r.needsReprocessing);
    
    console.log(`\n📊 Quality Analysis Results:`);
    console.log(`   Total pages analyzed: ${results.length}`);
    console.log(`   Pages needing reprocessing: ${needsReprocessing.length}`);
    console.log(`   Average quality score: ${(results.reduce((sum, r) => sum + r.score, 0) / results.length).toFixed(1)}`);
    
    return {
      allResults: results,
      needsReprocessing: needsReprocessing,
      summary: {
        total: results.length,
        needsReprocessing: needsReprocessing.length,
        averageScore: results.reduce((sum, r) => sum + r.score, 0) / results.length
      }
    };
  }

  /**
   * Analyze quality of a single page
   */
  async analyzePageQuality(htmlPath, jsonPath, aiProcessedPath, baseName) {
    let score = 100;
    const issues = [];
    
    // Analyze HTML content quality
    const htmlAnalysis = await this.analyzeHtmlQuality(htmlPath);
    score -= htmlAnalysis.penalty;
    issues.push(...htmlAnalysis.issues);
    
    // Analyze original JSON metadata
    const jsonAnalysis = await this.analyzeJsonQuality(jsonPath);
    score -= jsonAnalysis.penalty;
    issues.push(...jsonAnalysis.issues);
    
    // Analyze AI-processed content quality
    const aiAnalysis = await this.analyzeAiProcessedQuality(aiProcessedPath);
    score -= aiAnalysis.penalty;
    issues.push(...aiAnalysis.issues);
    
    const needsReprocessing = score < this.qualityThreshold || issues.length > 3;
    
    return {
      filename: baseName,
      score: Math.max(0, score),
      needsReprocessing,
      issues,
      details: {
        html: htmlAnalysis,
        json: jsonAnalysis,
        aiProcessed: aiAnalysis
      }
    };
  }

  /**
   * Analyze HTML content for quality issues
   */
  async analyzeHtmlQuality(htmlPath) {
    const issues = [];
    let penalty = 0;
    
    try {
      const htmlContent = await fs.readFile(htmlPath, 'utf8');
      
      // Check for excessive navigation elements
      const navMatches = (htmlContent.match(/class="_3wm1"|class="_1dyy"|data-click-area="to_top_nav"/g) || []).length;
      if (navMatches > 5) {
        issues.push(`Excessive navigation elements (${navMatches})`);
        penalty += 20;
      }
      
      // Check for script tags
      const scriptMatches = (htmlContent.match(/<script/g) || []).length;
      if (scriptMatches > 0) {
        issues.push(`Contains ${scriptMatches} script tags`);
        penalty += 15;
      }
      
      // Check for style tags
      const styleMatches = (htmlContent.match(/<style/g) || []).length;
      if (styleMatches > 0) {
        issues.push(`Contains ${styleMatches} style tags`);
        penalty += 10;
      }
      
      // Check content-to-markup ratio
      const textContent = htmlContent.replace(/<[^>]*>/g, '').trim();
      const markupRatio = (htmlContent.length - textContent.length) / htmlContent.length;
      if (markupRatio > 0.8) {
        issues.push(`High markup-to-content ratio (${(markupRatio * 100).toFixed(1)}%)`);
        penalty += 25;
      }
      
      // Check for very short content
      if (textContent.length < 500) {
        issues.push(`Very short content (${textContent.length} chars)`);
        penalty += 30;
      }
      
      // Check for table of contents that shouldn't be there
      if (htmlContent.includes('On This Page') || htmlContent.includes('_33zv _3e1u')) {
        issues.push('Contains table of contents elements');
        penalty += 15;
      }
      
    } catch (error) {
      issues.push('Failed to read HTML file');
      penalty += 50;
    }
    
    return { issues, penalty };
  }

  /**
   * Analyze original JSON metadata quality
   */
  async analyzeJsonQuality(jsonPath) {
    const issues = [];
    let penalty = 0;
    
    try {
      if (!await fs.pathExists(jsonPath)) {
        issues.push('Original JSON file missing');
        return { issues, penalty: 20 };
      }
      
      const jsonData = await fs.readJson(jsonPath);
      
      // Check if title is meaningful
      if (!jsonData.title || jsonData.title.length < 5) {
        issues.push('Missing or very short title');
        penalty += 10;
      }
      
      // Check content length
      if (!jsonData.content || jsonData.content.length < 1000) {
        issues.push('Very short content in original JSON');
        penalty += 15;
      }
      
    } catch (error) {
      issues.push('Failed to read original JSON file');
      penalty += 20;
    }
    
    return { issues, penalty };
  }

  /**
   * Analyze AI-processed content quality
   */
  async analyzeAiProcessedQuality(aiProcessedPath) {
    const issues = [];
    let penalty = 0;
    
    try {
      if (!await fs.pathExists(aiProcessedPath)) {
        issues.push('AI-processed file missing');
        return { issues, penalty: 30 };
      }
      
      const aiData = await fs.readJson(aiProcessedPath);
      
      // Check summary quality
      if (!aiData.summary || aiData.summary.length < 50) {
        issues.push('Missing or very short summary');
        penalty += 15;
      }
      
      // Check key points
      if (!aiData.keyPoints || aiData.keyPoints.length < 2) {
        issues.push('Missing or insufficient key points');
        penalty += 10;
      }
      
      // Check for generic/unhelpful content
      if (aiData.summary && aiData.summary.includes('AI processing failed')) {
        issues.push('AI processing failed fallback content');
        penalty += 40;
      }
      
      // Check content length
      if (!aiData.content || aiData.content.length < 500) {
        issues.push('Very short processed content');
        penalty += 20;
      }
      
      // Check for API endpoints on reference pages
      const isReferencePage = aiData.originalUrl && aiData.originalUrl.includes('/reference/');
      if (isReferencePage && (!aiData.apiEndpoints || aiData.apiEndpoints.length === 0)) {
        issues.push('Reference page missing API endpoints');
        penalty += 15;
      }
      
      // Check for parameters on reference pages
      if (isReferencePage && (!aiData.parameters || aiData.parameters.length === 0)) {
        issues.push('Reference page missing parameters');
        penalty += 10;
      }
      
    } catch (error) {
      issues.push('Failed to read AI-processed file');
      penalty += 30;
    }
    
    return { issues, penalty };
  }

  /**
   * Generate a detailed report
   */
  async generateReport(analysisResults, outputPath = './content-quality-report.json') {
    const report = {
      generatedAt: new Date().toISOString(),
      summary: analysisResults.summary,
      qualityThreshold: this.qualityThreshold,
      needsReprocessing: analysisResults.needsReprocessing.map(page => ({
        filename: page.filename,
        score: page.score,
        issues: page.issues
      })),
      topIssues: this.getTopIssues(analysisResults.allResults),
      recommendations: this.generateRecommendations(analysisResults)
    };
    
    await fs.writeJson(outputPath, report, { spaces: 2 });
    console.log(`📋 Quality report saved to: ${outputPath}`);
    
    return report;
  }

  /**
   * Get the most common issues across all pages
   */
  getTopIssues(results) {
    const issueCount = {};
    
    results.forEach(result => {
      result.issues.forEach(issue => {
        issueCount[issue] = (issueCount[issue] || 0) + 1;
      });
    });
    
    return Object.entries(issueCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([issue, count]) => ({ issue, count }));
  }

  /**
   * Generate recommendations based on analysis
   */
  generateRecommendations(analysisResults) {
    const recommendations = [];
    
    if (analysisResults.needsReprocessing.length > 0) {
      recommendations.push(`Reprocess ${analysisResults.needsReprocessing.length} pages with improved content extraction`);
    }
    
    const topIssues = this.getTopIssues(analysisResults.allResults);
    if (topIssues.length > 0) {
      recommendations.push(`Address top issue: "${topIssues[0].issue}" affecting ${topIssues[0].count} pages`);
    }
    
    if (analysisResults.summary.averageScore < 70) {
      recommendations.push('Overall content quality is below optimal - consider full reprocessing');
    }
    
    return recommendations;
  }
}

module.exports = ContentQualityAnalyzer;
