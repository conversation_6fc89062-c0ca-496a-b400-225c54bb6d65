const express = require('express');
const crypto = require('crypto');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const config = require('../config/config');

const router = express.Router();

/**
 * @swagger
 * /webhooks/facebook:
 *   get:
 *     summary: Facebook webhook verification
 *     tags: [Webhooks]
 *     parameters:
 *       - in: query
 *         name: hub.mode
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: hub.challenge
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: hub.verify_token
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Webhook verified successfully
 */
router.get('/facebook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  if (mode && token) {
    if (mode === 'subscribe' && token === config.facebook.webhookVerifyToken) {
      logger.info('Facebook webhook verified');
      res.status(200).send(challenge);
    } else {
      logger.warn('Facebook webhook verification failed');
      res.sendStatus(403);
    }
  } else {
    res.sendStatus(400);
  }
});

/**
 * @swagger
 * /webhooks/facebook:
 *   post:
 *     summary: Handle Facebook webhook events
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 */
router.post('/facebook', asyncHandler(async (req, res) => {
  const body = req.body;

  // Verify webhook signature
  const signature = req.get('X-Hub-Signature-256');
  if (!signature) {
    logger.warn('Facebook webhook: Missing signature');
    return res.sendStatus(401);
  }

  const expectedSignature = crypto
    .createHmac('sha256', config.facebook.appSecret)
    .update(JSON.stringify(body))
    .digest('hex');

  if (`sha256=${expectedSignature}` !== signature) {
    logger.warn('Facebook webhook: Invalid signature');
    return res.sendStatus(401);
  }

  // Process webhook events
  if (body.object === 'page') {
    body.entry.forEach((entry) => {
      entry.changes?.forEach((change) => {
        if (change.field === 'leadgen') {
          // Handle lead generation event
          logger.info('Facebook lead received:', change.value);
          // TODO: Process lead and trigger VAPI call
        }
      });
    });
  }

  res.status(200).send('EVENT_RECEIVED');
}));

/**
 * @swagger
 * /webhooks/vapi:
 *   post:
 *     summary: Handle VAPI webhook events
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 */
router.post('/vapi', asyncHandler(async (req, res) => {
  const body = req.body;

  // Verify VAPI webhook signature if configured
  const signature = req.get('X-VAPI-Signature');
  if (config.vapi.webhookSecret && signature) {
    const expectedSignature = crypto
      .createHmac('sha256', config.vapi.webhookSecret)
      .update(JSON.stringify(body))
      .digest('hex');

    if (signature !== expectedSignature) {
      logger.warn('VAPI webhook: Invalid signature');
      return res.sendStatus(401);
    }
  }

  // Process VAPI events
  logger.info('VAPI webhook received:', body);
  
  // TODO: Handle different VAPI event types
  // - call.started
  // - call.ended
  // - call.failed
  // - transcript.updated

  res.status(200).send('EVENT_RECEIVED');
}));

/**
 * @swagger
 * /webhooks/stripe:
 *   post:
 *     summary: Handle Stripe webhook events
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 */
router.post('/stripe', asyncHandler(async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = config.stripe.webhookSecret;

  let event;

  try {
    // Verify Stripe webhook signature
    const stripe = require('stripe')(config.stripe.secretKey);
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    logger.error('Stripe webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  logger.info('Stripe webhook received:', event.type);

  switch (event.type) {
    case 'customer.subscription.created':
      // Handle subscription created
      break;
    case 'customer.subscription.updated':
      // Handle subscription updated
      break;
    case 'customer.subscription.deleted':
      // Handle subscription cancelled
      break;
    case 'invoice.payment_succeeded':
      // Handle successful payment
      break;
    case 'invoice.payment_failed':
      // Handle failed payment
      break;
    default:
      logger.info(`Unhandled Stripe event type: ${event.type}`);
  }

  res.status(200).send('EVENT_RECEIVED');
}));

module.exports = router;
