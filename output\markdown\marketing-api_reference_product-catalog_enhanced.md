# Facebook Marketing API - Product Catalog Reference

## Summary
Complete reference documentation for the Product Catalog API endpoint in Facebook's Marketing API. Covers reading, creating, updating, and deleting product catalogs used for dynamic ads, including all available fields, parameters, and edge connections.

## Key Points
- Product catalogs are essential for dynamic ads and contain products, hotels, flights, or other items
- Catalogs can be associated with pixels and apps to track user interactions and optimize ad delivery
- Batch operations are supported for efficient bulk management of catalog items
- Multiple verticals are supported including commerce, hotels, flights, and vehicles
- Proper permissions and Marketing API access levels are required for catalog operations

## API Endpoints
- `GET /{business_id}/owned_product_catalogs`
- `POST /{business_id}/product_catalogs`
- `GET/POST/DELETE /{product_catalog_id}`
- `POST /{product_catalog_id}/items_batch`
- `GET/POST/DELETE /{product_catalog_id}/external_event_sources`
- `POST /{product_catalog_id}/assigned_users`
- `POST /{product_catalog_id}/vehicles`

## Parameters
- name
- vertical
- segment_use_cases
- da_display_settings
- external_event_sources
- item_type
- requests
- allow_upsert
- tasks
- user
- default_image_url
- fallback_image_url

## Content
# Product Catalog

Represents a catalog for your business you can use to deliver ads with [dynamic ads](/docs/marketing-api/dynamic-ad).

## Overview

Product catalogs contain a list of items like products, hotels or flights, and the information needed to display them in dynamic ads. You can associate pixels and apps with a product catalog and then display products in ads based on signals from pixels or apps.

## Permissions

You need the appropriate [Marketing API Access Level](/docs/marketing-api/access#limits) and must accept the [Terms of Service](https://business.facebook.com/legal/product_catalog_terms/) by creating your first catalog through [Business Manager](https://business.facebook.com/).

## Reading Product Catalogs

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `segment_use_cases` | array<enum> | Available values: AFFILIATE_SELLER_STOREFRONT, AFFILIATE_TAGGED_ONLY_DEPRECATED, COLLAB_ADS, COLLAB_ADS_FOR_MARKETPLACE_PARTNER, COLLAB_ADS_SEGMENT_WITHOUT_SEGMENT_SYNCING, DIGITAL_CIRCULARS, FB_LIVE_SHOPPING, IG_SHOPPING, IG_SHOPPING_SUGGESTED_PRODUCTS, MARKETPLACE_SHOPS, TEST |

### Fields

| Field | Type | Description |
|-------|------|--------------|
| `id` | numeric string | ID of a catalog (Default) |
| `business` | Business | Business that owns a catalog |
| `da_display_settings` | ProductCatalogImageSettings | Image display settings for Dynamic Ad formats |
| `default_image_url` | string | URL for default image used when product images are unavailable |
| `fallback_image_url` | list<string> | URL for fallback image used for auto-generated dynamic items |
| `feed_count` | int32 | Total number of feeds used by catalog |
| `is_catalog_segment` | bool | Indicates if this is a catalog segment |
| `is_local_catalog` | bool | Indicates if this is a local catalog |
| `name` | string | Name of the catalog (Default) |
| `product_count` | int32 | Total number of products in catalog |
| `vertical` | enum | Type of catalog (hotels, commerce, etc.) |

### Available Edges

- `agencies` - Agencies with catalog access
- `assigned_users` - Users assigned to catalog
- `automotive_models` - Automotive models in catalog
- `categories` - Product categories
- `external_event_sources` - Associated pixels and apps
- `products` - Products in catalog
- `product_sets` - Product sets in catalog
- `hotels` - Hotels in catalog
- `flights` - Flights in catalog
- `vehicles` - Vehicles in catalog
- And many more...

## Creating Product Catalogs

### Basic Catalog Creation

```bash
curl \
  -F 'name=Catalog' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/product_catalogs
```

### Parameters for Creation

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `name` | string | Yes | Name of the catalog |
| `vertical` | enum | No | Catalog vertical (default: commerce) |
| `additional_vertical_option` | enum | No | Additional catalog configurations |
| `catalog_segment_filter` | JSON rule | No | Filter for creating catalog segment |
| `da_display_settings` | Object | No | Dynamic Ads display settings |

### Batch Operations

You can perform batch operations on catalog items:

```bash
curl \
  -F 'requests=[{"method":"CREATE","data":{...}}]' \
  -F 'item_type=product' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>/items_batch
```

## Updating Product Catalogs

### Available Update Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `name` | string | Update catalog name |
| `da_display_settings` | Object | Update display settings |
| `default_image_url` | URI | Update default image URL |
| `fallback_image_url` | URI | Update fallback image URL |

## Deleting Product Catalogs

```bash
curl -X DELETE \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>
```

### Delete Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `allow_delete_catalog_with_live_product_set` | boolean | false | Allow deletion even with live product sets |

## Managing External Event Sources

### Associate Pixels/Apps

```bash
curl \
  -F 'external_event_sources=[<PIXEL_ID>,<APP_ID>]' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/<VERSION>/<PRODUCT_CATALOG_ID>/external_event_sources
```

### Remove Association

```bash
curl -X DELETE \
  -F 'external_event_sources=[<APP_ID>,<PIXEL_ID>]' \
  -F 'access_token=<TOKEN>' \
  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>/external_event_sources
```

## Error Codes

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | Action deemed abusive or disallowed |
| 80009 | Too many calls to catalog account |
| 80014 | Too many batch upload calls |

## User Management

### Assign Users

```bash
curl \
  -F 'user=<USER_ID>' \
  -F 'tasks=["MANAGE","ADVERTISE"]' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>/assigned_users
```

### Available Tasks

- `MANAGE` - Full catalog management
- `ADVERTISE` - Create ads from catalog
- `MANAGE_AR` - Manage AR features
- `AA_ANALYZE` - Analytics access

## Examples
curl -G -d "access_token=<ACCESS_TOKEN>" "https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/owned_product_catalogs"

curl -F 'name=Catalog' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/product_catalogs

curl -F 'external_event_sources=[<PIXEL_ID>,<APP_ID>]' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/<VERSION>/<PRODUCT_CATALOG_ID>/external_event_sources

curl -X DELETE -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>

---
**Tags:** Facebook Marketing API, Product Catalog, Dynamic Ads, E-commerce, API Reference, CRUD Operations, Batch Operations
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/product-catalog
**Processed:** 2025-06-25T15:47:27.635Z