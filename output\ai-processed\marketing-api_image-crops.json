{"title": "Facebook Marketing API Image Crops", "summary": "Image Crops allow you to specify aspect ratios for images in different ad placements on Facebook. You can provide custom cropping specifications or use defaults, with support for various placement types through crop keys and pixel coordinates.", "content": "# Image Crops\n\nProvide aspect ratios for images in different ad placements. Facebook crops your image according to your specifications given or if you provide no cropping we display it using defaults. See [Ad Image](/docs/reference/ads-api/adimage/).\n\n## Usage Example\n\nFirst, upload an image to use in ad creative:\n\n```bash\ncurl \\\n  -F 'filename=@<IMAGE_PATH>' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adimages\n```\n\nThen, provide ad creative by referencing the image hash returned in the previous call along with cropping.\n\n## Crop Structure\n\nCrops contains key-value pairs, where:\n- **Key**: A `crop key` that describes an aspect ratio\n- **Value**: The pixel dimensions of the crop as `(x, y)` coordinates\n\nFor all supported keys, see [Ads Image Crops Reference](/docs/marketing-api/reference/ads-image-crops).\n\n## Coordinate System\n\nProvide value as `(x, y)` coordinates for the upper-left and bottom-right corners of the cropping rectangle:\n- Image origin `(0, 0)` is at the upper-left corner\n- Point `(width - 1, height - 1)` is at the bottom-right corner\n- The aspect ratio of the specified box must be as close as possible to the aspect ratio in the `crop key`\n\n## Specification Requirements\n\nWhen you use this feature, **you should use it for all placements where an ad may appear**. For example, if you provide it for the Right Hand Column, and you also want to use the ad in Newsfeed, you'll need to provide cropping for the Newsfeed placement.\n\n## Limitations\n\nImage crops are only supported for ad creatives with `image_file` or `image_hash`. `Page posts` are not supported. Values must adhere to these constraints:\n\n- Points specified by `(x, y)` must lie within the image. A rectangle that extends beyond the bounds of the image is invalid\n- The rectangle must be the same aspect ratio as specified by the crop key\n- Coordinates cannot contain negative values\n- Facebook Stories do not support image crops\n\n## Example\n\n```json\n{\"100x100\": [[330, 67], [1080, 817]]}\n```", "keyPoints": ["Image crops allow custom aspect ratios for different ad placements using crop keys and pixel coordinates", "Must be used for all placements where an ad may appear to ensure consistency", "Only supported for ad creatives with image_file or image_hash, not Page posts", "Coordinates must be within image bounds and maintain the correct aspect ratio", "Facebook Stories do not support image crops functionality"], "apiEndpoints": ["https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adimages"], "parameters": ["filename", "access_token", "crop key", "x coordinate", "y coordinate", "image_file", "image_hash"], "examples": ["curl -F 'filename=@<IMAGE_PATH>' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adimages", "{\"100x100\": [[330, 67], [1080, 817]]}"], "tags": ["Facebook Marketing API", "Image Crops", "Ad Creative", "Image Upload", "Ad Placements", "Aspect Ratios"], "relatedTopics": ["Ad Image", "Ads Image Crops Reference", "Ad Creative", "Facebook Stories", "Newsfeed placement", "Right Hand Column placement", "Page posts"], "difficulty": "intermediate", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/image-crops", "processedAt": "2025-06-25T15:46:42.884Z", "processor": "openrouter-claude-sonnet-4"}