# Facebook Marketing API - Ad Creative Reference

## Summary
Complete reference for the Ad Creative object in Facebook Marketing API, which provides layout and contains content for ads. Covers creation, reading, updating, and deletion of ad creatives with detailed field specifications and examples.

## Key Points
- Ad Creative objects define the visual and textual content for Facebook ads across all placements
- The instagram_actor_id field is deprecated - migrate to instagram_user_id before January 2026
- Political ads require special authorization_category flags and compliance with advertising policies
- Content has strict character limits and formatting rules that vary by ad type and placement
- Inline page post creation allows creating unpublished posts directly within the creative specification

## API Endpoints
- `GET /v23.0/<CREATIVE_ID>`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives`
- `POST /v23.0/<CREATIVE_ID>`
- `DELETE /v23.0/<CREATIVE_ID>`
- `GET /v23.0/<CREATIVE_ID>/previews`

## Parameters
- object_story_spec
- object_story_id
- authorization_category
- name
- image_hash
- image_url
- video_id
- call_to_action_type
- link_url
- thumbnail_width
- thumbnail_height
- instagram_user_id
- page_id
- asset_feed_spec
- platform_customizations

## Content
# Facebook Marketing API - Ad Creative Reference

## Overview

The Ad Creative object provides layout and contains content for Facebook ads. It defines the visual and textual elements that make up an advertisement across Facebook's family of apps and services.

### Important Deprecation Notice

The `instagram_actor_id` field for `act_<AD_ACCOUNT_ID>/adcreatives` has been deprecated for v22.0 and will be deprecated for all versions January 20, 2026. Migrate to use the `instagram_user_id` field instead.

## Special Requirements

### Political Ads

Advertisers running ads about social issues, elections, and politics must:
- Specify `special_ad_categories` while creating an ad campaign
- Set `authorization_category` to flag at the ad creative level
- Use `POLITICAL_WITH_DIGITALLY_CREATED_MEDIA` for digitally created/altered media (effective January 9, 2024)

## API Operations

### Reading Ad Creatives

```bash
curl -G \
  -d 'fields=name,object_story_id' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

#### Read Thumbnail

```bash
curl -G \
  -d 'thumbnail_width=150' \
  -d 'thumbnail_height=120' \
  -d 'fields=thumbnail_url' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

### Creating Ad Creatives

#### Basic Link Ad

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "link_data": { 
      "image_hash": "<IMAGE_HASH>", 
      "link": "<URL>", 
      "message": "try it out" 
    }, 
    "page_id": "<PAGE_ID>" 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Link Ad with Call to Action

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "link_data": { 
      "call_to_action": {"type":"SIGN_UP","value":{"link":"<URL>"}}, 
      "link": "<URL>", 
      "message": "try it out" 
    }, 
    "page_id": "<PAGE_ID>" 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Carousel Ad

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "link_data": { 
      "child_attachments": [ 
        { 
          "description": "$8.99", 
          "image_hash": "<IMAGE_HASH>", 
          "link": "https://www.link.com/product1", 
          "name": "Product 1", 
          "video_id": "<VIDEO_ID>" 
        }, 
        { 
          "description": "$9.99", 
          "image_hash": "<IMAGE_HASH>", 
          "link": "https://www.link.com/product2", 
          "name": "Product 2", 
          "video_id": "<VIDEO_ID>" 
        } 
      ], 
      "link": "<URL>" 
    }, 
    "page_id": "<PAGE_ID>" 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Political Ad Creative

```bash
curl \
  -F 'authorization_category=POLITICAL' \
  -F 'object_story_spec={...}' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Video Page Like Ad

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "page_id": "<PAGE_ID>", 
    "video_data": { 
      "call_to_action": {"type":"LIKE_PAGE","value":{"page":"<PAGE_ID>"}}, 
      "image_url": "<THUMBNAIL_URL>", 
      "video_id": "<VIDEO_ID>" 
    } 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Updating Ad Creatives

```bash
curl \
  -F 'name=New creative name' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

### Deleting Ad Creatives

```bash
curl -X DELETE \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>/
```

## Content Limits and Guidelines

### Field Limits

- **Maximum ad title length**: 25 characters (recommended)
- **Minimum ad title length**: 1 character
- **Maximum ad body length**: 90 characters (recommended)
- **Minimum ad body length**: 1 character
- **Maximum URL length**: 1000 characters
- **Maximum word length**: 30 characters (recommended)
- **Creative name limit**: 100 characters

### Content Rules

- Cannot start with punctuation: `\ / ! . ? - * ( ) , ; :`
- Cannot have consecutive punctuation except three full-stops `...`
- Words no longer than 30 characters
- Only three 1-character words allowed
- Special character restrictions vary by ad type

### Prohibited Characters

- IPA Symbols (except specific exceptions)
- Standalone diacritical marks
- Superscript and subscript characters (except ™ and ℠)
- Special characters: `^~_={}[]|<>`

## Key Fields

### Core Fields

- `id`: Unique numeric string identifier
- `account_id`: Ad account ID
- `name`: Creative name in library
- `status`: ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED
- `object_story_spec`: Specification for creating unpublished page posts
- `object_story_id`: ID of existing page post to use

### Authorization Fields

- `authorization_category`: POLITICAL, POLITICAL_WITH_DIGITALLY_CREATED_MEDIA
- `effective_authorization_category`: System-determined political classification

### Media Fields

- `image_hash`: Image from ad account library
- `image_url`: External image URL
- `video_id`: Facebook video object ID
- `thumbnail_url`: Thumbnail image URL

### Targeting Fields

- `call_to_action_type`: Button type and behavior
- `object_type`: Type of Facebook object being advertised
- `link_url`: Destination URL for link ads

## Limits and Restrictions

- Maximum 50,000 ad creatives returned (pagination unavailable beyond this)
- Image size limit: 8 MB for page post images
- Placement restrictions apply based on creative type

## Related Resources

- App Ads
- Video & Carousel Ads
- Advantage+ Catalog Ads
- Instagram Ads
- WhatsApp Click-to-Message Ads
- Lead Ads

## Examples
Basic link ad creation with image and message

Carousel ad with multiple products and descriptions

Political ad creative with authorization category

Video page like ad with call to action

Reading creative with thumbnail specifications

Partnership ads with branded content sponsor

URL tags for tracking parameters

---
**Tags:** facebook-marketing-api, ad-creative, advertising, creative-management, political-ads, carousel-ads, video-ads, instagram-ads, branded-content
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-creative
**Processed:** 2025-06-25T15:41:48.868Z