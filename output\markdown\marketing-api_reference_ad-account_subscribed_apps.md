# Ad Account Subscribed Apps

Graph API Version

[v23.0](#)

# Ad Account Subscribed Apps

## Reading

Get list of apps subscribed to the ad account

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fsubscribed_apps&version=v23.0)

```
`GET /v23.0/{ad-account-id}/subscribed_apps HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of AdAccountSubscribedApps nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

## Creating

You can't perform this operation on this endpoint.

## Updating

You can update an [Application](/docs/graph-api/reference/application/) by making a POST request to [`/act_{ad_account_id}/subscribed_apps`](/docs/marketing-api/reference/ad-account/subscribed_apps/).

### Parameters

Parameter

Description

`app_id`

string

Default value:

the id of app to be subscribed from ad account

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

Struct {

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

## Deleting

You can dissociate an [Application](/docs/graph-api/reference/application/) from an [AdAccount](/docs/marketing-api/reference/ad-account/) by making a DELETE request to [`/act_{ad_account_id}/subscribed_apps`](/docs/marketing-api/reference/ad-account/subscribed_apps/).

### Parameters

Parameter

Description

`app_id`

string

Default value:

the id of app to be unsubscribed from ad account

### Return Type

Struct {

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter