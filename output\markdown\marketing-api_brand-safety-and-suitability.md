# Brand Safety and Suitability

On This Page

[Brand Safety and Suitability](#brand-safety-and-suitability)

[Documentation Links](#documentation-links)

[Integration Setup](#integration-setup)

[Block Lists API](#block-lists-api)

[Content Allow Lists API](#content-allow-lists-api)

[Content Delivery Reports API](#content-delivery-reports-api)

[Feed Verification API](#feed-verification-api)

[Partner-publisher Lists API](#partner-publisher-lists-api)

[Passback API](#passback-api)

[Publisher Delivery Reports API](#publisher-delivery-reports-api)

# Brand Safety and Suitability

Meta offers several brand suitability controls to help you place ads adjacent to organic content that is more suitable for your brand on Facebook, Instagram and Meta Audience Network. You can apply one of these controls or use them in combination. Meta keeps your brand safe by enforcing [Facebook Community Standards](https://www.facebook.com/business/brand-safety/media-responsibility) and [Instagram Community Guidelines](https://www.facebook.com/help/instagram/***************) for all content and publishers. [Learn more about brand suitability](https://www.facebook.com/business/help/****************?id=****************).

## Documentation Links

### [Integration Setup](/docs/marketing-api/brand-safety-and-suitability/brand-safety-partners)

An overview of initial setup steps required for program participation. The main elements it addresses include: setting up a business in Business Manager, creating and obtaining access to ad accounts, and creating an app to access Meta’s API.

### [Block Lists API](/docs/marketing-api/brand-safety-and-suitability/block-list)

Block lists stop your ads from appearing with publishers you don't consider suitable for your brand or campaign.

### [Content Allow Lists API](/docs/marketing-api/brand-safety-and-suitability/content-allow-lists)

Content Allow Lists give you the ability to work with trusted Meta Business Partners to review and customize lists of brand suitable videos for running Facebook in-stream campaigns.

### [Content Delivery Reports API](/docs/marketing-api/brand-safety-and-suitability/content-delivery-report)

Content delivery reports provide transparency into where ads appeared and show impressions at the content level.

### [Feed Verification API](/docs/marketing-api/brand-safety-and-suitability/feed-verification)

Feed verification allows you to measure, verify and understand the suitability of content near your ads to help you make informed decisions in order to reach your marketing goals.

### [Partner-publisher Lists API](/docs/marketing-api/brand-safety-and-suitability/publisher-list)

Partner-publisher lists show publishers that have signed up for monetization and follow our Partner Monetization Policies.

### [Passback API](/docs/marketing-api/brand-safety-and-suitability/passback-api)

Passback allows Meta Business Partners to share content risk labels and campaign performance data with Meta. The goals are to provide advertisers and partners with a mechanism to give feedback on content, for Meta to be able to take action on that feedback, and for Meta and partners to be able to compare content labels.

### [Publisher Delivery Reports API](/docs/marketing-api/brand-safety-and-suitability/publisher-delivery-report)

Publisher delivery reports provide transparency into where ads appeared and show impressions at the publisher level.

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)