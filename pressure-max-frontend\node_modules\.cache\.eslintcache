[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8"}, {"size": 232, "mtime": 1750870627179, "results": "9", "hashOfConfig": "10"}, {"size": 1992, "mtime": 1750870863192, "results": "11", "hashOfConfig": "10"}, {"size": 10186, "mtime": 1750872635989, "results": "12", "hashOfConfig": "10"}, {"size": 7989, "mtime": 1750870820653, "results": "13", "hashOfConfig": "10"}, {"size": 6092, "mtime": 1750870791114, "results": "14", "hashOfConfig": "10"}, {"size": 8227, "mtime": 1750870850894, "results": "15", "hashOfConfig": "10"}, {"size": 3438, "mtime": 1750870723227, "results": "16", "hashOfConfig": "10"}, {"size": 3195, "mtime": 1750870706758, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["42"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["43", "44"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["45"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], {"ruleId": "46", "severity": 1, "message": "47", "line": 41, "column": 6, "nodeType": "48", "endLine": 41, "endColumn": 23, "suggestions": "49"}, {"ruleId": "50", "severity": 1, "message": "51", "line": 8, "column": 28, "nodeType": "52", "messageId": "53", "endLine": 8, "endColumn": 32}, {"ruleId": "46", "severity": 1, "message": "54", "line": 22, "column": 6, "nodeType": "48", "endLine": 22, "endColumn": 23, "suggestions": "55"}, {"ruleId": "46", "severity": 1, "message": "56", "line": 22, "column": 6, "nodeType": "48", "endLine": 22, "endColumn": 8, "suggestions": "57"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["58"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["59"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["60"], {"desc": "61", "fix": "62"}, {"desc": "63", "fix": "64"}, {"desc": "65", "fix": "66"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "67", "text": "68"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "69", "text": "70"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "71", "text": "72"}, [1445, 1462], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]"]