[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8"}, {"size": 232, "mtime": 1750870627179, "results": "9", "hashOfConfig": "10"}, {"size": 1992, "mtime": 1750870863192, "results": "11", "hashOfConfig": "10"}, {"size": 8613, "mtime": 1750872098798, "results": "12", "hashOfConfig": "10"}, {"size": 7989, "mtime": 1750870820653, "results": "13", "hashOfConfig": "10"}, {"size": 6092, "mtime": 1750870791114, "results": "14", "hashOfConfig": "10"}, {"size": 8227, "mtime": 1750870850894, "results": "15", "hashOfConfig": "10"}, {"size": 3438, "mtime": 1750870723227, "results": "16", "hashOfConfig": "10"}, {"size": 3195, "mtime": 1750870706758, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", ["42", "43"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["44"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["45", "46"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["47"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 5, "column": 37, "nodeType": "50", "messageId": "51", "endLine": 5, "endColumn": 45}, {"ruleId": "48", "severity": 1, "message": "52", "line": 40, "column": 9, "nodeType": "50", "messageId": "51", "endLine": 40, "endColumn": 28}, {"ruleId": "53", "severity": 1, "message": "54", "line": 41, "column": 6, "nodeType": "55", "endLine": 41, "endColumn": 23, "suggestions": "56"}, {"ruleId": "48", "severity": 1, "message": "57", "line": 8, "column": 28, "nodeType": "50", "messageId": "51", "endLine": 8, "endColumn": 32}, {"ruleId": "53", "severity": 1, "message": "58", "line": 22, "column": 6, "nodeType": "55", "endLine": 22, "endColumn": 23, "suggestions": "59"}, {"ruleId": "53", "severity": 1, "message": "60", "line": 22, "column": 6, "nodeType": "55", "endLine": 22, "endColumn": 8, "suggestions": "61"}, "no-unused-vars", "'Facebook' is defined but never used.", "Identifier", "unusedVar", "'handleFacebookLogin' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["62"], "'user' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["63"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["64"], {"desc": "65", "fix": "66"}, {"desc": "67", "fix": "68"}, {"desc": "69", "fix": "70"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "71", "text": "72"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "73", "text": "74"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "75", "text": "76"}, [1445, 1462], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]"]