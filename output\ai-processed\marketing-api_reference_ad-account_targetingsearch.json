{"title": "Facebook Marketing API - Ad Account Targeting Search", "summary": "The Ad Account Targeting Search endpoint provides a unified search interface to retrieve targeting descriptors with query parameters. This read-only endpoint allows advertisers to search for various targeting options like interests, demographics, locations, and behaviors for their ad campaigns.", "content": "# Ad Account Targeting Search\n\n## Overview\n\nThe Ad Account Targeting Search endpoint is a unified search interface that allows you to retrieve targeting descriptors using query parameters. This endpoint is essential for building targeting options in Facebook ad campaigns.\n\n## Endpoint\n\n```\nGET /v23.0/{ad-account-id}/targetingsearch\n```\n\n## Parameters\n\n### Required Parameters\n\n- **`q`** (string): Search query term\n\n### Optional Parameters\n\n- **`allow_only_fat_head_interests`** (boolean): Restrict results to only pre-vetted interests\n- **`app_store`** (enum): Specify the app store for app install campaigns. Options include:\n  - amazon_app_store, google_play, itunes, itunes_ipad\n  - fb_canvas, fb_gameroom, windows_store, fb_android_store\n  - windows_10_store, roku_channel_store, instant_game\n  - oculus_app_store, galaxy_store, and others\n- **`limit_type`** (enum): Restrict the type of targeting audience to retrieve. Extensive options including:\n  - Demographics: genders, age_min, age_max, countries, cities\n  - Interests: interests, behaviors, life_events\n  - Connections: custom_audiences, excluded_custom_audiences\n  - Placements: facebook_positions, instagram_positions, messenger_positions\n- **`objective`** (enum): Campaign objective such as:\n  - APP_INSTALLS, BRAND_AWARENESS, CONVERSIONS\n  - LEAD_GENERATION, LINK_CLICKS, PAGE_LIKES\n  - OUTCOME_SALES, OUTCOME_TRAFFIC, VIDEO_VIEWS\n- **`regulated_categories`** (array): Specify regulated campaign categories:\n  - EMPLOYMENT, HOUSING, CREDIT\n  - ISSUES_ELECTIONS_POLITICS\n  - ONLINE_GAMBLING_AND_GAMING\n  - FINANCIAL_PRODUCTS_SERVICES\n\n## Response Format\n\nThe endpoint returns a JSON object with:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n- **`data`**: Array of AdAccountTargetingUnified nodes\n- **`paging`**: Pagination information for large result sets\n\n## Example Request\n\n```http\nGET /v23.0/{ad-account-id}/targetingsearch?q=technology HTTP/1.1\nHost: graph.facebook.com\n```\n\n## Error Codes\n\n- **100**: Invalid parameter\n- **80004**: Too many API calls - rate limiting applied\n- **190**: Invalid OAuth 2.0 Access Token\n- **200**: Permissions error\n- **368**: Action deemed abusive or disallowed\n\n## Limitations\n\n- **Read-only endpoint**: Creating, updating, and deleting operations are not supported\n- **Rate limiting**: Subject to Facebook's ads management rate limits\n- **Permissions**: Requires appropriate access tokens and permissions", "keyPoints": ["Unified search endpoint for retrieving targeting descriptors with query parameters", "Supports extensive filtering options including demographics, interests, behaviors, and placements", "Read-only endpoint - no create, update, or delete operations allowed", "Subject to rate limiting and requires proper OAuth 2.0 authentication", "Returns paginated results with AdAccountTargetingUnified nodes"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/targetingsearch"], "parameters": ["q (required)", "allow_only_fat_head_interests", "app_store", "limit_type", "objective", "regulated_categories"], "examples": ["GET /v23.0/{ad-account-id}/targetingsearch HTTP/1.1"], "tags": ["Facebook Marketing API", "targeting", "search", "ad account", "audience", "demographics", "interests"], "relatedTopics": ["Graph API", "Ad Campaign Objectives", "Custom Audiences", "Rate Limiting", "OAuth 2.0 Authentication", "Pagination", "Regulated Categories"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/targetingsearch/", "processedAt": "2025-06-25T15:40:31.669Z", "processor": "openrouter-claude-sonnet-4"}