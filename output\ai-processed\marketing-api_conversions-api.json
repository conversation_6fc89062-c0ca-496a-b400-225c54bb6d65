{"title": "Facebook Conversions API - Server-Side Event Tracking", "summary": "The Conversions API enables advertisers to send marketing data (website events, app events, offline conversions) directly from their servers to Meta systems for improved ad targeting, cost optimization, and measurement. It provides a unified connection point for multiple event types, simplifying the technology stack while maintaining compatibility with Meta Pixel and other tracking methods.", "content": "# Conversions API\n\nThe Conversions API is designed to create a connection between an advertiser's marketing data (such as website events, app events, business messaging events and offline conversions) from an advertiser's server, website platform, mobile app, or CRM to Meta systems that optimize ad targeting, decrease cost per result and measure outcomes.\n\nRather than maintaining separate connection points for each data source, advertisers are able to leverage the Conversions API to send multiple event types and simplify their technology stack. In the case of direct integrations, this entails establishing a connection between an advertiser's server and Meta's Conversions API endpoint.\n\nServer events are linked to a dataset ID and are processed like events sent using the Meta Pixel, Facebook SDK for iOS or Android, mobile measurement partner SDK, offline event set, or .csv upload. This means that server events may be used in measurement, reporting, or optimization in a similar way as other connection channels. Offline events may be used for attributed offline events measurement, offline custom audience creation or measurement.\n\nFor optimal ad performance and measurement, we recommend that advertisers follow the [Conversions API best practices](/docs/marketing-api/conversions-api/best-practices).\n\n## Recommended Steps\n\n1. **[Get Started](/docs/marketing-api/conversions-api/get-started)**: Choose the integration method that works best for you, see prerequisites for using the API, and understand where to begin.\n2. **[Implement the API and start sending requests](/docs/marketing-api/conversions-api/using-the-api)**: Start making `POST` requests and learn more about dropped events, batch requests, and event transaction time.\n3. **[Verify your setup](/docs/marketing-api/conversions-api/verifying-setup)**: Confirm that we have received your events and that events are deduplicated and matched correctly.\n\n## Documentation\n\n### [API Parameters](/docs/marketing-api/conversions-api/parameters)\nRequired and optional parameters you can use to improve ads attribution and delivery optimization.\n\n### [Payload Helper](/docs/marketing-api/conversions-api/payload-helper)\nSee how your payload should be structured when it is sent to Facebook from your server.\n\n### [Troubleshooting](/docs/marketing-api/conversions-api/support)\nLearn how to handle error codes returned by the Conversions API.\n\n## Resources\n\n### Meta Pixel Events\nLearn more about the Meta Pixel's [Standard Events](/docs/facebook-pixel/implementation/conversion-tracking#standard-events) and [Custom Events](/docs/facebook-pixel/implementation/conversion-tracking#custom-events).\n\n### Business Help Center\nFrom our Help Center, see [About Conversions API](https://www.facebook.com/business/help/2041148702652965) and [Test Your Server Events](https://www.facebook.com/business/help/1624255387706033).\n\n### Playbook and Webinar\nView the [Direct Integration Playbook for Developers (PDF)](https://www.facebook.com/gms_hub/share/conversions-api-direct-integration-playbook_english.pdf) and [Direct Integration Webinar for Developers](https://www.facebook.com/business/m/sessionsforsuccess/conversions-api).\n\n### [Data Processing Options](/docs/marketing-apis/data-processing-options)\nLearn more about the Limited Data Use feature and how to implement it for Conversions API.", "keyPoints": ["Conversions API creates server-to-server connections for sending marketing data directly to Meta systems", "Supports multiple event types (website, app, offline, business messaging) through a single unified API", "Server events are processed similarly to Meta Pixel events and can be used for measurement, reporting, and optimization", "Requires three main steps: getting started, implementing POST requests, and verifying setup", "Helps improve ad targeting, decrease cost per result, and measure campaign outcomes"], "apiEndpoints": ["Meta's Conversions API endpoint"], "parameters": ["dataset ID", "server events", "event transaction time"], "examples": ["POST requests for sending events", "Batch requests implementation", "Event payload structure"], "tags": ["conversions-api", "server-side-tracking", "facebook-marketing-api", "event-tracking", "ad-optimization", "measurement", "meta-pixel", "server-events"], "relatedTopics": ["<PERSON><PERSON>", "Facebook SDK for iOS", "Facebook SDK for Android", "Standard Events", "Custom Events", "Data Processing Options", "Limited Data Use", "Offline Events", "Custom Audiences", "Ad Attribution", "Event Deduplication"], "difficulty": "intermediate", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-api/conversions-api", "processedAt": "2025-06-25T15:50:03.706Z", "processor": "openrouter-claude-sonnet-4"}