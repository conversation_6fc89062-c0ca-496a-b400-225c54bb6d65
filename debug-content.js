const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('https://developers.facebook.com/docs/marketing-apis');
  await page.waitForLoadState('networkidle');
  
  console.log('Analyzing content structure...');
  
  // Test the data-click-area="main" selector
  const mainContent = await page.locator('[data-click-area="main"]').innerHTML();
  console.log('Main content length:', mainContent.length);
  console.log('Main content preview:', mainContent.substring(0, 200));
  
  // Try to find just the documentation content without navigation
  const docContent = await page.evaluate(() => {
    // Look for the main documentation content
    const mainElement = document.querySelector('[data-click-area="main"]');
    if (!mainElement) return null;
    
    // Clone the element to avoid modifying the original
    const clone = mainElement.cloneNode(true);
    
    // Remove navigation elements
    const toRemove = [
      '.hidden_elem',
      '[data-click-area="to_top_nav"]',
      'script',
      'noscript',
      'style',
      '._2k32',
      '._1dyy', // Table of contents
      'fb\\:like'
    ];
    
    toRemove.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
    
    return clone.innerHTML;
  });
  
  console.log('\nCleaned content length:', docContent ? docContent.length : 0);
  console.log('Cleaned content preview:', docContent ? docContent.substring(0, 300) : 'null');
  
  // Also try to get just the main sections
  const sections = await page.evaluate(() => {
    const sections = document.querySelectorAll('._4-u2 ._4-u3');
    return Array.from(sections).map(section => ({
      html: section.innerHTML.substring(0, 200),
      textLength: section.textContent.length
    }));
  });
  
  console.log('\nFound sections:', sections.length);
  sections.forEach((section, i) => {
    console.log(`Section ${i + 1}: ${section.textLength} chars`);
    console.log(`Preview: ${section.html}...`);
    console.log('');
  });
  
  await browser.close();
})();
