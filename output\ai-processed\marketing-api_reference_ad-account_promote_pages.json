{"title": "Facebook Marketing API - Ad Account Promote Pages Reference", "summary": "This endpoint allows you to retrieve all promoted Pages associated with a specific Ad Account. It's a read-only endpoint that requires specific permissions to access Page data and manage ads for those Pages.", "content": "# Ad Account Promote Pages\n\n## Overview\n\nThe Ad Account Promote Pages endpoint allows you to get all the promoted Pages for an Ad Account. This is a read-only endpoint that returns a list of Page objects.\n\n## Reading\n\n### Requirements\n\nTo use this endpoint, you need the following permissions:\n\n- `pages_show_list` permission - to get access to your app user's Pages\n- `pages_manage_ads` permission - to get access to create and manage ads for your app user's Pages\n\n### Endpoint\n\n```\nGET /v23.0/{ad-account-id}/promote_pages\n```\n\n### Example Request\n\n```http\nGET /v23.0/{ad-account-id}/promote_pages HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK Example\n\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/promote_pages',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK Example\n\n```javascript\nFB.api(\n    \"/{ad-account-id}/promote_pages\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Parameters\n\nThis endpoint doesn't have any parameters.\n\n### Response Format\n\nThe response returns a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Fields\n\n- **data**: A list of Page nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 80004 | Too many calls to this ad-account. Wait and try again. Refer to rate limiting documentation. |\n| 190 | Invalid OAuth 2.0 Access Token |\n\n## Operations\n\n- **Creating**: Not supported on this endpoint\n- **Updating**: Not supported on this endpoint  \n- **Deleting**: Not supported on this endpoint", "keyPoints": ["Read-only endpoint that retrieves all promoted Pages for an Ad Account", "Requires pages_show_list and pages_manage_ads permissions", "Returns a list of Page objects with pagination support", "Does not support create, update, or delete operations", "Subject to rate limiting with specific error codes for different failure scenarios"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/promote_pages"], "parameters": ["ad-account-id (path parameter)", "access-token (required for authentication)"], "examples": ["HTTP GET request example", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest example", "iOS SDK FBSDKGraphRequest example"], "tags": ["Facebook Marketing API", "Ad Account", "Pages", "Promote Pages", "Graph API", "Read Operations"], "relatedTopics": ["Page objects", "Graph API pagination", "OAuth 2.0 Access Tokens", "Rate limiting", "Facebook SDK implementations", "Permission requirements"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/promote_pages/", "processedAt": "2025-06-25T15:32:39.399Z", "processor": "openrouter-claude-sonnet-4"}