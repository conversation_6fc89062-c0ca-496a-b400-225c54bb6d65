{"generatedAt": "2025-06-25T16:07:12.946Z", "summary": {"total": 83, "needsReprocessing": 44, "averageScore": 67.*************}, "qualityThreshold": 60, "needsReprocessing": [{"filename": "marketing-api_reference_high-demand-period", "score": 0, "issues": ["High markup-to-content ratio (96.3%)", "Very short content (22 chars)", "Very short content in original JSON", "Very short processed content", "Reference page missing API endpoints", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_ad_place_page_sets_async", "score": 25, "issues": ["Very short content (175 chars)", "Very short content in original JSON", "Very short processed content", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_connected_instagram_accounts", "score": 30, "issues": ["Very short content (179 chars)", "Very short content in original JSON", "Reference page missing API endpoints", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_activities", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_adcreatives", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Missing or very short title"]}, {"filename": "marketing-api_reference_ad-account_adimages", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Missing or very short title"]}, {"filename": "marketing-api_reference_ad-account_adlabels", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Missing or very short title"]}, {"filename": "marketing-api_reference_ad-account_ads_reporting_mmm_reports", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_ads_reporting_mmm_schedulers", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_campaigns", "score": 40, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements", "Missing or very short title"]}, {"filename": "marketing-api_reference_ad-account_dsa_recommendations", "score": 40, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_impacting_ad_studies", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Reference page missing parameters"]}, {"filename": "marketing-api_reference_ad-account_insights", "score": 40, "issues": ["Excessive navigation elements (8)", "Contains 4 script tags", "Contains table of contents elements", "Missing or very short title"]}, {"filename": "marketing-api_reference_ad-account_mcmeconversions", "score": 40, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements", "Reference page missing parameters"]}, {"filename": "marketing-api_best-practices", "score": 50, "issues": ["Excessive navigation elements (13)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_creative", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_get-started", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_insights", "score": 50, "issues": ["Excessive navigation elements (14)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference", "score": 50, "issues": ["Excessive navigation elements (9)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_account_controls", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_ad_place_page_sets", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_adplayables", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_adrules_library", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_ads", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_adsets", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_adspixels", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_advertisable_applications", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_advideos", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_agencies", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_applications", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_assigned_users", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_async_batch_requests", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_asyncadcreatives", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_asyncadrequestsets", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_broadtargetingcategories", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_customaudiences", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_customaudiencestos", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_customconversions", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_delivery_estimate", "score": 50, "issues": ["Excessive navigation elements (7)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_deprecatedtargetingadsets", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_instagram_accounts", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_minimum_budgets", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}, {"filename": "marketing-api_reference_ad-account_product_audiences", "score": 50, "issues": ["Excessive navigation elements (6)", "Contains 4 script tags", "Contains table of contents elements"]}], "topIssues": [{"issue": "Contains table of contents elements", "count": 48}, {"issue": "Contains 4 script tags", "count": 46}, {"issue": "Excessive navigation elements (6)", "count": 27}, {"issue": "Excessive navigation elements (7)", "count": 10}, {"issue": "Reference page missing parameters", "count": 9}, {"issue": "Missing or very short title", "count": 6}, {"issue": "Very short content in original JSON", "count": 3}, {"issue": "Very short processed content", "count": 2}, {"issue": "Reference page missing API endpoints", "count": 2}, {"issue": "Contains 3 script tags", "count": 2}], "recommendations": ["Reprocess 44 pages with improved content extraction", "Address top issue: \"Contains table of contents elements\" affecting 48 pages", "Overall content quality is below optimal - consider full reprocessing"]}