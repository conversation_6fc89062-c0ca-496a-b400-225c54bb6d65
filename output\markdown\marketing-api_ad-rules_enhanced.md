# Facebook Marketing API - Ad Rules Engine

## Summary
The Ad Rules Engine is a central rule management service that automates ad management through schedule-based or trigger-based rules. It eliminates the need for manual monitoring and actions by expressing conditions as logical expressions that can automatically manage ad performance.

## Key Points
- Automates ad management through logical expressions and conditions
- Supports both schedule-based and trigger-based rule execution
- Eliminates manual monitoring and intervention for ad performance management
- Core components include Evaluation Spec, Execution Spec, and Change Spec
- Provides specialized rules for budget rebalancing and ROAS optimization

## Parameters
- Evaluation Spec
- Execution Spec
- Change Spec

## Content
# Ad Rules Engine

A central rule management service that helps you easily, efficiently and intelligently manage ads. Without it, you must query the Marketing API to monitor an ad's performance and manually take actions on certain conditions. Since we can express most conditions as logical expressions, we can automate management two ways: using **Schedule-based** or **Trigger-based** rules.

New to this? Try the rules-based notification quickstart in your App Dashboard, Quickstarts.

## Documentation Contents

### Overview

Core concepts and usage requirements. Learn about:
- **Evaluation Spec** - Defines the conditions to evaluate
- **Execution Spec** - Specifies when and how rules are executed
- **Change Spec** - Defines what changes to make when conditions are met

### Guides

Use case based guides covering:
- **Trigger Based Ad Rules** - Rules that execute when specific conditions are met
- **Schedule Based Rules** - Rules that execute on a predefined schedule
- **Advanced Scheduling** - Complex scheduling configurations
- **Rebalance Budget Ad Rules** - Automatically redistribute budgets based on performance
- **ROAS Ad Rules** - Rules based on Return on Ad Spend metrics
- **API Calls** - Implementation details for API integration

---
**Tags:** ad-rules, automation, marketing-api, ad-management, facebook-ads, rule-engine
**Difficulty:** intermediate
**Content Type:** overview
**Source:** https://developers.facebook.com/docs/marketing-api/ad-rules
**Processed:** 2025-06-25T15:06:47.017Z