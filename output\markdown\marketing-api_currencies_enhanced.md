# Facebook Marketing API Currency Codes Reference

## Summary
Complete reference guide for all currency codes supported by Facebook Marketing API ad accounts. Includes currency names, ISO codes, and offset values that determine how Facebook handles currency subdivisions and minimum bid calculations.

## Key Points
- Facebook Marketing API supports 70+ currencies for ad accounts worldwide
- Each currency has a specific offset value (1 or 100) that determines bid calculation
- Offset 100 means minimum bid represents 1/100 of base unit (e.g., cents for USD)
- Offset 1 means minimum bid represents the full base unit (e.g., whole yen for JPY)
- Currency codes follow standard ISO format (USD, EUR, JPY, etc.)

## API Endpoints
- `/docs/reference/ads-api/adaccount/`

## Parameters
- currency_code
- offset
- bid_amount

## Content
# Currency Codes

The Marketing API supports all currencies that are supported by [ad accounts](/docs/reference/ads-api/adaccount/).

## Supported Currencies

| Name | Code | Offset |
|------|------|--------|
| Algerian Dinar | DZD | 100 |
| Argentine Peso | ARS | 100 |
| Australian Dollar | AUD | 100 |
| Bahraini Dinar | BHD | 100 |
| Bangladeshi Taka | BDT | 100 |
| Bolivian Boliviano | BOB | 100 |
| Bulgarian Lev | BGN | 100 |
| Brazilian Real | BRL | 100 |
| British Pound | GBP | 100 |
| Canadian Dollar | CAD | 100 |
| Chilean Peso | CLP | 1 |
| Chinese Yuan | CNY | 100 |
| Colombian Peso | COP | 1 |
| Costa Rican Colon | CRC | 1 |
| Croatian Kuna | HRK | 100 |
| Czech Koruna | CZK | 100 |
| Danish Krone | DKK | 100 |
| Egyptian Pound | EGP | 100 |
| Euro | EUR | 100 |
| Guatemalan Quetzal | GTQ | 100 |
| Honduran Lempira | HNL | 100 |
| Hong Kong Dollar | HKD | 100 |
| Hungarian Forint | HUF | 1 |
| Iceland Krona | ISK | 1 |
| Indian Rupee | INR | 100 |
| Indonesian Rupiah | IDR | 1 |
| Israeli New Shekel | ILS | 100 |
| Japanese Yen | JPY | 1 |
| Jordanian Dinar | JOD | 100 |
| Kenyan Shilling | KES | 100 |
| Korean Won | KRW | 1 |
| Latvian Lats | LVL | 100 |
| Lithuanian Litas | LTL | 100 |
| Macau Patacas | MOP | 100 |
| Malaysian Ringgit | MYR | 100 |
| Mexican Peso | MXN | 100 |
| New Zealand Dollar | NZD | 100 |
| Nicaraguan Cordoba | NIO | 100 |
| Nigerian Naira | NGN | 100 |
| Norwegian Krone | NOK | 100 |
| Pakistani Rupee | PKR | 100 |
| Paraguayan Guarani | PYG | 1 |
| Peruvian Nuevo Sol | PEN | 100 |
| Philippine Peso | PHP | 100 |
| Polish Zloty | PLN | 100 |
| Qatari Rials | QAR | 100 |
| Romanian Leu | RON | 100 |
| Russian Ruble | RUB | 100 |
| Saudi Arabian Riyal | SAR | 100 |
| Serbian Dinar | RSD | 100 |
| Singapore Dollar | SGD | 100 |
| Slovak Koruna | SKK | 100 |
| South African Rand | ZAR | 100 |
| Swedish Krona | SEK | 100 |
| Swiss Franc | CHF | 100 |
| Taiwan Dollar | TWD | 1 |
| Thai Baht | THB | 100 |
| Turkish Lira | TRY | 100 |
| UAE Dirham | AED | 100 |
| Ukrainian Hryvnia | UAH | 100 |
| US Dollars | USD | 100 |
| Uruguay Peso | UYU | 100 |
| Venezuelan Bolivar | VEF | 100 |
| Vietnamese Dong | VND | 1 |
| Credits | FBZ | 100 |
| Bolivar Soberano | VES | 100 |

## Currency Offset

Each currency has an offset value that specifies how Facebook handles currency subdivisions. This ensures that the minimum bid value of "1" is usable for each currency.

### Offset 100 Example

When a currency has an offset of 100, the minimum allowed bid represents 1/100 of the currency base unit.

**Example:** For USD (offset 100), a bid of "1" equals $0.01 USD.

### Offset 1 Example

When a currency has an offset of 1, the minimum allowed bid represents the currency base unit.

**Example:** For JPY (offset 1), a bid of "1" equals ¥1 JPY.

## Examples
USD with offset 100: bid "1" = $0.01

JPY with offset 1: bid "1" = ¥1

---
**Tags:** currency, marketing-api, ad-accounts, bidding, international, reference
**Difficulty:** beginner
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/currencies
**Processed:** 2025-06-25T15:45:42.041Z