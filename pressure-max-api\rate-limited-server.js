const express = require('express');
const cors = require('cors');
const app = express();

// Rate limiting and caching setup
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache
const API_DELAY = 2000; // 2 second delay between API calls
let lastApiCall = 0;

// Rate limiting helper
async function rateLimitedApiCall(apiCallFunction) {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  
  if (timeSinceLastCall < API_DELAY) {
    const waitTime = API_DELAY - timeSinceLastCall;
    console.log(`⏱️ Rate limiting: waiting ${waitTime}ms before next API call`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  lastApiCall = Date.now();
  return await apiCallFunction();
}

// Cache helper functions
function getCacheKey(endpoint, params) {
  return `${endpoint}_${JSON.stringify(params)}`;
}

function getFromCache(key) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log(`💾 Cache hit for ${key}`);
    return cached.data;
  }
  return null;
}

function setCache(key, data) {
  cache.set(key, {
    data: data,
    timestamp: Date.now()
  });
  console.log(`💾 Cached data for ${key}`);
}

// Middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true
}));

app.use(express.json());

// Helper function to ensure ad account ID has proper format
function ensureActPrefix(adAccountId) {
  if (adAccountId.startsWith('act_')) {
    return adAccountId;
  }
  return `act_${adAccountId}`;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// User profile endpoint with real Facebook data
app.get('/api/v1/users/profile', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';
    
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get('https://graph.facebook.com/v23.0/me', {
        params: {
          fields: 'id,name,email,first_name,last_name,picture',
          access_token: accessToken
        }
      });
    });

    const fbUser = response.data;
    console.log('Facebook User Profile:', fbUser);

    res.json({
      id: `fb_${fbUser.id}`,
      email: fbUser.email || '<EMAIL>',
      firstName: fbUser.first_name || 'Facebook',
      lastName: fbUser.last_name || 'User',
      name: fbUser.name,
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      picture: fbUser.picture?.data?.url,
      facebookId: fbUser.id
    });
  } catch (error) {
    console.error('Facebook user profile error:', error.response?.data || error.message);
    res.json({
      id: 'user_fb_demo',
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      error: 'Real API call failed, showing demo data'
    });
  }
});

// Ad accounts endpoint with caching
app.get('/api/v1/facebook/ad-accounts', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Check cache first
    const cacheKey = getCacheKey('ad-accounts', {});
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get('https://graph.facebook.com/v23.0/me/adaccounts', {
        params: {
          fields: 'id,name,account_status,currency,timezone_name,business,account_id',
          access_token: accessToken,
          limit: 50
        }
      });
    });

    console.log('Facebook Ad Accounts Response:', response.data);
    const adAccounts = response.data.data || [];
    
    // Cache the result
    setCache(cacheKey, adAccounts);
    
    res.json(adAccounts);
  } catch (error) {
    console.error('Facebook ad accounts error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Pages endpoint with caching and rate limiting
app.get('/api/v1/facebook/pages', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching Facebook pages...');

    // Check cache first
    const cacheKey = getCacheKey('pages', {});
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached pages data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get('https://graph.facebook.com/v23.0/me/accounts', {
        params: {
          fields: 'id,name,category,category_list,link,picture,access_token,fan_count,followers_count,about,description,website,phone,location,hours,parking,payment_options,price_range,restaurant_services,restaurant_specialties,general_info',
          access_token: accessToken,
          limit: 50
        }
      });
    });

    console.log('✅ Facebook pages retrieved:', response.data.data?.length || 0, 'pages');

    const pages = response.data.data || [];

    // Cache the result
    setCache(cacheKey, pages);

    res.json(pages);
  } catch (error) {
    console.error('❌ Facebook pages error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Campaigns endpoint with caching and rate limiting (no insights for now)
app.get('/api/v1/facebook/campaigns/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching campaigns for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached campaigns data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, {
        params: {
          fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,configured_status,effective_status,start_time,stop_time,bid_strategy,buying_type,special_ad_categories,spend_cap,pacing_type,promoted_object,issues_info,recommendations',
          access_token: accessToken,
          limit: 25 // Reduced limit to avoid rate limits
        }
      });
    });

    console.log('✅ Facebook campaigns retrieved:', response.data.data?.length || 0, 'campaigns');

    // Add basic insights structure for frontend compatibility (skip API calls for now)
    const campaigns = response.data.data || [];
    const campaignsWithBasicInsights = campaigns.map(campaign => ({
      ...campaign,
      insights: {
        impressions: '0',
        clicks: '0',
        spend: '0.00',
        cpm: '0.00',
        cpc: '0.00',
        ctr: '0.00',
        reach: '0',
        frequency: '0.00',
        actions: [],
        cost_per_action_type: []
      }
    }));

    // Cache the result
    setCache(cacheKey, campaignsWithBasicInsights);

    res.json(campaignsWithBasicInsights);
  } catch (error) {
    console.error('❌ Facebook campaigns error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Campaign creation endpoint with rate limiting
app.post('/api/v1/facebook/campaigns', async (req, res) => {
  try {
    const { adAccountId, name, objective, status = 'PAUSED' } = req.body;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🚀 Creating Facebook campaign:', { adAccountId, name, objective, status });

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
        params: {
          name: name,
          objective: objective,
          status: status,
          special_ad_categories: '[]',
          access_token: accessToken
        }
      });
    });

    console.log('✅ Facebook campaign created successfully:', response.data);

    // Clear campaigns cache to force refresh
    const cacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    cache.delete(cacheKey);
    console.log('🗑️ Cleared campaigns cache');

    res.json({
      success: true,
      campaign: response.data,
      message: 'Campaign created successfully'
    });
  } catch (error) {
    console.error('❌ Facebook campaign creation error:', error.response?.data || error.message);
    
    res.json({
      error: 'Failed to create campaign in Facebook',
      details: error.response?.data || error.message,
      fallback_campaign: {
        id: `demo_${Date.now()}`,
        name: req.body.name,
        objective: req.body.objective,
        status: 'DEMO_MODE',
        created_time: new Date().toISOString(),
        note: 'Campaign creation failed, this is demo data'
      }
    });
  }
});

// Enhanced campaign creation endpoint - creates campaign + ad set + ad
app.post('/api/v1/facebook/campaigns/complete', async (req, res) => {
  try {
    const {
      adAccountId,
      campaignName,
      objective = 'OUTCOME_TRAFFIC',
      adSetName,
      dailyBudget = 1000, // $10.00 in cents
      targeting = {},
      adName,
      creative = {}
    } = req.body;

    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🚀 Creating complete Facebook campaign structure:', {
      campaignName, objective, adSetName, dailyBudget, adName
    });

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    const axios = require('axios');

    // Step 1: Create Campaign
    console.log('📝 Step 1: Creating campaign...');
    const campaignResponse = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
        params: {
          name: campaignName,
          objective: objective,
          status: 'PAUSED',
          special_ad_categories: '[]',
          access_token: accessToken
        }
      });
    });

    const campaignId = campaignResponse.data.id;
    console.log('✅ Campaign created:', campaignId);

    // Step 2: Create Ad Set
    console.log('📝 Step 2: Creating ad set...');
    const defaultTargeting = {
      geo_locations: { countries: ['US'] },
      age_min: 18,
      age_max: 65
    };

    // Merge targeting carefully to avoid conflicts
    const finalTargeting = {
      ...defaultTargeting,
      targeting_automation: {
        advantage_audience: 0 // Disable Advantage audience for manual targeting
      }
    };
    if (targeting.geo_locations) {
      finalTargeting.geo_locations = targeting.geo_locations;
    }
    if (targeting.age_min) finalTargeting.age_min = targeting.age_min;
    if (targeting.age_max) finalTargeting.age_max = targeting.age_max;
    if (targeting.interests) finalTargeting.interests = targeting.interests;
    if (targeting.behaviors) finalTargeting.behaviors = targeting.behaviors;
    if (targeting.demographics) finalTargeting.demographics = targeting.demographics;

    const adSetResponse = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, null, {
        params: {
          name: adSetName || `${campaignName} - Ad Set`,
          campaign_id: campaignId,
          daily_budget: dailyBudget,
          billing_event: 'IMPRESSIONS',
          optimization_goal: objective === 'OUTCOME_LEADS' ? 'LEAD_GENERATION' : 'LINK_CLICKS',
          bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
          targeting: JSON.stringify(finalTargeting),
          status: 'PAUSED',
          access_token: accessToken
        }
      });
    });

    const adSetId = adSetResponse.data.id;
    console.log('✅ Ad Set created:', adSetId);

    // Step 3: Create Ad Creative (if creative data provided)
    let adCreativeId = null;
    if (creative.message || creative.link_url || creative.image_hash) {
      console.log('📝 Step 3: Creating ad creative...');

      const creativeData = {
        name: `${campaignName} - Creative`,
        object_story_spec: {
          page_id: '***************', // Default page ID
          link_data: {
            message: creative.message || `Check out our amazing offer!`,
            link: creative.link_url || 'https://example.com',
            name: creative.headline || campaignName,
            description: creative.description || 'Learn more about our products and services.',
            call_to_action: {
              type: creative.cta_type || 'LEARN_MORE'
            }
          }
        },
        access_token: accessToken
      };

      // Add image if provided
      if (creative.image_hash) {
        creativeData.object_story_spec.link_data.image_hash = creative.image_hash;
      }

      const creativeResponse = await rateLimitedApiCall(async () => {
        return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adcreatives`, null, {
          params: creativeData
        });
      });

      adCreativeId = creativeResponse.data.id;
      console.log('✅ Ad Creative created:', adCreativeId);
    }

    // Step 4: Create Ad
    console.log('📝 Step 4: Creating ad...');
    const adParams = {
      name: adName || `${campaignName} - Ad`,
      adset_id: adSetId,
      status: 'PAUSED',
      access_token: accessToken
    };

    if (adCreativeId) {
      adParams.creative = JSON.stringify({ creative_id: adCreativeId });
    }

    const adResponse = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/ads`, null, {
        params: adParams
      });
    });

    const adId = adResponse.data.id;
    console.log('✅ Ad created:', adId);

    // Clear relevant caches
    const campaignCacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    const adSetCacheKey = getCacheKey('adsets', { adAccountId: formattedAdAccountId });
    const adCacheKey = getCacheKey('ads', { adAccountId: formattedAdAccountId });

    cache.delete(campaignCacheKey);
    cache.delete(adSetCacheKey);
    cache.delete(adCacheKey);
    console.log('🗑️ Cleared all relevant caches');

    res.json({
      success: true,
      message: 'Complete campaign structure created successfully',
      data: {
        campaign: { id: campaignId, name: campaignName },
        adSet: { id: adSetId, name: adSetName || `${campaignName} - Ad Set` },
        adCreative: adCreativeId ? { id: adCreativeId } : null,
        ad: { id: adId, name: adName || `${campaignName} - Ad` }
      }
    });

  } catch (error) {
    console.error('❌ Complete campaign creation error:', error.response?.data || error.message);

    res.json({
      error: 'Failed to create complete campaign structure',
      details: error.response?.data || error.message,
      fallback_data: {
        campaign: { id: `demo_campaign_${Date.now()}`, name: req.body.campaignName },
        adSet: { id: `demo_adset_${Date.now()}`, name: req.body.adSetName },
        ad: { id: `demo_ad_${Date.now()}`, name: req.body.adName },
        note: 'Campaign creation failed, this is demo data'
      }
    });
  }
});

// Ad sets endpoint with caching and rate limiting
app.get('/api/v1/facebook/adsets/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching ad sets for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('adsets', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached ad sets data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, {
        params: {
          fields: 'id,name,campaign_id,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,targeting,optimization_goal,billing_event,bid_amount,bid_strategy,configured_status,effective_status,start_time,end_time,pacing_type,promoted_object,attribution_spec,conversion_specs,creative_sequence,frequency_control_specs,is_dynamic_creative,learning_stage_info,rf_prediction_id,source_adset,targeting_optimization_types',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ Facebook ad sets retrieved:', response.data.data?.length || 0, 'ad sets');

    const adSets = response.data.data || [];
    const adSetsWithBasicInsights = adSets.map(adSet => ({
      ...adSet,
      insights: {
        impressions: '0',
        clicks: '0',
        spend: '0.00',
        cpm: '0.00',
        cpc: '0.00',
        ctr: '0.00',
        reach: '0',
        frequency: '0.00',
        actions: [],
        cost_per_action_type: []
      }
    }));

    setCache(cacheKey, adSetsWithBasicInsights);
    res.json(adSetsWithBasicInsights);
  } catch (error) {
    console.error('❌ Facebook ad sets error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Ads endpoint with caching and rate limiting
app.get('/api/v1/facebook/ads/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching ads for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first
    const cacheKey = getCacheKey('ads', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached ads data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/ads`, {
        params: {
          fields: 'id,name,adset_id,campaign_id,status,created_time,updated_time,creative{id,name,title,body,image_url,video_id,thumbnail_url,call_to_action,link_url,object_story_spec,image_hash,image_crops,instagram_actor_id,link_og_id,object_id,object_story_id,object_type,object_url,platform_customizations,portrait_customizations,product_set_id,template_url,url_tags,use_page_actor_override},configured_status,effective_status,bid_amount,conversion_specs,tracking_specs,issues_info,last_updated_by_app_id,preview_shareable_link,recommendations,source_ad',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ Facebook ads retrieved:', response.data.data?.length || 0, 'ads');

    const ads = response.data.data || [];
    const adsWithBasicInsights = ads.map(ad => ({
      ...ad,
      insights: {
        impressions: '0',
        clicks: '0',
        spend: '0.00',
        cpm: '0.00',
        cpc: '0.00',
        ctr: '0.00',
        reach: '0',
        frequency: '0.00',
        actions: [],
        cost_per_action_type: []
      }
    }));

    setCache(cacheKey, adsWithBasicInsights);
    res.json(adsWithBasicInsights);
  } catch (error) {
    console.error('❌ Facebook ads error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Simplified campaign creation endpoint - creates campaign + ad set only
app.post('/api/v1/facebook/campaigns/simple', async (req, res) => {
  try {
    const {
      adAccountId,
      campaignName,
      objective = 'OUTCOME_TRAFFIC',
      adSetName,
      dailyBudget = 1000, // $10.00 in cents
      targeting = {}
    } = req.body;

    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🚀 Creating simple Facebook campaign structure:', {
      campaignName, objective, adSetName, dailyBudget
    });

    const formattedAdAccountId = ensureActPrefix(adAccountId);
    const axios = require('axios');

    // Step 1: Create Campaign
    console.log('📝 Step 1: Creating campaign...');
    const campaignResponse = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
        params: {
          name: campaignName,
          objective: objective,
          status: 'PAUSED',
          special_ad_categories: '[]',
          access_token: accessToken
        }
      });
    });

    const campaignId = campaignResponse.data.id;
    console.log('✅ Campaign created:', campaignId);

    // Step 2: Create Ad Set
    console.log('📝 Step 2: Creating ad set...');
    const defaultTargeting = {
      geo_locations: { countries: ['US'] },
      age_min: 18,
      age_max: 65,
      targeting_automation: {
        advantage_audience: 0 // Disable Advantage audience for manual targeting
      }
    };

    // Merge targeting carefully to avoid conflicts
    const finalTargeting = { ...defaultTargeting };
    if (targeting.geo_locations) {
      finalTargeting.geo_locations = targeting.geo_locations;
    }
    if (targeting.age_min) finalTargeting.age_min = targeting.age_min;
    if (targeting.age_max) finalTargeting.age_max = targeting.age_max;
    if (targeting.interests) finalTargeting.interests = targeting.interests;

    const adSetResponse = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, null, {
        params: {
          name: adSetName || `${campaignName} - Ad Set`,
          campaign_id: campaignId,
          daily_budget: dailyBudget,
          billing_event: 'IMPRESSIONS',
          optimization_goal: objective === 'OUTCOME_LEADS' ? 'LEAD_GENERATION' : 'LINK_CLICKS',
          bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
          targeting: JSON.stringify(finalTargeting),
          status: 'PAUSED',
          access_token: accessToken
        }
      });
    });

    const adSetId = adSetResponse.data.id;
    console.log('✅ Ad Set created:', adSetId);

    // Clear relevant caches
    const campaignCacheKey = getCacheKey('campaigns', { adAccountId: formattedAdAccountId });
    const adSetCacheKey = getCacheKey('adsets', { adAccountId: formattedAdAccountId });

    cache.delete(campaignCacheKey);
    cache.delete(adSetCacheKey);
    console.log('🗑️ Cleared relevant caches');

    res.json({
      success: true,
      message: 'Campaign and Ad Set created successfully',
      data: {
        campaign: { id: campaignId, name: campaignName },
        adSet: { id: adSetId, name: adSetName || `${campaignName} - Ad Set` },
        note: 'Ready for ad creation in Facebook Ads Manager'
      }
    });

  } catch (error) {
    console.error('❌ Simple campaign creation error:', error.response?.data || error.message);

    res.json({
      error: 'Failed to create campaign structure',
      details: error.response?.data || error.message,
      fallback_data: {
        campaign: { id: `demo_campaign_${Date.now()}`, name: req.body.campaignName },
        adSet: { id: `demo_adset_${Date.now()}`, name: req.body.adSetName },
        note: 'Campaign creation failed, this is demo data'
      }
    });
  }
});

// Lead forms endpoint
app.get('/api/v1/facebook/leadforms/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching lead forms for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);

    // Check cache first
    const cacheKey = getCacheKey('leadforms', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached lead forms data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/leadgen_forms`, {
        params: {
          fields: 'id,name,status,created_time,expired_leads_count,leads_count,locale,page,page_id,privacy_policy_url,questions,thank_you_page,context_card,follow_up_action_url,is_continued_flow,leadgen_export_csv_url,legal_content,organic_lead_retrieval_authorized,qualifiers,question_page_custom_headline,tracking_parameters',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ Facebook lead forms retrieved:', response.data.data?.length || 0, 'forms');

    const leadForms = response.data.data || [];
    setCache(cacheKey, leadForms);
    res.json(leadForms);
  } catch (error) {
    console.error('❌ Facebook lead forms error:', error.response?.data || error.message);
    res.json([]);
  }
});

// Custom audiences endpoint
app.get('/api/v1/facebook/audiences/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching custom audiences for ad account:', adAccountId);

    const formattedAdAccountId = ensureActPrefix(adAccountId);

    // Check cache first
    const cacheKey = getCacheKey('audiences', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached audiences data');
      return res.json(cachedData);
    }

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/customaudiences`, {
        params: {
          fields: 'id,name,description,approximate_count,customer_file_source,data_source,delivery_status,external_event_source,is_value_based,lookalike_audience_ids,lookalike_spec,operation_status,opt_out_link,permission_for_actions,pixel_id,retention_days,rule,rule_aggregation,rule_v2,seed_audience,sharing_status,subtype,time_content_updated,time_created,time_updated',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ Facebook custom audiences retrieved:', response.data.data?.length || 0, 'audiences');

    const audiences = response.data.data || [];
    setCache(cacheKey, audiences);
    res.json(audiences);
  } catch (error) {
    console.error('❌ Facebook custom audiences error:', error.response?.data || error.message);
    res.json([]);
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Rate-Limited Pressure Max API server running on port ${PORT}`);
  console.log(`📚 Health check available at http://localhost:${PORT}/health`);
  console.log(`🔗 CORS enabled for http://localhost:3001`);
  console.log(`⏱️ API rate limiting: ${API_DELAY}ms delay between calls`);
  console.log(`💾 Cache duration: ${CACHE_DURATION / 1000}s`);
});
