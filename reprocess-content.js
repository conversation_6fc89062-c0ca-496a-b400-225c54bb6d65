#!/usr/bin/env node

require('dotenv').config();

const fs = require('fs-extra');
const path = require('path');
const cliProgress = require('cli-progress');
const ContentQualityAnalyzer = require('./content-quality-analyzer');
const ImprovedContentExtractor = require('./improved-content-extractor');
const OpenRouterProcessor = require('./openrouter');
const ScraperUtils = require('./utils');
const config = require('./config');

class ContentReprocessor {
  constructor() {
    this.outputDir = config.settings.outputDir;
    this.analyzer = new ContentQualityAnalyzer(this.outputDir);
    this.extractor = new ImprovedContentExtractor();
    this.aiProcessor = new OpenRouterProcessor(config.openrouter.apiKey, config.openrouter);
    this.utils = new ScraperUtils(config);
    this.progressBar = null;
  }

  /**
   * Main reprocessing workflow
   */
  async run(options = {}) {
    try {
      console.log('🔄 Starting Facebook Marketing API Content Reprocessing...');
      console.log('🎯 Goal: Improve content quality with better extraction and AI processing\n');

      // Step 1: Analyze existing content quality
      console.log('📊 Step 1: Analyzing existing content quality...');
      const analysisResults = await this.analyzer.analyzeAllContent();
      
      // Generate quality report
      await this.analyzer.generateReport(analysisResults, path.join(this.outputDir, 'quality-analysis.json'));
      
      if (analysisResults.needsReprocessing.length === 0) {
        console.log('✅ All content is already high quality! No reprocessing needed.');
        return;
      }

      console.log(`\n🔧 Found ${analysisResults.needsReprocessing.length} pages needing reprocessing:`);
      analysisResults.needsReprocessing.slice(0, 5).forEach(page => {
        console.log(`   - ${page.filename} (score: ${page.score})`);
      });
      if (analysisResults.needsReprocessing.length > 5) {
        console.log(`   ... and ${analysisResults.needsReprocessing.length - 5} more`);
      }

      // Step 2: Initialize extraction tools
      console.log('\n🛠️ Step 2: Initializing improved content extraction...');
      await this.extractor.initialize();
      
      // Test AI connection
      console.log('🤖 Testing OpenRouter AI connection...');
      const aiConnected = await this.aiProcessor.testConnection();
      if (!aiConnected) {
        throw new Error('Failed to connect to OpenRouter API');
      }
      console.log('✅ AI processing ready with Claude Sonnet 4');

      // Step 3: Reprocess pages
      console.log('\n🔄 Step 3: Reprocessing pages with improved extraction...');
      await this.reprocessPages(analysisResults.needsReprocessing, options);

      // Step 4: Update manifest
      console.log('\n📋 Step 4: Updating manifest...');
      await this.utils.createManifest(this.outputDir);

      console.log('\n🎉 Reprocessing completed successfully!');
      console.log(`✅ Improved ${analysisResults.needsReprocessing.length} pages`);
      console.log(`📁 Updated files in: ${this.outputDir}`);

    } catch (error) {
      console.error('\n💥 Reprocessing failed:', error.message);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Reprocess a list of pages
   */
  async reprocessPages(pagesToReprocess, options = {}) {
    const baseUrl = config.baseUrl;
    
    // Initialize progress bar
    this.progressBar = new cliProgress.SingleBar({
      format: 'Reprocessing |{bar}| {percentage}% | {value}/{total} | Current: {current_page}',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    
    this.progressBar.start(pagesToReprocess.length, 0, { current_page: 'Starting...' });
    
    let processed = 0;
    let improved = 0;
    let failed = 0;

    for (const pageInfo of pagesToReprocess) {
      this.progressBar.update(processed, { current_page: pageInfo.filename });
      
      try {
        // Get original URL from JSON file
        const jsonPath = path.join(this.outputDir, 'json', `${pageInfo.filename}.json`);
        let url;

        try {
          const originalData = await fs.readJson(jsonPath);
          url = originalData.url;
        } catch (error) {
          // Fallback to reconstructing URL
          url = this.reconstructUrlFromFilename(pageInfo.filename, baseUrl);
        }

        console.log(`\n🔄 Reprocessing: ${pageInfo.filename}`);
        console.log(`   URL: ${url}`);
        console.log(`   Current score: ${pageInfo.score}`);
        
        // Extract improved content
        const newContent = await this.extractor.extractCleanContent(url);
        
        // Load old content for comparison
        const oldHtmlPath = path.join(this.outputDir, 'html', `${pageInfo.filename}.html`);
        const oldContent = await fs.readFile(oldHtmlPath, 'utf8');
        
        // Compare quality
        const comparison = this.extractor.compareContentQuality(oldContent, newContent.content);
        
        if (comparison.qualityImproved) {
          console.log(`   ✅ Content quality improved:`);
          console.log(`      - Text length: +${comparison.textLengthImprovement} chars`);
          console.log(`      - Script tags removed: ${comparison.scriptTagsRemoved}`);
          console.log(`      - Nav elements removed: ${comparison.navigationElementsRemoved}`);
          
          // Process with AI
          console.log(`   🤖 Processing with Claude Sonnet 4...`);
          const aiResult = await this.aiProcessor.processContent(newContent.content, url, newContent.title);
          
          if (aiResult.success) {
            // Save improved content
            await this.saveImprovedContent(pageInfo.filename, newContent, aiResult.processed);
            improved++;
            console.log(`   ✅ Successfully reprocessed and saved`);
          } else {
            console.log(`   ⚠️ AI processing failed, but content extraction improved`);
            await this.saveImprovedContent(pageInfo.filename, newContent, null);
          }
        } else {
          console.log(`   ℹ️ No significant improvement detected, skipping`);
        }
        
        processed++;
        
        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, config.openrouter.requestDelay));
        
      } catch (error) {
        console.error(`   ❌ Failed to reprocess ${pageInfo.filename}:`, error.message);
        failed++;
        processed++;
      }
    }
    
    this.progressBar.stop();
    
    console.log(`\n📊 Reprocessing Summary:`);
    console.log(`   ✅ Successfully improved: ${improved} pages`);
    console.log(`   ❌ Failed: ${failed} pages`);
    console.log(`   📄 Total processed: ${processed} pages`);
  }

  /**
   * Save improved content in all formats
   */
  async saveImprovedContent(filename, extractedContent, aiProcessedContent) {
    // Save improved HTML
    await fs.writeFile(
      path.join(this.outputDir, 'html', `${filename}.html`),
      extractedContent.content,
      'utf8'
    );

    // Save improved JSON
    await fs.writeFile(
      path.join(this.outputDir, 'json', `${filename}.json`),
      JSON.stringify(extractedContent, null, 2),
      'utf8'
    );

    // Save AI-processed content if available
    if (aiProcessedContent) {
      await fs.writeFile(
        path.join(this.outputDir, 'ai-processed', `${filename}.json`),
        JSON.stringify(aiProcessedContent, null, 2),
        'utf8'
      );

      // Save enhanced markdown
      const enhancedMarkdown = this.generateEnhancedMarkdown(aiProcessedContent);
      await fs.writeFile(
        path.join(this.outputDir, 'markdown', `${filename}_enhanced.md`),
        enhancedMarkdown,
        'utf8'
      );
    }

    // Save basic markdown
    const basicMarkdown = this.utils.htmlToMarkdown(extractedContent.content);
    const markdownContent = `# ${extractedContent.title}\n\n${basicMarkdown}`;
    await fs.writeFile(
      path.join(this.outputDir, 'markdown', `${filename}.md`),
      markdownContent,
      'utf8'
    );
  }

  /**
   * Generate enhanced markdown from AI-processed content
   */
  generateEnhancedMarkdown(aiContent) {
    return `# ${aiContent.title}

## Summary
${aiContent.summary}

${aiContent.keyPoints && aiContent.keyPoints.length > 0 ? `## Key Points
${aiContent.keyPoints.map(point => `- ${point}`).join('\n')}

` : ''}${aiContent.apiEndpoints && aiContent.apiEndpoints.length > 0 ? `## API Endpoints
${aiContent.apiEndpoints.map(endpoint => `- \`${endpoint}\``).join('\n')}

` : ''}${aiContent.parameters && aiContent.parameters.length > 0 ? `## Parameters
${aiContent.parameters.map(param => `- ${param}`).join('\n')}

` : ''}## Content
${aiContent.content}

${aiContent.examples && aiContent.examples.length > 0 ? `## Examples
${aiContent.examples.join('\n\n')}

` : ''}---
**Tags:** ${aiContent.tags ? aiContent.tags.join(', ') : 'N/A'}  
**Difficulty:** ${aiContent.difficulty || 'N/A'}  
**Content Type:** ${aiContent.contentType || 'N/A'}  
**Source:** ${aiContent.originalUrl}  
**Processed:** ${aiContent.processedAt}`;
  }

  /**
   * Reconstruct URL from filename
   */
  reconstructUrlFromFilename(filename, baseUrl) {
    // Convert filename back to URL path
    let urlPath = filename.replace(/_/g, '/');
    
    // Handle special cases
    if (!urlPath.startsWith('/docs/')) {
      urlPath = '/docs/' + urlPath;
    }
    
    return baseUrl + urlPath;
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.extractor) {
      await this.extractor.cleanup();
    }
  }
}

// Main execution
async function main() {
  const reprocessor = new ContentReprocessor();
  
  // Check for options
  const args = process.argv.slice(2);
  const options = {
    forceAll: args.includes('--force-all'),
    dryRun: args.includes('--dry-run')
  };
  
  if (options.dryRun) {
    console.log('🧪 Running in dry-run mode (analysis only)...');
  }
  
  await reprocessor.run(options);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received interrupt signal. Cleaning up...');
  process.exit(0);
});

// Run the reprocessor
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ContentReprocessor;
