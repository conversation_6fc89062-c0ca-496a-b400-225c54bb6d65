# Facebook Marketing API Ad Optimization Basics

## Summary
This guide introduces the fundamental endpoints of Facebook's Marketing API for ad optimization, focusing on audience management and campaign analytics. It covers the customaudiences endpoint for creating targeted user segments and the insights endpoint for tracking campaign performance metrics.

## Key Points
- Marketing API provides endpoints for audience management and campaign analytics
- customaudiences endpoint enables creation of targeted user segments based on demographics, interests, and behaviors
- insights endpoint offers performance analytics for campaigns, ad sets, and ads
- Key metrics available include impressions, clicks, spend, and conversions
- Both endpoints require proper authentication with access tokens

## API Endpoints
- `/act_<AD_ACCOUNT_ID>/customaudiences`
- `/act_<AD_ACCOUNT_ID>/insights`

## Parameters
- name
- subtype
- access_token
- fields
- time_range
- impressions
- clicks
- spend

## Content
# Ad Optimization Basics

The Marketing API offers endpoints to manage audiences and analyze advertising campaign insights. Understanding these endpoints and their functionalities is important for both new and experienced developers looking to optimize their advertising strategies.

## Ad Optimization Endpoints

### The `customaudiences` endpoint

The [`customaudiences` endpoint](/docs/marketing-api/reference/ad-account/customaudiences) allows you to create and manage custom and lookalike audiences, tailoring ads to specific user segments based on demographics, interests, and behaviors.

**Example API Request**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences \
  -F 'name=My Custom Audience' \
  -F 'subtype=CUSTOM' \
  -F 'access_token=<ACCESS_TOKEN>'
```

### The `insights` endpoint

The [`insights` endpoint](/docs/marketing-api/reference/ad-account/insights) provides valuable analytics about the performance of campaigns, ad sets, and ads, allowing you to track key metrics such as impressions, clicks, and conversions.

**Example API Request**

```bash
curl -X GET \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/insights \
  -F 'fields=impressions,clicks,spend' \
  -F 'time_range={"since":"2023-01-01","until":"2023-12-31"}' \
  -F 'access_token=<ACCESS_TOKEN>'
```

## Examples
POST request to create custom audience with name and subtype parameters

GET request to retrieve insights with specific fields and time range filters

---
**Tags:** facebook-marketing-api, ad-optimization, custom-audiences, campaign-insights, analytics, audience-targeting
**Difficulty:** beginner
**Content Type:** guide
**Source:** https://developers.facebook.com/docs/marketing-api/get-started/ad-optimization-basics
**Processed:** 2025-06-25T15:49:43.687Z