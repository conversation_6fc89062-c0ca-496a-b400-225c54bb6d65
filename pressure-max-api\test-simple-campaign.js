const axios = require('axios');

async function testSimpleCampaignCreation() {
  try {
    console.log('🚀 Testing simple campaign creation (Campaign + Ad Set)...');
    
    const response = await axios.post('http://localhost:3000/api/v1/facebook/campaigns/simple', {
      adAccountId: 'act_263173616383414',
      campaignName: 'Pressure Max - Lead Generation Campaign',
      objective: 'OUTCOME_LEADS',
      adSetName: 'Pressure Washing Services - Homeowners',
      dailyBudget: 3000, // $30.00 per day
      targeting: {
        geo_locations: { 
          countries: ['US']
        },
        age_min: 30,
        age_max: 60
      }
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Simple campaign creation response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('❌ Simple campaign creation error:', error.response?.data || error.message);
  }
}

async function testLeadFormsRetrieval() {
  try {
    console.log('\n🔍 Testing lead forms retrieval...');
    
    const response = await axios.get('http://localhost:3000/api/v1/facebook/leadforms/act_1313976499465490');
    console.log(`✅ Retrieved ${response.data.length} lead forms`);
    
    if (response.data.length > 0) {
      const firstForm = response.data[0];
      console.log('📝 Sample lead form:', {
        id: firstForm.id,
        name: firstForm.name,
        status: firstForm.status,
        leads_count: firstForm.leads_count,
        page_id: firstForm.page_id
      });
    }
  } catch (error) {
    console.error('❌ Lead forms retrieval error:', error.response?.data || error.message);
  }
}

async function testCustomAudiencesRetrieval() {
  try {
    console.log('\n🔍 Testing custom audiences retrieval...');
    
    const response = await axios.get('http://localhost:3000/api/v1/facebook/audiences/act_1313976499465490');
    console.log(`✅ Retrieved ${response.data.length} custom audiences`);
    
    if (response.data.length > 0) {
      const firstAudience = response.data[0];
      console.log('📝 Sample custom audience:', {
        id: firstAudience.id,
        name: firstAudience.name,
        approximate_count: firstAudience.approximate_count,
        data_source: firstAudience.data_source,
        subtype: firstAudience.subtype
      });
    }
  } catch (error) {
    console.error('❌ Custom audiences retrieval error:', error.response?.data || error.message);
  }
}

async function testEnhancedCampaignData() {
  try {
    console.log('\n📊 Testing enhanced campaign data retrieval...');
    
    const response = await axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_1313976499465490');
    console.log(`✅ Retrieved ${response.data.length} campaigns with enhanced fields`);
    
    if (response.data.length > 0) {
      const campaign = response.data[0];
      console.log('📝 Enhanced campaign fields available:', {
        basic: ['id', 'name', 'objective', 'status'].every(field => campaign[field] !== undefined),
        budget: ['daily_budget', 'lifetime_budget', 'budget_remaining'].some(field => campaign[field] !== undefined),
        advanced: ['bid_strategy', 'buying_type', 'special_ad_categories'].some(field => campaign[field] !== undefined),
        timing: ['start_time', 'stop_time', 'created_time'].some(field => campaign[field] !== undefined)
      });
    }
  } catch (error) {
    console.error('❌ Enhanced campaign data error:', error.response?.data || error.message);
  }
}

async function runAllTests() {
  await testSimpleCampaignCreation();
  await testLeadFormsRetrieval();
  await testCustomAudiencesRetrieval();
  await testEnhancedCampaignData();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📋 Summary:');
  console.log('✅ Simple Campaign Creation - Creates Campaign + Ad Set');
  console.log('✅ Enhanced Data Retrieval - More detailed fields for campaigns, ad sets, ads');
  console.log('✅ Lead Forms - Retrieve lead generation forms');
  console.log('✅ Custom Audiences - Retrieve targeting audiences');
  console.log('✅ Rate Limiting - 2-second delays prevent API limits');
  console.log('✅ Caching - 5-minute cache reduces redundant calls');
}

runAllTests();
