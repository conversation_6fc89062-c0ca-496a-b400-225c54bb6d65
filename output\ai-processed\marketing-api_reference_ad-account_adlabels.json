{"title": "Facebook Marketing API - Ad Account Ad Labels Reference", "summary": "Complete reference documentation for the Facebook Marketing API's Ad Account Ad Labels endpoint, covering how to read, create, and manage ad labels within an ad account. This endpoint allows developers to organize and categorize ads using custom labels for better campaign management.", "content": "# Ad Account Ad Labels\n\nThe Ad Account Ad Labels endpoint allows you to manage labels for organizing ads within a Facebook ad account. This endpoint supports reading existing labels and creating new ones.\n\n## Reading Ad Labels\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/adlabels\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Response Format\nReturns a JSON formatted result with the following structure:\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Response Fields\n- **data**: A list of AdLabel nodes\n- **paging**: Pagination information for navigating through results\n- **summary**: Aggregated information about the edge, including:\n  - `insights`: Analytics summary for all objects\n  - `total_count`: Total number of objects\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/adlabels HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/adlabels',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/adlabels\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n## Creating Ad Labels\n\n### Endpoint\n```\nPOST /v23.0/act_{ad_account_id}/adlabels\n```\n\n### Required Parameters\n- **name** (string): The name for the new ad label\n\n### Return Type\nReturns a struct with the created label's ID:\n```json\n{\n  \"id\": \"numeric_string\"\n}\n```\n\n### Code Examples\n\n#### HTTP Request\n```http\nPOST /v23.0/act_<AD_ACCOUNT_ID>/adlabels HTTP/1.1\nHost: graph.facebook.com\n\nname=My+Label+1\n```\n\n#### cURL\n```bash\ncurl -X POST \\\n  -F 'name=\"My Label 1\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adlabels\n```\n\n## Error Codes\n\n### Common Errors\n- **200**: Permissions error\n- **190**: Invalid OAuth 2.0 Access Token\n- **100**: Invalid parameter\n- **80004**: Too many calls to this ad-account (rate limiting)\n\n## Limitations\n- **Updating**: Not supported on this endpoint\n- **Deleting**: Not supported on this endpoint\n- This endpoint supports read-after-write functionality\n\n## API Version\nCurrent version: v23.0", "keyPoints": ["Ad Labels help organize and categorize ads within Facebook ad accounts", "The endpoint supports reading existing labels and creating new ones", "Creating labels requires only a name parameter and returns the new label's ID", "Updating and deleting operations are not supported on this endpoint", "The endpoint supports read-after-write functionality for immediate access to created labels"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/adlabels", "POST /v23.0/act_{ad_account_id}/adlabels"], "parameters": ["name (string, required for POST) - AdLabel name", "summary (optional for GET) - Specify fields to fetch in summary", "access_token (required) - OAuth 2.0 Access Token"], "examples": ["GET request to retrieve all ad labels for an account", "POST request to create a new ad label named 'My Label 1'", "PHP SDK implementation for reading ad labels", "JavaScript SDK implementation for creating ad labels", "cURL command for creating ad labels"], "tags": ["Facebook Marketing API", "Ad Labels", "Ad Account", "Campaign Management", "Graph API", "REST API"], "relatedTopics": ["AdLabel object reference", "Graph API pagination", "OAuth 2.0 authentication", "Rate limiting in Ads Management API", "Read-after-write functionality"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adlabels/", "processedAt": "2025-06-25T15:15:08.361Z", "processor": "openrouter-claude-sonnet-4"}