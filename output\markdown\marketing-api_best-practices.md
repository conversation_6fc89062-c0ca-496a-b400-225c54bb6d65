# Best Practices

On This Page

[Best Practices](#best-practices)

[Ad Changes Triggering Ad Reviews](#ad-changes-triggering-ad-reviews)

[Pagination](#paging)

[User Information](#user-information)

[Suggested Bids](#suggested-bids)

[Batch Requests](#batch-requests)

[Check Data Changes using ETags](#check-data-changes-using-etags)

[Object Archive and Delete Status](#object-archive-and-delete-status)

[Viewing Errors](#viewing-errors)

[Facebook Marketing Developer Community Group](#facebook-marketing-developer-community-group)

[Testing](#testing)

[Basic Criteria](#criteria)

[Policies](#policies)

# Best Practices

## Ad Changes Triggering Ad Reviews

If you make any changes to the following scenarios, your ad will be triggered for review:

*   Any changes to your creative (image, text, link, video, and so on)
*   Any changes to targeting
*   Any changes of optimization goals and billing events may also trigger review

**Note**: Changes to bid amount, budget, and ad set schedule will not have any effect on the review status.

Additionally, if an ad enters Ad Review with the run status of "Paused", then it will remain Paused upon exiting Ad Review. Otherwise, the ad will be considered Active and ready to deliver.

[](#)

## Pagination

For paging response data, see the [Graph API Pagination](/docs/graph-api/results).

[](#)

## User Information

You should store user IDs, session keys, and the ads account ID so it is easy to programmatically access them and keep them together. This is important because any calls made with an account ID belonging to one user and the session key for another user will fail with a permissions error. Any storages of user data must be done in compliance with [Facebook Platform Terms](/terms) and [Developer Policies](/devpolicy).

[](#)

## Suggested Bids

Run frequent reports on your campaigns, as suggested bids change dynamically in response to bidding by competitors using similar targeting. Bid suggestions get updated within a few hours, depending upon the bidding of competitors.

[](#)

## Batch Requests

Make multiple requests to the API with a single call, see:

*   [Multiple Requests](/docs/graph-api/making-multiple-requests)
*   [Batch Requests](/docs/reference/ads-api/batch-requests)

You can also query for multiple objects by ID as follows:

```
https://graph.facebook.com/<API\_VERSION>?ids=\[id1,id2\]
```

To query for a specific field:

```
https://graph.facebook.com/<API\_VERSION>?ids=\[id1,id2\]&amp;fields=field1,field2
```

[](#)

## Check Data Changes using ETags

Quickly check if the response to a request has changed since you last made it, see:

*   [ETags blog](/blog/post/627/)
*   [ETags Reference](/docs/reference/ads-api/etags-reference/)

[](#)

## Object Archive and Delete Status

Ad objects have two types of delete states: archived and deleted. You can query both archived and deleted objects with the object id. However, we do not return deleted objects if you request it from another object's edge.

You can have up to 5000 archived objects at any time. You should move ad objects from archived states to deleted states if you no longer need to retrieve them via edges. To learn how states work and for sample calls see [Storing Ad Objects](/docs/ads-api/best-practices/storing_adobjects).

[](#)

## Viewing Errors

People make mistakes and try to create ads that are not accepted, [Error Codes](/docs/reference/ads-api/error-reference) provide reasons an API call failed. You should share some form of the error to users so they can fix their ads.

[](#)

## Facebook Marketing Developer Community Group

Join [Facebook Marketing Developer Community](https://www.facebook.com/groups/pmdcommunity/) group on Facebook for news and update on for Marketing API. We post items from the [Marketing API blog](/ads/blog/) to the group.

[](#)

## Testing

Sandbox mode is a testing environment to read and write Marketing API calls without delivering actual ads. See [Sandbox Mode for Developers](/ads/blog/post/2016/10/19/sandbox-ad-accounts/)

Try API calls with [Graph API Explorer](/tools/explorer). You can try any API call you would like to make to the Marketing API, see [blog post](/blog/post/517/). Select your app in `App`, and grant your app `ads_management` or `ads_read` permission in `extended permissions` when you create an access token. Use `ads_read` if you only need Ads Insights API access for reporting. Use `ads_management` to read and update ads in an account.

For [development and basic access](/docs/reference/ads-api/access), configure a list of ad accounts your app is able to make API calls for, see [account list](/docs/reference/ads-api/access#standard_accounts).

You can use sandbox mode to demonstrate your app for app review. However in sandbox mode you cannot create ads or ad creative. Therefore you should use hard coded ad IDs and ad creative IDs to demonstrate your use of our API for app review.

### Basic Criteria

*   Demonstrate value beyond Facebook's core solutions, such as [Facebook Ads Manager](https://www.facebook.com/ads/manager/).
    
*   Focus on business objectives, such as increase in sales. Facebook business objectives can be found [here](/docs/reference/ads-api/guides/chapter-2-objective-connections).
    

[](#)

## Policies

Understand the API policies; Facebook has the right to audit your activity anytime:

*   **[Platform Terms](https://developers.facebook.com/terms)**
*   **[Developer Policies](https://developers.facebook.com/devpolicy)**
*   **[Promotion Policies](https://www.facebook.com/page_guidelines.php#promotionsguidelines)**
*   **[Data Use Policy](https://www.facebook.com/full_data_use_policy)**
*   **[Statement of Rights and Responsibilities](https://www.facebook.com/legal/terms)**
*   **[Advertising Guidelines](https://www.facebook.com/ad_guidelines.php)**

Be ready to adapt quickly to changes. Most changes are [versioned](/docs/reference/ads-api/versions) and change windows are 90 days, ongoing.

In [Statement of Rights and Responsibilities](https://www.facebook.com/legal/terms), you are financially and operationally responsible for your application, its contents, and your use of the Meta Platform and the Ads API. You should manage your app's stability and potential bugs.

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)