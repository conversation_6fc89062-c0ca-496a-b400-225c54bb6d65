<h1 id="image-crops">Image Crops</h1>

<p>Provide aspect ratios for images in different ad placements. Facebook crops your image according to your specifications given or if you provide no cropping we display it using defaults. See <a href="/docs/reference/ads-api/adimage/">Ad Image</a>. For example upload an image to use in ad creative:</p>
<pre class="_5s-8 prettyprint lang-curl prettyprinted" style=""><span class="pln">curl \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'filename=@&lt;IMAGE_PATH&gt;'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
  https</span><span class="pun">:</span><span class="com">//graph.facebook.com/</span><code><span class="com">v23.0</span></code><span class="com">/act_&lt;AD_ACCOUNT_ID&gt;/adimages</span><span class="pln">
    </span></pre><p>Then, provide ad creative by referencing the image hash returned in the previous call along with cropping.</p>
<p>Crops contains key-value pairs, where the key is a <code>crop key</code> and value is the pixel dimensions of the crop. For all supported keys, see <a href="/docs/marketing-api/reference/ads-image-crops">Ads Image Crops Reference</a>.</p>

<p>Provide value as <code>(x, y)</code> coordinates for the upper-left and bottom-right corners of the cropping rectangle. <code>crop key</code> describes an aspect ratio. The aspect ratio of the box specified by width and height must be as close as possible to the aspect ratio in <code>crop key</code>.</p>

<p>An image's origin <code>(0, 0)</code> is at the upper-left corner. The point, <code>(width - 1, height - 1)</code> is at the bottom-right corner.</p>

<h2 id="requirements">Specification</h2>

<p>When you use this feature, <strong>you should use it for all placements where an ad may appear</strong>. For example, if you provide it for the Right Hand Column, and you also want to use the ad in Newsfeed, you'll need to provide cropping for the Newsfeed placement.</p>

<h2 id="limits">Limitations</h2>

<p>Image crops are only supported for ad creatives with <code>image_file</code> or <code>image_hash</code>. <code>Page posts</code> are not supported. Values must adhere to these constraints:</p>

<ul>
<li>Points specified by <code>(x, y)</code> must lie within the image. A rectangle that extends beyond the bounds of the image is invalid.</li>
<li>The rectangle must be the same aspect ratio as specified by the crop key.</li>
<li>Coordinates cannot contain negative values.</li>
<li>Facebook Stories do not support image crops. </li>
</ul>

<p>For example:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="typ">Example</span><span class="pun">:{</span><span class="str">"100x100"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln"> </span><span class="pun">[</span><span class="lit">330</span><span class="pun">,</span><span class="pln"> </span><span class="lit">67</span><span class="pun">],</span><span class="pln"> </span><span class="pun">[</span><span class="lit">1080</span><span class="pun">,</span><span class="pln"> </span><span class="lit">817</span><span class="pun">]</span><span class="pln"> </span><span class="pun">]}</span></pre><div style="text-align:center;"></div>

