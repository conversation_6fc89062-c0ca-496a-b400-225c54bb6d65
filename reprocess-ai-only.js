#!/usr/bin/env node

require('dotenv').config();

const fs = require('fs-extra');
const path = require('path');
const cliProgress = require('cli-progress');
const ContentQualityAnalyzer = require('./content-quality-analyzer');
const OpenRouterProcessor = require('./openrouter');
const config = require('./config');

class AIReprocessor {
  constructor() {
    this.outputDir = config.settings.outputDir;
    this.analyzer = new ContentQualityAnalyzer(this.outputDir);
    this.aiProcessor = new OpenRouterProcessor(config.openrouter.apiKey, config.openrouter);
    this.progressBar = null;
  }

  /**
   * Main AI reprocessing workflow
   */
  async run(options = {}) {
    try {
      console.log('🤖 Starting AI Reprocessing of Facebook Marketing API Documentation...');
      console.log('🎯 Goal: Improve AI-processed content quality with Claude Sonnet 4\n');

      // Step 1: Analyze existing content quality
      console.log('📊 Step 1: Analyzing existing content quality...');
      const analysisResults = await this.analyzer.analyzeAllContent();
      
      if (analysisResults.needsReprocessing.length === 0) {
        console.log('✅ All content is already high quality! No reprocessing needed.');
        return;
      }

      console.log(`\n🔧 Found ${analysisResults.needsReprocessing.length} pages needing AI reprocessing:`);
      analysisResults.needsReprocessing.slice(0, 5).forEach(page => {
        console.log(`   - ${page.filename} (score: ${page.score})`);
      });
      if (analysisResults.needsReprocessing.length > 5) {
        console.log(`   ... and ${analysisResults.needsReprocessing.length - 5} more`);
      }

      // Step 2: Test AI connection
      console.log('\n🤖 Step 2: Testing OpenRouter AI connection...');
      const aiConnected = await this.aiProcessor.testConnection();
      if (!aiConnected) {
        throw new Error('Failed to connect to OpenRouter API');
      }
      console.log('✅ AI processing ready with Claude Sonnet 4');

      // Step 3: Reprocess pages with AI
      console.log('\n🔄 Step 3: Reprocessing pages with improved AI processing...');
      await this.reprocessPagesWithAI(analysisResults.needsReprocessing, options);

      // Step 4: Update manifest
      console.log('\n📋 Step 4: Updating manifest...');
      await this.updateManifest();

      console.log('\n🎉 AI Reprocessing completed successfully!');
      console.log(`✅ Improved ${analysisResults.needsReprocessing.length} pages with Claude Sonnet 4`);
      console.log(`📁 Updated files in: ${this.outputDir}`);

    } catch (error) {
      console.error('\n💥 AI Reprocessing failed:', error.message);
      throw error;
    }
  }

  /**
   * Reprocess pages with AI only (no re-scraping)
   */
  async reprocessPagesWithAI(pagesToReprocess, options = {}) {
    // Initialize progress bar
    this.progressBar = new cliProgress.SingleBar({
      format: 'AI Processing |{bar}| {percentage}% | {value}/{total} | Current: {current_page}',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    
    this.progressBar.start(pagesToReprocess.length, 0, { current_page: 'Starting...' });
    
    let processed = 0;
    let improved = 0;
    let failed = 0;

    for (const pageInfo of pagesToReprocess) {
      this.progressBar.update(processed, { current_page: pageInfo.filename });
      
      try {
        console.log(`\n🤖 AI Processing: ${pageInfo.filename}`);
        console.log(`   Current score: ${pageInfo.score}`);
        
        // Load existing content
        const jsonPath = path.join(this.outputDir, 'json', `${pageInfo.filename}.json`);
        const htmlPath = path.join(this.outputDir, 'html', `${pageInfo.filename}.html`);
        
        if (!await fs.pathExists(jsonPath)) {
          console.log(`   ❌ Original JSON file not found, skipping`);
          failed++;
          processed++;
          continue;
        }

        const originalData = await fs.readJson(jsonPath);
        
        // Use HTML content if available, otherwise use content from JSON
        let contentToProcess = originalData.content;
        if (await fs.pathExists(htmlPath)) {
          contentToProcess = await fs.readFile(htmlPath, 'utf8');
        }

        console.log(`   📄 Content length: ${contentToProcess.length} chars`);
        console.log(`   🔗 URL: ${originalData.url}`);
        
        // Process with improved AI
        console.log(`   🤖 Processing with Claude Sonnet 4...`);
        const aiResult = await this.aiProcessor.processContent(
          contentToProcess, 
          originalData.url, 
          originalData.title
        );
        
        if (aiResult.success) {
          // Save improved AI-processed content
          await this.saveImprovedAIContent(pageInfo.filename, originalData, aiResult.processed);
          improved++;
          console.log(`   ✅ Successfully reprocessed with AI`);
          console.log(`   📊 Summary length: ${aiResult.processed.summary?.length || 0} chars`);
          console.log(`   🔑 Key points: ${aiResult.processed.keyPoints?.length || 0}`);
          console.log(`   🔗 API endpoints: ${aiResult.processed.apiEndpoints?.length || 0}`);
        } else {
          console.log(`   ❌ AI processing failed`);
          failed++;
        }
        
        processed++;
        
        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, config.openrouter.requestDelay));
        
      } catch (error) {
        console.error(`   ❌ Failed to reprocess ${pageInfo.filename}:`, error.message);
        failed++;
        processed++;
      }
    }
    
    this.progressBar.stop();
    
    console.log(`\n📊 AI Reprocessing Summary:`);
    console.log(`   ✅ Successfully improved: ${improved} pages`);
    console.log(`   ❌ Failed: ${failed} pages`);
    console.log(`   📄 Total processed: ${processed} pages`);
  }

  /**
   * Save improved AI-processed content
   */
  async saveImprovedAIContent(filename, originalData, aiProcessedContent) {
    // Save improved AI-processed JSON
    await fs.writeFile(
      path.join(this.outputDir, 'ai-processed', `${filename}.json`),
      JSON.stringify(aiProcessedContent, null, 2),
      'utf8'
    );

    // Save enhanced markdown
    const enhancedMarkdown = this.generateEnhancedMarkdown(aiProcessedContent);
    await fs.writeFile(
      path.join(this.outputDir, 'markdown', `${filename}_enhanced.md`),
      enhancedMarkdown,
      'utf8'
    );

    // Update the original JSON with a flag indicating it was reprocessed
    const updatedOriginalData = {
      ...originalData,
      reprocessedAt: new Date().toISOString(),
      reprocessedWith: 'claude-sonnet-4'
    };
    
    await fs.writeFile(
      path.join(this.outputDir, 'json', `${filename}.json`),
      JSON.stringify(updatedOriginalData, null, 2),
      'utf8'
    );
  }

  /**
   * Generate enhanced markdown from AI-processed content
   */
  generateEnhancedMarkdown(aiContent) {
    return `# ${aiContent.title}

## Summary
${aiContent.summary}

${aiContent.keyPoints && aiContent.keyPoints.length > 0 ? `## Key Points
${aiContent.keyPoints.map(point => `- ${point}`).join('\n')}

` : ''}${aiContent.apiEndpoints && aiContent.apiEndpoints.length > 0 ? `## API Endpoints
${aiContent.apiEndpoints.map(endpoint => `- \`${endpoint}\``).join('\n')}

` : ''}${aiContent.parameters && aiContent.parameters.length > 0 ? `## Parameters
${aiContent.parameters.map(param => `- ${param}`).join('\n')}

` : ''}## Content
${aiContent.content}

${aiContent.examples && aiContent.examples.length > 0 ? `## Examples
${aiContent.examples.join('\n\n')}

` : ''}---
**Tags:** ${aiContent.tags ? aiContent.tags.join(', ') : 'N/A'}  
**Difficulty:** ${aiContent.difficulty || 'N/A'}  
**Content Type:** ${aiContent.contentType || 'N/A'}  
**Source:** ${aiContent.originalUrl}  
**Processed:** ${aiContent.processedAt}`;
  }

  /**
   * Update manifest with reprocessing information
   */
  async updateManifest() {
    const manifestPath = path.join(this.outputDir, 'manifest.json');
    
    try {
      const jsonDir = path.join(this.outputDir, 'json');
      const aiProcessedDir = path.join(this.outputDir, 'ai-processed');
      
      const files = await fs.readdir(jsonDir);
      const aiFiles = await fs.pathExists(aiProcessedDir) ? await fs.readdir(aiProcessedDir) : [];
      
      const manifest = {
        totalPages: 0,
        aiProcessedPages: 0,
        reprocessedPages: 0,
        pages: [],
        aiProcessingEnabled: true,
        lastReprocessed: new Date().toISOString(),
        generatedAt: new Date().toISOString()
      };

      for (const file of files) {
        if (file.endsWith('.json') && file !== 'manifest.json') {
          try {
            const content = await fs.readJson(path.join(jsonDir, file));
            const hasAIProcessed = aiFiles.includes(file);
            const wasReprocessed = !!content.reprocessedAt;
            
            manifest.pages.push({
              filename: file,
              title: content.title,
              url: content.url,
              timestamp: content.timestamp,
              aiProcessed: hasAIProcessed,
              reprocessed: wasReprocessed,
              reprocessedAt: content.reprocessedAt
            });

            if (hasAIProcessed) {
              manifest.aiProcessedPages++;
            }
            if (wasReprocessed) {
              manifest.reprocessedPages++;
            }
          } catch (error) {
            console.error(`Error reading ${file}:`, error.message);
          }
        }
      }

      manifest.totalPages = manifest.pages.length;
      await fs.writeJson(manifestPath, manifest, { spaces: 2 });
      
      console.log(`✅ Manifest updated: ${manifest.reprocessedPages} pages marked as reprocessed`);
      
    } catch (error) {
      console.error('Error updating manifest:', error.message);
    }
  }
}

// Main execution
async function main() {
  const reprocessor = new AIReprocessor();
  
  // Check for options
  const args = process.argv.slice(2);
  const options = {
    forceAll: args.includes('--force-all')
  };
  
  if (options.forceAll) {
    console.log('🔄 Force reprocessing ALL pages...');
  }
  
  await reprocessor.run(options);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received interrupt signal. Cleaning up...');
  process.exit(0);
});

// Run the reprocessor
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = AIReprocessor;
