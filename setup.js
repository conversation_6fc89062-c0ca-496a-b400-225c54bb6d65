#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Facebook Marketing API Documentation Scraper...\n');

// Check if package.json exists
if (!fs.existsSync('package.json')) {
  console.error('❌ package.json not found. Please run this script from the project directory.');
  process.exit(1);
}

// Function to run command and return promise
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`📦 Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function setup() {
  try {
    // Step 1: Install npm dependencies
    console.log('\n📦 Installing npm dependencies...');
    await runCommand('npm', ['install']);
    console.log('✅ Dependencies installed successfully\n');
    
    // Step 2: Install Playwright browsers
    console.log('🌐 Installing Playwright browsers...');
    await runCommand('npx', ['playwright', 'install']);
    console.log('✅ Playwright browsers installed successfully\n');
    
    // Step 3: Create output directory
    console.log('📁 Creating output directory...');
    const outputDir = './output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      console.log('✅ Output directory created\n');
    } else {
      console.log('✅ Output directory already exists\n');
    }
    
    // Step 4: Test the setup
    console.log('🧪 Testing the setup...');
    console.log('Running a quick test to verify everything works...\n');
    
    await runCommand('node', ['scraper.js', '--test']);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 SETUP COMPLETE!');
    console.log('='.repeat(60));
    console.log('✅ All dependencies installed');
    console.log('✅ Playwright browsers ready');
    console.log('✅ Output directory created');
    console.log('✅ Test run successful');
    console.log('\n📋 Next steps:');
    console.log('   1. Review config.js to customize settings');
    console.log('   2. Run "npm run scrape" to start full scraping');
    console.log('   3. Check ./output/ directory for results');
    console.log('\n🚀 Happy scraping!');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure you have Node.js installed (v14 or higher)');
    console.log('   2. Check your internet connection');
    console.log('   3. Try running commands manually:');
    console.log('      - npm install');
    console.log('      - npx playwright install');
    console.log('      - node scraper.js --test');
    process.exit(1);
  }
}

// Run setup
setup();
