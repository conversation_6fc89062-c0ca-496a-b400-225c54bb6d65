{"title": "Facebook Marketing API - Ad Account Assigned Users Reference", "summary": "Complete reference for managing business and system users assigned to Facebook Ad Accounts through the Marketing API. Covers reading, updating, and deleting user assignments with task-based permissions.", "content": "# Ad Account Assigned Users\n\nManage business and system users assigned to Facebook Ad Accounts through the Marketing API.\n\n## Reading Assigned Users\n\nRetrieve business and system users assigned to an Ad Account.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/assigned_users\n```\n\n### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|---------|\n| `business` | numeric string or integer | The business associated with this Ad Account | Yes |\n\n### Response Format\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Data Fields\n\nEach AssignedUser node includes:\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `permitted_tasks` | list<string> | Tasks that are assignable on this object |\n| `tasks` | list<string> | All unpacked roles/tasks of this particular user on this object |\n\n#### Summary Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `total_count` | unsigned int32 | Total number of business and system users assigned to this Ad Account |\n\n### Code Examples\n\n#### HTTP\n```http\nGET /v23.0/{ad-account-id}/assigned_users HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/assigned_users',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/assigned_users\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n## Updating User Assignments\n\nAssign users to Ad Accounts with task-based permissions.\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/assigned_users\n```\n\n### Task-Based Permissions\n\nReplaces role-based permissions with granular task assignments:\n\n#### Ad Account Tasks\n- **ADMIN** → `['MANAGE', 'ADVERTISE', 'ANALYZE']`\n- **GENERAL_USER** → `['ADVERTISE', 'ANALYZE']` or `['ANALYZE']`\n\n#### Business Manager Tasks\n- **MANAGER** → `['MANAGE', 'CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`\n- **CONTENT_CREATOR** → `['CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`\n- **MODERATOR** → `['MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`\n- **ADVERTISER** → `['ADVERTISE', 'ANALYZE', 'DRAFT']`\n- **INSIGHTS_ANALYST** → `['ANALYZE', 'DRAFT']`\n- **CREATIVE_HUB_MOCKUPS_MANAGER** → `['DRAFT']`\n\n### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|---------|\n| `tasks` | array<enum> | AdAccount permission tasks: MANAGE, ADVERTISE, ANALYZE, DRAFT, AA_ANALYZE | No |\n| `user` | UID | Business user id or system user id | Yes |\n\n### Return Type\n```json\n{\n  \"success\": true\n}\n```\n\n## Deleting User Assignments\n\nRemove user assignments from Ad Accounts.\n\n### Endpoint\n```\nDELETE /act_{ad_account_id}/assigned_users\n```\n\n### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|---------|\n| `user` | UID | Business user id or system user id | Yes |\n\n### Return Type\n```json\n{\n  \"success\": true\n}\n```\n\n## Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | The action attempted has been deemed abusive or is otherwise disallowed |\n| 2620 | Invalid call to update account permissions |\n| 3919 | There was an unexpected technical issue. Please try again |\n| 80004 | Too many calls to this ad-account. Rate limiting applied |", "keyPoints": ["Task-based permissions replace role-based permissions for granular access control", "Reading assigned users requires business parameter and returns permitted tasks", "Users can be assigned with specific task combinations like MANAGE, ADVERTISE, ANALYZE", "API supports read-after-write for immediate verification of changes", "Rate limiting applies with error code 80004 for excessive API calls"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/assigned_users", "POST /act_{ad_account_id}/assigned_users", "DELETE /act_{ad_account_id}/assigned_users"], "parameters": ["business", "tasks", "user", "permitted_tasks", "total_count"], "examples": ["HTTP GET request for reading assigned users", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Task-based permission mapping from roles", "JSON response structure with data, paging, and summary"], "tags": ["Facebook Marketing API", "Ad Account", "User Management", "Permissions", "Business Manager", "API Reference"], "relatedTopics": ["Business Manager API", "Ad Account management", "User permissions", "Task-based access control", "Graph API pagination", "OAuth 2.0 authentication", "Rate limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/assigned_users/", "processedAt": "2025-06-25T15:21:53.686Z", "processor": "openrouter-claude-sonnet-4"}