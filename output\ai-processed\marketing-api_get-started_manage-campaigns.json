{"title": "Facebook Marketing API: Ad Campaign Management", "summary": "This guide covers essential ad campaign management operations through the Facebook Marketing API, including modifying campaign settings, pausing/resuming campaigns, archiving campaigns, and permanently deleting campaigns. It provides practical examples for each operation with proper API request formatting.", "content": "# Ad Campaign Management\n\nManaging ad campaigns through the Marketing API involves several key operations: modifying campaign settings, pausing and resuming campaigns, and deleting campaigns.\n\n## Modify an Ad Campaign\n\nTo update an existing ad campaign, you can send a `POST` request to the `/<CAMPAIGN_ID>` endpoint. You can change various settings, including the campaign's objective, budget, and targeting attributes.\n\n**Example API Request:**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \\\n  -F 'objective=CONVERSIONS' \\\n  -F 'daily_budget=2000' \\\n  -F 'status=ACTIVE' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\n## Pause an Ad Campaign\n\nTemporarily stopping a campaign can help you reassess your strategy without deleting the campaign entirely. To pause a campaign, update its status to `PAUSED`.\n\n**Example API Request:**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \\\n  -F 'status=PAUSED' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\nTo resume the campaign, you can set the status back to `ACTIVE`.\n\n## Archive an Ad Campaign\n\nIf you want to temporarily stop a campaign without deleting it, you can archive it instead. To do this, send a `POST` request to the `/<CAMPAIGN_ID>` endpoint with the status parameter set to `ARCHIVED`.\n\n**Example API Request:**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \\\n  -F 'status=ARCHIVED' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\nNote that archiving a campaign will stop it from running, but it can be easily restored by changing its status back to `ACTIVE`.\n\n## Delete an Ad Campaign\n\nWhen you need to permanently remove a campaign, send a `DELETE` request to the `/<CAMPAIGN_ID>` endpoint.\n\nBe cautious when deleting campaigns, as this action cannot be undone. Always double-check the campaign ID before deletion to avoid accidental loss of data.\n\n**Example API Request:**\n\n```bash\ncurl -X DELETE \\\n  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\n## Learn More\n\n- [Campaign Reference](/docs/marketing-api/reference/ad-campaign-group)\n- [Manage Your Ad Object's Status](/docs/marketing-apis/guides/manage-your-ad-object-status)\n- [Troubleshooting](/docs/liz-test/marketing-api/troubleshooting)", "keyPoints": ["Campaign modifications are done via POST requests to the /<CAMPAIGN_ID> endpoint", "Campaign status can be set to ACTIVE, PAUSED, or ARCHIVED for different operational states", "Deleting campaigns is permanent and cannot be undone - use DELETE method carefully", "Archived campaigns can be restored by changing status back to ACTIVE", "Multiple campaign attributes can be updated in a single API request"], "apiEndpoints": ["POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID>", "DELETE https://graph.facebook.com/v23.0/<CAMPAIGN_ID>"], "parameters": ["objective", "daily_budget", "status", "access_token", "CAMPAIGN_ID"], "examples": ["curl -X POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'objective=CONVERSIONS' -F 'daily_budget=2000' -F 'status=ACTIVE' -F 'access_token=<ACCESS_TOKEN>'", "curl -X POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'status=PAUSED' -F 'access_token=<ACCESS_TOKEN>'", "curl -X POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'status=ARCHIVED' -F 'access_token=<ACCESS_TOKEN>'", "curl -X DELETE https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'access_token=<ACCESS_TOKEN>'"], "tags": ["facebook-marketing-api", "campaign-management", "api-operations", "crud-operations", "advertising"], "relatedTopics": ["Campaign Reference", "Ad Object Status Management", "Basic Ad Creation", "API Troubleshooting"], "difficulty": "intermediate", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/get-started/manage-campaigns", "processedAt": "2025-06-25T15:49:28.830Z", "processor": "openrouter-claude-sonnet-4"}