# Get Started with the Marketing API

On This Page

[Get Started with the Marketing API](#get-started-with-the-marketing-api)

[Ad Account Requirements](#ad-account-requirements)

[Finding Your Ad Account Number](#finding-your-ad-account-number)

[Meta Developer Account](#meta-developer-account)

[Create an App](#create-an-app)

[Authorization and Authentication](#authorization-and-authentication)

[Next Steps](#next-steps)

# Get Started with the Marketing API

To effectively utilize the Marketing API, users must follow some key steps to set up their environment and gain access to the API's features. This section outlines the prerequisites necessary for getting started.

## Ad Account Requirements

To manage your ads through the Marketing API, you must have an [active ad account](https://www.facebook.com/business/help/***************). This account is crucial not only for running campaigns but also for managing billing settings and setting spending limits. An ad account allows you to track your advertising expenses, monitor performance, and optimize your campaigns effectively.

### Finding Your Ad Account Number

Locating your ad account number can be done through the [Meta Ads Manager](https://adsmanager.facebook.com/).

1.  **Log into Facebook:** Start by logging into your Facebook account that is associated with your business.
2.  **Access Ads Manager:** Ads Manager can be found in the drop-down menu in the upper right corner of your Facebook homepage or business page.
3.  **Locate your ad account:** In Ads Manager, click on the ad account Settings from the menu on the bottom left of the screen.
4.  **View ad account information:** In the Settings screen, you will find your ad account number listed along with other details such as your billing information and spending limits.

[](#)

## Meta Developer Account

See [Register as a Meta Developer](/docs/development/register) for more information.

[](#)

## Create an App

See [Create an App](/docs/development/create-an-app) for more information on setting up an app in the App Dashboard as well as app types and use cases.

[](#)

## Authorization and Authentication

See [Authorization](/docs/marketing-api/overview/authorization) for more information on verifying the users and apps that will be accessing the Marketing API and granting them permissions.

See [Authentication](/docs/marketing-apis/overview/authentication) for more information on getting, extending, and renewing access tokens with the Marketing API.

[](#)

## Next Steps

1.  [Create an Ad Campaign](/docs/marketing-api/get-started/basic-ad-creation/create-an-ad-campaign)
2.  [Manage Ad Campaigns](/docs/marketing-api/get-started/manage-campaigns)
3.  [Optimize Ad Campaigns](/docs/marketing-api/get-started/ad-optimization-basics)

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)