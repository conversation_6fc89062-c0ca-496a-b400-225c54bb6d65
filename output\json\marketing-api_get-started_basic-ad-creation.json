{"title": "Automating Ad Creation", "breadcrumbs": [], "content": "<h1 id=\"automating-ad-creation\">Automating Ad Creation</h1>\n\n<p>Creating ads using the Marketing API involves a systematic approach that includes setting up campaigns, ad sets, and ad creatives. This document provides detailed guidance on programmatically creating these components, along with code samples to illustrate the implementation process.</p>\n\n\n<h2 id=\"ad-creation-endpoints\">Ad Creation Endpoints</h2>\n\n<p>The Marketing API offers a variety of key endpoints that serve as essential tools for developers to create, manage, and analyze advertising campaigns. The primary creation endpoints include <code>campaigns</code>, <code>adsets</code>, and <code>ads</code>. Understanding these endpoints and their functionalities is crucial for both new and experienced developers looking to optimize their advertising strategies.</p>\n\n<h3 id=\"the-campaigns-endpoint\">The <code>campaigns</code> endpoint</h3>\n\n<p>The <a href=\"/docs/marketing-api/reference/ad-account/campaigns\"><code>campaigns</code> endpoint</a> is used to create and manage advertising campaigns. This endpoint allows users to set the overall objectives for their marketing efforts, such as brand awareness or conversions.</p>\n\n<p><strong>Example API Request:</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/campaigns \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=My Campaign'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'objective=LINK_CLICKS'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=PAUSED'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span></pre><h3 id=\"the-adsets-endpoint\">The <code>adsets</code> endpoint</h3>\n\n<p>The <a href=\"/docs/marketing-api/reference/ad-account/adsets\"><code>adsets</code> endpoint</a> organizes ads within campaigns based on specific targeting criteria and budget allocation. This allows for more granular control over audience targeting and spending.</p>\n\n<p><strong>Example API Request:</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adsets \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=My Ad Set'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'campaign_id=&lt;CAMPAIGN_ID&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'daily_budget=1000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'targeting={\"geo_locations\":{\"countries\":[\"US\"]}}'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span></pre><h3 id=\"the-ads-endpoint\">The <code>ads</code> endpoint</h3>\n\n<p>The <a href=\"/docs/marketing-api/reference/ad-account/ads\"><code>ads</code> endpoint</a> is where the actual advertisements are created, allowing you to define creative elements and link them to the appropriate ad set.</p>\n\n<p><strong>Example API Request:</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/ads \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=My Ad'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'adset_id=&lt;AD_SET_ID&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'creative={\"creative_id\": \"&lt;CREATIVE_ID&gt;\"}'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=ACTIVE'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span></pre>\n\n<div class=\"_ap2s clearfix\"><a class=\"_ap2t _ap2v\" href=\"/docs/marketing-api/get-started/basic-ad-creation/create-an-ad-campaign\"><div class=\"_ap2x\">→</div><div class=\"_ap2w\">Next</div><div class=\"_ap2z\">Create an Ad Campaign</div></a></div>\n\n", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/get-started", "/docs/marketing-api/get-started/basic-ad-creation", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started/authorization", "/docs/marketing-api/get-started/authentication", "/docs/marketing-api/get-started/basic-ad-creation/create-an-ad-campaign", "/docs/marketing-api/get-started/basic-ad-creation/create-an-ad-set", "/docs/marketing-api/get-started/basic-ad-creation/create-an-ad-creative", "/docs/marketing-api/get-started/basic-ad-creation/create-an-ad", "/docs/marketing-api/get-started/manage-campaigns", "/docs/marketing-api/get-started/ad-optimization-basics", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/ad-account/campaigns", "/docs/marketing-api/reference/ad-account/adsets", "/docs/marketing-api/reference/ad-account/ads"], "url": "https://developers.facebook.com/docs/marketing-api/get-started/basic-ad-creation", "timestamp": "2025-06-25T15:48:55.797Z"}