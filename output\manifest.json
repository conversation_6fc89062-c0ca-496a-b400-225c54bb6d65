{"totalPages": 83, "aiProcessedPages": 83, "pages": [{"filename": "marketing-api_ad-preview-plugin.json", "title": "Ad Preview Plugin", "url": "https://developers.facebook.com/docs/marketing-api/ad-preview-plugin/v23.0", "timestamp": "2025-06-25T15:43:21.779Z", "aiProcessed": true}, {"filename": "marketing-api_ad-rules.json", "title": "Ad Rules Engine", "url": "https://developers.facebook.com/docs/marketing-api/ad-rules", "timestamp": "2025-06-25T15:06:34.152Z", "aiProcessed": true}, {"filename": "marketing-api_adcreative.json", "title": "Ad Creative", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-creative", "timestamp": "2025-06-25T15:41:06.050Z", "aiProcessed": true}, {"filename": "marketing-api_audiences.json", "title": "Audiences", "url": "https://developers.facebook.com/docs/marketing-api/audiences", "timestamp": "2025-06-25T15:06:50.670Z", "aiProcessed": true}, {"filename": "marketing-api_best-practices.json", "title": "Best Practices", "url": "https://developers.facebook.com/docs/marketing-api/best-practices", "timestamp": "2025-06-25T15:08:15.787Z", "aiProcessed": true}, {"filename": "marketing-api_bidding.json", "title": "Bidding", "url": "https://developers.facebook.com/docs/marketing-api/bidding", "timestamp": "2025-06-25T15:06:10.648Z", "aiProcessed": true}, {"filename": "marketing-api_brand-safety-and-suitability.json", "title": "Brand Safety and Suitability", "url": "https://developers.facebook.com/docs/marketing-api/brand-safety-and-suitability", "timestamp": "2025-06-25T15:07:51.870Z", "aiProcessed": true}, {"filename": "marketing-api_catalog.json", "title": "Catalog", "url": "https://developers.facebook.com/docs/marketing-api/catalog", "timestamp": "2025-06-25T15:50:07.573Z", "aiProcessed": true}, {"filename": "marketing-api_conversions-api.json", "title": "Conversions API", "url": "https://developers.facebook.com/docs/marketing-api/conversions-api", "timestamp": "2025-06-25T15:49:47.177Z", "aiProcessed": true}, {"filename": "marketing-api_creative.json", "title": "Ad Creative", "url": "https://developers.facebook.com/docs/marketing-api/creative", "timestamp": "2025-06-25T15:05:25.102Z", "aiProcessed": true}, {"filename": "marketing-api_currencies.json", "title": "Currency Codes", "url": "https://developers.facebook.com/docs/marketing-api/currencies", "timestamp": "2025-06-25T15:45:21.876Z", "aiProcessed": true}, {"filename": "marketing-api_generatepreview.json", "title": "Ad Previews", "url": "https://developers.facebook.com/docs/marketing-api/generatepreview/v23.0", "timestamp": "2025-06-25T15:42:42.552Z", "aiProcessed": true}, {"filename": "marketing-api_get-started.json", "title": "Get Started with the Marketing API", "url": "https://developers.facebook.com/docs/marketing-api/get-started", "timestamp": "2025-06-25T15:05:04.722Z", "aiProcessed": true}, {"filename": "marketing-api_get-started_ad-optimization-basics.json", "title": "Ad Optimization Basics", "url": "https://developers.facebook.com/docs/marketing-api/get-started/ad-optimization-basics", "timestamp": "2025-06-25T15:49:32.619Z", "aiProcessed": true}, {"filename": "marketing-api_get-started_basic-ad-creation.json", "title": "Automating Ad Creation", "url": "https://developers.facebook.com/docs/marketing-api/get-started/basic-ad-creation", "timestamp": "2025-06-25T15:48:55.797Z", "aiProcessed": true}, {"filename": "marketing-api_get-started_manage-campaigns.json", "title": "Ad Campaign Management", "url": "https://developers.facebook.com/docs/marketing-api/get-started/manage-campaigns", "timestamp": "2025-06-25T15:49:13.166Z", "aiProcessed": true}, {"filename": "marketing-api_image-crops.json", "title": "Image Crops", "url": "https://developers.facebook.com/docs/marketing-api/image-crops", "timestamp": "2025-06-25T15:46:29.300Z", "aiProcessed": true}, {"filename": "marketing-api_insights.json", "title": "Insights API", "url": "https://developers.facebook.com/docs/marketing-api/insights", "timestamp": "2025-06-25T15:07:10.091Z", "aiProcessed": true}, {"filename": "marketing-api_marketing-api-changelog.json", "title": "Changelog", "url": "https://developers.facebook.com/docs/marketing-api/marketing-api-changelog", "timestamp": "2025-06-25T15:47:54.371Z", "aiProcessed": true}, {"filename": "marketing-api_out-of-cycle-changes.json", "title": "Out-Of-Cycle Changes", "url": "https://developers.facebook.com/docs/marketing-api/out-of-cycle-changes", "timestamp": "2025-06-25T15:48:26.157Z", "aiProcessed": true}, {"filename": "marketing-api_reference.json", "title": "Marketing API Reference", "url": "https://developers.facebook.com/docs/marketing-api/reference/v23.0", "timestamp": "2025-06-25T15:09:22.510Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account-user.json", "title": "Ad Account User", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account-user", "timestamp": "2025-06-25T15:50:27.102Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account.json", "title": "Ad Account", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account", "timestamp": "2025-06-25T15:10:04.239Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_account_controls.json", "title": "Ad Account Account Controls", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/account_controls/", "timestamp": "2025-06-25T15:10:51.247Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_activities.json", "title": "Ad Account Activities", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/activities/", "timestamp": "2025-06-25T15:11:28.781Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_ad_place_page_sets.json", "title": "Ad Account Ad Place Page Sets", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "timestamp": "2025-06-25T15:12:03.924Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_ad_place_page_sets_async.json", "title": "Page Not Found - <PERSON><PERSON> for Developers", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ad_place_page_sets_async/", "timestamp": "2025-06-25T15:12:52.652Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adcreatives.json", "title": "", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/", "timestamp": "2025-06-25T15:13:15.229Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adimages.json", "title": "", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adimages/", "timestamp": "2025-06-25T15:14:06.553Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adlabels.json", "title": "", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adlabels/", "timestamp": "2025-06-25T15:14:47.410Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adplayables.json", "title": "Ad Account Adplayables", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adplayables/", "timestamp": "2025-06-25T15:15:16.341Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adrules_library.json", "title": "Ad Account Adrules Library", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adrules_library/", "timestamp": "2025-06-25T15:15:52.113Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_ads.json", "title": "Ad Account Ads", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/", "timestamp": "2025-06-25T15:16:32.386Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_ads_reporting_mmm_reports.json", "title": "Ad Account Ads Reporting Mmm Reports", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "timestamp": "2025-06-25T15:17:16.333Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_ads_reporting_mmm_schedulers.json", "title": "Ad Account Ads Reporting Mmm Schedulers", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "timestamp": "2025-06-25T15:17:38.094Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adsets.json", "title": "Ad Account Adsets", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/", "timestamp": "2025-06-25T15:18:03.213Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_adspixels.json", "title": "Ad Account Adspixels", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adspixels/", "timestamp": "2025-06-25T15:18:46.782Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_advertisable_applications.json", "title": "Ad Account Advertisable Applications", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/advertisable_applications/", "timestamp": "2025-06-25T15:19:17.480Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_advideos.json", "title": "Ad Videos", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/", "timestamp": "2025-06-25T15:19:46.969Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_agencies.json", "title": "Ad Account Agencies", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/agencies/", "timestamp": "2025-06-25T15:20:28.085Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_applications.json", "title": "Ad Account Applications", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/applications/", "timestamp": "2025-06-25T15:20:59.413Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_assigned_users.json", "title": "Ad Account, Assigned Users", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/assigned_users/", "timestamp": "2025-06-25T15:21:27.013Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_async_batch_requests.json", "title": "Ad Account Async Batch Requests", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/async_batch_requests/", "timestamp": "2025-06-25T15:22:09.476Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_asyncadcreatives.json", "title": "Ad Account Asyncadcreatives", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/asyncadcreatives/", "timestamp": "2025-06-25T15:22:37.225Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_asyncadrequestsets.json", "title": "Ad Account Asyncadrequestsets", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "timestamp": "2025-06-25T15:22:58.854Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_broadtargetingcategories.json", "title": "Ad Account Broadtargetingcategories", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "timestamp": "2025-06-25T15:23:27.146Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_campaigns.json", "title": "", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/campaigns/", "timestamp": "2025-06-25T15:23:58.476Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_connected_instagram_accounts.json", "title": "Page Not Found - <PERSON><PERSON> for Developers", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/connected_instagram_accounts/", "timestamp": "2025-06-25T15:25:05.939Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_customaudiences.json", "title": "Ad Account Customaudiences", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiences/", "timestamp": "2025-06-25T15:25:25.839Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_customaudiencestos.json", "title": "Ad Account Customaudiencestos", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiencestos/", "timestamp": "2025-06-25T15:26:13.821Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_customconversions.json", "title": "Ad Account, Custom Conversions", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customconversions/", "timestamp": "2025-06-25T15:26:48.674Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_delivery_estimate.json", "title": "Ad Account Delivery Estimate", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/delivery_estimate/", "timestamp": "2025-06-25T15:27:24.022Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_deprecatedtargetingadsets.json", "title": "Ad Account Deprecatedtargetingadsets", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "timestamp": "2025-06-25T15:28:01.529Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_dsa_recommendations.json", "title": "Ad Account Dsa Recommendations", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/dsa_recommendations/", "timestamp": "2025-06-25T15:28:33.592Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_impacting_ad_studies.json", "title": "Ad Account Impacting Ad Studies", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "timestamp": "2025-06-25T15:29:04.892Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_insights.json", "title": "", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/", "timestamp": "2025-06-25T15:29:41.748Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_instagram_accounts.json", "title": "Ad Account Instagram Accounts", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/instagram_accounts/", "timestamp": "2025-06-25T15:30:21.524Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_mcmeconversions.json", "title": "Ad Account Mcmeconversions", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/mcmeconversions/", "timestamp": "2025-06-25T15:30:50.409Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_minimum_budgets.json", "title": "Ad Account Minimum Budgets", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/minimum_budgets/", "timestamp": "2025-06-25T15:31:13.304Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_product_audiences.json", "title": "Ad Account Product Audiences", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/product_audiences/", "timestamp": "2025-06-25T15:31:45.245Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_promote_pages.json", "title": "Ad Account Promote Pages", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/promote_pages/", "timestamp": "2025-06-25T15:37:19.364Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_publisher_block_lists.json", "title": "Ad Account Publisher Block Lists", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/publisher_block_lists/", "timestamp": "2025-06-25T15:37:41.472Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_reachestimate.json", "title": "Ad Account Reachestimate", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/reachestimate/", "timestamp": "2025-06-25T15:37:59.900Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_reachfrequencypredictions.json", "title": "", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "timestamp": "2025-06-25T15:38:28.305Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_saved_audiences.json", "title": "Ad Account Saved Audiences", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/saved_audiences/", "timestamp": "2025-06-25T15:38:58.212Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_subscribed_apps.json", "title": "Ad Account Subscribed Apps", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/subscribed_apps/", "timestamp": "2025-06-25T15:39:26.736Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_targetingbrowse.json", "title": "Ad Account Targetingbrowse", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/targetingbrowse/", "timestamp": "2025-06-25T15:39:49.935Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_targetingsearch.json", "title": "Ad Account Targetingsearch", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/targetingsearch/", "timestamp": "2025-06-25T15:40:09.934Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-account_tracking.json", "title": "Ad Account Tracking", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/tracking/", "timestamp": "2025-06-25T15:40:42.682Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-campaign-group.json", "title": "Campaign", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group", "timestamp": "2025-06-25T15:52:43.439Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-campaign.json", "title": "Ad Set", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-campaign", "timestamp": "2025-06-25T15:51:41.638Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-creative.json", "title": "Ad Creative", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-creative", "timestamp": "2025-06-25T15:50:44.286Z", "aiProcessed": true}, {"filename": "marketing-api_reference_ad-image.json", "title": "Ad, Image", "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-image", "timestamp": "2025-06-25T15:41:58.047Z", "aiProcessed": true}, {"filename": "marketing-api_reference_business-role-request.json", "title": "Business Role Request", "url": "https://developers.facebook.com/docs/marketing-api/reference/business-role-request", "timestamp": "2025-06-25T15:44:23.100Z", "aiProcessed": true}, {"filename": "marketing-api_reference_business-user.json", "title": "Business User", "url": "https://developers.facebook.com/docs/marketing-api/reference/business-user", "timestamp": "2025-06-25T15:44:47.162Z", "aiProcessed": true}, {"filename": "marketing-api_reference_business.json", "title": "Business", "url": "https://developers.facebook.com/docs/marketing-api/reference/business", "timestamp": "2025-06-25T15:43:45.896Z", "aiProcessed": true}, {"filename": "marketing-api_reference_high-demand-period.json", "title": "High Demand Period - Marketing API", "url": "https://developers.facebook.com/docs/marketing-api/reference/high-demand-period/v23.0", "timestamp": "2025-06-25T15:46:17.999Z", "aiProcessed": true}, {"filename": "marketing-api_reference_product-catalog.json", "title": "Product Catalog", "url": "https://developers.facebook.com/docs/marketing-api/reference/product-catalog", "timestamp": "2025-06-25T15:46:51.347Z", "aiProcessed": true}, {"filename": "marketing-api_reference_system-user.json", "title": "System User", "url": "https://developers.facebook.com/docs/marketing-api/reference/system-user", "timestamp": "2025-06-25T15:47:33.457Z", "aiProcessed": true}, {"filename": "marketing-api_troubleshooting.json", "title": "Troubleshooting", "url": "https://developers.facebook.com/docs/marketing-api/troubleshooting", "timestamp": "2025-06-25T15:09:00.645Z", "aiProcessed": true}, {"filename": "marketing-apis.json", "title": "Marketing API", "url": "https://developers.facebook.com/docs/marketing-apis", "timestamp": "2025-06-25T15:04:14.698Z", "aiProcessed": true}, {"filename": "marketing-apis_overview#how-it-works.json", "title": "Overview", "url": "https://developers.facebook.com/docs/marketing-apis/overview#how-it-works", "timestamp": "2025-06-25T15:48:09.010Z", "aiProcessed": true}, {"filename": "marketing-apis_overview.json", "title": "Overview", "url": "https://developers.facebook.com/docs/marketing-apis/overview", "timestamp": "2025-06-25T15:04:40.101Z", "aiProcessed": true}], "aiProcessingEnabled": true, "generatedAt": "2025-06-25T15:53:25.363Z"}