{"title": "Facebook Marketing API - Ad Account Advertisable Applications", "summary": "This endpoint returns the advertisable applications associated with an Ad Account. It provides access to applications that can be promoted through Facebook advertising, with special restrictions for apps in developer mode.", "content": "# Ad Account Advertisable Applications\n\nA call to this edge returns the Ad Account's advertisable applications.\n\n## Restrictions\n\nApplications in developer mode are only returned if the access token belongs to a user who is an admin on the app. The restrictions do not apply to apps that are live.\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/advertisable_applications\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `app_id` | numeric string or integer | Specify App ID for the specific application |\n| `business_id` | numeric string or integer | Specify Business ID for applications under a specific business |\n\n### Response Format\n\nReading from this edge will return a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Data Fields\n\nA list of Application nodes with the following additional fields:\n\n| Field | Type | Description |\n|-------|------|--------------|\n| `advertisable_app_events` | list<string> | Available app events to be advertised |\n| `cpa_access` | CPA | CPA Access for apps |\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/advertisable_applications HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/advertisable_applications',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/advertisable_applications\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 3000 | Reading insights of a Page, business, app, domain or event source group not owned by the querying user or application |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 80004 | Too many calls to this ad-account. Rate limiting applied |\n| 100 | Invalid parameter |\n\n## Operations\n\n- **Creating**: Not supported on this endpoint\n- **Updating**: Not supported on this endpoint  \n- **Deleting**: Not supported on this endpoint", "keyPoints": ["Returns advertisable applications associated with an Ad Account", "Apps in developer mode require admin access token for visibility", "Supports filtering by app_id or business_id parameters", "Returns Application nodes with additional advertising-specific fields", "Read-only endpoint - no create, update, or delete operations supported"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/advertisable_applications"], "parameters": ["app_id", "business_id", "advertisable_app_events", "cpa_access"], "examples": ["HTTP GET request example", "PHP SDK implementation", "JavaScript SDK usage", "Android SDK example", "iOS SDK example"], "tags": ["Facebook Marketing API", "Ad Account", "Applications", "Advertising", "Graph API", "Mobile Apps"], "relatedTopics": ["Application nodes", "Graph API pagination", "OAuth 2.0 Access Tokens", "Rate limiting", "App events", "CPA access"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/advertisable_applications/", "processedAt": "2025-06-25T16:22:31.943Z", "processor": "openrouter-claude-sonnet-4"}