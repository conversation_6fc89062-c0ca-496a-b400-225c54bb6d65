{"title": "Ad Account User", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_uu\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_q+\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_bd\"></div></span></div></div>\n\n<h1 id=\"overview\">Ad Account User</h1>\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Data of App Scoped User.</p>\n</div><div><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_G2\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_5T\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_7a\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_QX\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_dt\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_HM\">iOS SDK</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=act_%7Bad-account-id%7D%3Ffields%3D%257Bfieldname_of_type_AdAccountUser%257D&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_b_sY\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">{</span><span class=\"pln\">ad</span><span class=\"pun\">-</span><span class=\"pln\">account</span><span class=\"pun\">-</span><span class=\"pln\">id</span><span class=\"pun\">}?</span><span class=\"pln\">fields</span><span class=\"pun\">={</span><span class=\"pln\">fieldname_of_type_AdAccountUser</span><span class=\"pun\">}</span><span class=\"pln\"> HTTP</span><span class=\"pun\">/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div><p></p></div><div><h3 id=\"parameters\">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID of the App Scoped User</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>User public full name</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>tasks</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Tasks of App Scoped User</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr></tbody></table></div></div><div></div></div></div>\n\n<h2 id=\"Creating\">Creating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div>\n\n<h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div>\n\n<h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account-user", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account-user", "timestamp": "2025-06-25T15:50:27.102Z"}