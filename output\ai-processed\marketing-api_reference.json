{"title": "Facebook Marketing API v23.0 Reference - Root Nodes and Core Objects", "summary": "Comprehensive reference documentation for Facebook Marketing API v23.0, covering root nodes and core advertising objects including Ad Accounts, Campaigns, Ad Sets, Ads, and Ad Creatives. Provides detailed information about available edges and endpoints for each object type.", "content": "# Marketing API Reference v23.0\n\n## Overview\n\nThis is a complete reference for the Facebook Marketing API v23.0 root nodes and core objects. The API follows Graph API architecture patterns for accessing advertising data and managing campaigns.\n\n**Note:** Access to all reference information requires Facebook login.\n\n## Root Nodes\n\n| Node Pattern | Description |\n|--------------|-------------|\n| `/{AD_ACCOUNT_USER_ID}` | Someone on Facebook who creates ads. Each ad user can have a role on several ad accounts. |\n| `/act_{AD_ACCOUNT_ID}` | Represents the business entity managing ads. |\n| `/{AD_ID}` | Contains information for an ad, such as creative elements and measurement information. |\n| `/{AD_CREATIVE_ID}` | Format for your image, carousel, collection, or video ad. |\n| `/{AD_SET_ID}` | Contains all ads that share the same budget, schedule, bid, and targeting. |\n| `/{AD_CAMPAIGN_ID}` | Defines your ad campaigns' objective. Contains one or more ad set. |\n\n## Core Objects\n\n### User\n\nRepresents a Facebook user who can create and manage ads.\n\n#### Key Edges\n- `/adaccounts` - All ad accounts associated with this person\n- `/accounts` - All pages and places that someone is an admin of\n- `/promotable_events` - All promotable events you created or promotable page events\n\n### Ad Account\n\nAll collections of ad objects in Marketing APIs belong to an ad account. This is the primary container for advertising activities.\n\n#### Popular Edges\n- `/adcreatives` - Defines your ad's appearance and content\n- `/adimages` - Library of images to use in ad creatives\n- `/ads` - Data for an ad, such as creative elements and measurement information\n- `/adsets` - Contain all ads that share the same budget, schedule, bid, and targeting\n- `/advideos` - Library of videos for use in ad creatives\n- `/campaigns` - Define your campaigns' objective and contain one or more ad sets\n- `/customaudiences` - The custom audiences owned by/shared with this ad account\n- `/insights` - Interface for insights with de-duplication, sorting, and async reports\n- `/users` - List of people associated with an ad account\n\n### Ad\n\nAn individual ad associated with an ad set.\n\n#### Key Edges\n- `/adcreatives` - Defines your ad's appearance and content\n- `/insights` - Insights on your advertising performance\n- `/leads` - Any leads associated with a Lead Ad\n- `/previews` - Generate ad previews from an existing ad\n\n### Ad Set\n\nA group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data.\n\n#### Key Edges\n- `/activities` - Log of actions taken on the ad set\n- `/adcreatives` - Defines your ad's content and appearance\n- `/ads` - Data necessary for an ad, such as creative elements and measurement information\n- `/insights` - Insights on your advertising performance\n\n### Ad Campaign\n\nThe highest level organizational structure within an ad account, representing a single objective for an advertiser.\n\n#### Key Edges\n- `/ads` - Data necessary for an ad, such as creative elements and measurement information\n- `/adsets` - Contain all ads that share the same budget, schedule, bid, and targeting\n- `/insights` - Insights on your advertising performance\n\n### Ad Creative\n\nThe format which provides layout and contains content for the ad.\n\n#### Key Edges\n- `/previews` - Generate ad previews from the existing ad creative object", "keyPoints": ["Facebook Marketing API v23.0 follows Graph API architecture with root nodes and edges", "Ad Account is the primary container for all advertising objects and activities", "Campaign hierarchy flows from Campaign → Ad Set → Ad, with Ad Creatives defining appearance", "Insights endpoints are available at multiple levels for performance analytics", "All reference documentation requires Facebook login for full access"], "apiEndpoints": ["/{AD_ACCOUNT_USER_ID}", "/act_{AD_ACCOUNT_ID}", "/{AD_ID}", "/{AD_CREATIVE_ID}", "/{AD_SET_ID}", "/{AD_CAMPAIGN_ID}", "/adaccounts", "/accounts", "/promotable_events", "/adcreatives", "/adimages", "/ads", "/adsets", "/advideos", "/campaigns", "/customaudiences", "/insights", "/users", "/leads", "/previews", "/activities"], "parameters": ["AD_ACCOUNT_USER_ID", "AD_ACCOUNT_ID", "AD_ID", "AD_CREATIVE_ID", "AD_SET_ID", "AD_CAMPAIGN_ID", "budget", "schedule", "bid", "targeting"], "examples": [], "tags": ["Facebook Marketing API", "Graph API", "Advertising", "API Reference", "v23.0", "Ad Management", "Campaign Management"], "relatedTopics": ["Graph API Usage", "Ad Account Management", "Campaign Structure", "Ad Creative Management", "Insights and Analytics", "Custom Audiences", "Lead Ads", "Ad Previews"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/v23.0", "processedAt": "2025-06-25T16:18:07.450Z", "processor": "openrouter-claude-sonnet-4"}