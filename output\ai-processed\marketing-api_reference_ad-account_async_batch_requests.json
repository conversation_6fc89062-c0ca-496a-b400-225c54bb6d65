{"title": "Facebook Marketing API - Ad Account Async Batch Requests", "summary": "Documentation for the Ad Account Async Batch Requests endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous batch requests for ad accounts but does not support reading, updating, or deleting operations.", "content": "# Ad Account Async Batch Requests\n\n## Overview\n\nThe Ad Account Async Batch Requests endpoint allows you to create asynchronous batch requests for ad account operations. This endpoint only supports POST operations for creating batch requests.\n\n## Supported Operations\n\n### Reading\nYou can't perform this operation on this endpoint.\n\n### Creating\nYou can make a POST request to the `async_batch_requests` edge from the following path:\n- `/act_{ad_account_id}/async_batch_requests`\n\nWhen posting to this edge, a Campaign will be created.\n\n#### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `adbatch` | list<Object> | JSON encoded batch request | Yes |\n| `name` | string | Name of the batch request for tracking purposes | Yes |\n| `relative_url` | string | Relative URL for the batch request | Yes |\n| `body` | UTF-8 encoded string | Request body content | Yes |\n\n#### Return Type\n\nThis endpoint supports read-after-write and will read the node represented by `id` in the return type.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 194 | Missing at least one required parameter |\n\n### Updating\nYou can't perform this operation on this endpoint.\n\n### Deleting\nYou can't perform this operation on this endpoint.", "keyPoints": ["Only supports POST operations for creating async batch requests", "Requires ad account ID in the endpoint path format /act_{ad_account_id}/async_batch_requests", "All parameters (adbatch, name, relative_url, body) are required", "Returns a numeric string ID upon successful creation", "Supports read-after-write functionality"], "apiEndpoints": ["/act_{ad_account_id}/async_batch_requests"], "parameters": ["adbatch", "name", "relative_url", "body"], "examples": [], "tags": ["Facebook Marketing API", "Ad Account", "Async Batch Requests", "POST", "Batch Operations"], "relatedTopics": ["Campaign", "Ad Account", "Graph API", "Read-after-write", "<PERSON><PERSON> requests"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/async_batch_requests/", "processedAt": "2025-06-25T16:24:38.829Z", "processor": "openrouter-claude-sonnet-4"}