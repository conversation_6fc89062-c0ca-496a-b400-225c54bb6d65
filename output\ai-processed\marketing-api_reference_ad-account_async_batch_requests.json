{"title": "Facebook Marketing API - Ad Account Async Batch Requests", "summary": "Documentation for the Ad Account Async Batch Requests endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous batch requests for ad accounts but does not support reading, updating, or deleting operations.", "content": "# Ad Account Async Batch Requests\n\n## Overview\n\nThe Ad Account Async Batch Requests endpoint is part of the Facebook Marketing API v23.0 that enables asynchronous batch processing for ad account operations.\n\n## Supported Operations\n\n### Reading\nReading operations are **not supported** on this endpoint.\n\n### Creating\nYou can make a POST request to the `async_batch_requests` edge from the following path:\n- `/act_{ad_account_id}/async_batch_requests`\n\nWhen posting to this edge, a Campaign will be created.\n\n#### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `adbatch` | list<Object> | JSON encoded batch request | Yes |\n| `name` | string | Name of the batch request for tracking purposes | Yes |\n| `relative_url` | string | Relative URL for the request | Yes |\n| `body` | UTF-8 encoded string | Request body content | Yes |\n\n#### Return Type\nThis endpoint supports read-after-write and will read the node represented by `id` in the return type.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 194 | Missing at least one required parameter |\n\n### Updating\nUpdating operations are **not supported** on this endpoint.\n\n### Deleting\nDeleting operations are **not supported** on this endpoint.", "keyPoints": ["Only POST (creating) operations are supported on this endpoint", "Requires JSON encoded batch request data with specific parameters", "Creates Campaign objects when posting to the edge", "Supports read-after-write functionality", "Returns a numeric string ID upon successful creation"], "apiEndpoints": ["/act_{ad_account_id}/async_batch_requests"], "parameters": ["adbatch (list<Object>, required)", "name (string, required)", "relative_url (string, required)", "body (UTF-8 encoded string, required)"], "examples": [], "tags": ["Facebook Marketing API", "Ad Account", "Async Batch Requests", "Campaign Creation", "Batch Processing"], "relatedTopics": ["Campaign", "Ad Account", "Graph API", "Read-after-write", "<PERSON><PERSON> requests"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/async_batch_requests/", "processedAt": "2025-06-25T15:22:22.881Z", "processor": "openrouter-claude-sonnet-4"}