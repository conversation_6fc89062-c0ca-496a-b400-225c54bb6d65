{"title": "Marketing API Overview", "summary": "The Marketing API is a Meta business tool designed to help developers and marketers automate advertising efforts across Meta technologies. It provides comprehensive functionality for creating, managing, and analyzing ad campaigns programmatically.", "content": "## Marketing API Overview\n\n### Key Features\n- Automate ad creation and management\n- Programmatically generate ad campaigns, ad sets, and individual ads\n- Access detailed insights and analytics\n- Update, pause, or delete ads seamlessly\n\n### How It Works\n\n#### Ad Components\n1. **Ad Campaigns**: Highest-level organizational structure representing a single marketing objective\n2. **Ad Sets**: Groups of ads with shared targeting, budget, and optimization goals\n3. **Ad Creatives**: Visual elements stored in a creative library\n4. **Ads**: Specific ad objects containing creative elements and targeting information\n\n#### Hierarchical Structure\n- Campaign: Sets overall objective\n- Ad Set: Configures budget, targeting, and scheduling\n- Ad: Contains specific creative and placement details", "keyPoints": ["Enables programmatic advertising across Meta platforms", "Supports full lifecycle of ad management", "Provides granular control over ad campaigns", "Allows automated creation and optimization of ads", "Offers detailed performance insights"], "apiEndpoints": ["/marketing-api/create-campaign", "/marketing-api/create-ad-set", "/marketing-api/create-ad"], "parameters": ["campaign_objective", "ad_set_budget", "targeting_parameters", "creative_elements", "optimization_goal"], "examples": ["Programmatically create a campaign with specific marketing objective", "Generate multiple ad variations within a single ad set", "Update ad performance in real-time"], "tags": ["advertising", "meta", "facebook", "marketing", "api"], "relatedTopics": ["Ad Creative Management", "Audience Targeting", "Performance Insights", "Bidding Strategies"], "difficulty": "intermediate", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-apis/overview", "processedAt": "2025-06-25T14:55:27.911Z", "processor": "openrouter-claude-3.5-haiku"}