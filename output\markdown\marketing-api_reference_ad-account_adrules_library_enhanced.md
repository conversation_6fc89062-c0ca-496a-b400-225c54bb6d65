# Facebook Marketing API - Ad Account Ad Rules Library Reference

## Summary
Complete reference documentation for the Facebook Marketing API's Ad Account Ad Rules Library endpoint, covering how to read, create, and manage automated ad rules for ad accounts. This endpoint allows developers to programmatically manage rule-based automation for advertising campaigns.

## Key Points
- The Ad Rules Library endpoint manages automated rules for Facebook ad accounts
- Reading returns a list of AdRule nodes with pagination support
- Creating rules requires evaluation_spec, execution_spec, and name parameters
- Rules can be triggered by schedules or specific events like metadata changes
- Update and delete operations are not supported on this endpoint

## API Endpoints
- `GET /v23.0/{ad-account-id}/adrules_library`
- `POST /act_{ad_account_id}/adrules_library`

## Parameters
- account_id
- evaluation_spec
- evaluation_type
- filters
- execution_spec
- execution_type
- name
- schedule_spec
- status
- trigger

## Content
# Ad Account Ad Rules Library

The Ad Account Ad Rules Library endpoint allows you to manage automated rules for ad accounts in the Facebook Marketing API.

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/adrules_library
```

### Example Requests

**HTTP**
```http
GET /v23.0/{ad-account-id}/adrules_library HTTP/1.1
Host: graph.facebook.com
```

**PHP SDK**
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/adrules_library',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

**JavaScript SDK**
```javascript
FB.api(
    "/{ad-account-id}/adrules_library",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Parameters
This endpoint doesn't have any parameters.

### Response Fields
Reading from this edge returns a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

- **data**: A list of AdRule nodes
- **paging**: Pagination information (see Graph API guide for details)

### Error Codes
| Error | Description |
|-------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 80004 | Too many calls to this ad-account. Wait and try again |
| 368 | Action deemed abusive or disallowed |

## Creating

You can create new ad rules by making a POST request to the adrules_library edge.

### Endpoint
```
POST /act_{ad_account_id}/adrules_library
```

### Required Parameters

**account_id** (numeric string)
- Ad Account ID (inferred from path)

**evaluation_spec** (Object)
- Defines the evaluation spec for rule execution
- **evaluation_type** (enum): SCHEDULE, TRIGGER
- **filters** (list<Object>):
  - **field** (string): Required
  - **value** (numeric, string, boolean, list, or object): Required
  - **operator** (enum): GREATER_THAN, LESS_THAN, EQUAL, NOT_EQUAL, IN_RANGE, NOT_IN_RANGE, IN, NOT_IN, CONTAIN, NOT_CONTAIN, ANY, ALL, NONE
- **trigger** (Object):
  - **type** (enum): METADATA_CREATION, METADATA_UPDATE, STATS_MILESTONE, STATS_CHANGE, DELIVERY_INSIGHTS_CHANGE
  - **field** (string)
  - **value** (numeric, string, boolean, list, or object)
  - **operator** (enum): Same as filters operator

**execution_spec** (Object)
- Defines the execution spec for rule execution
- **execution_type** (enum): DCO, PING_ENDPOINT, NOTIFICATION, PAUSE, REBALANCE_BUDGET, CHANGE_BUDGET, CHANGE_BID, ROTATE, UNPAUSE, CHANGE_CAMPAIGN_BUDGET, ADD_INTEREST_RELAXATION, ADD_QUESTIONNAIRE_INTERESTS, INCREASE_RADIUS, UPDATE_CREATIVE, UPDATE_LAX_BUDGET, UPDATE_LAX_DURATION, AUDIENCE_CONSOLIDATION, AUDIENCE_CONSOLIDATION_ASK_FIRST, AD_RECOMMENDATION_APPLY
- **is_once_off** (boolean)
- **execution_options** (list<Object>):
  - **field** (string): Required
  - **value** (numeric, string, boolean, list, or object): Required
  - **operator** (enum): EQUAL, IN

**name** (string)
- The friendly name of the rule

### Optional Parameters

**schedule_spec** (Object)
- Specifies the schedule for rule evaluation
- **schedule_type** (enum): DAILY, HOURLY, SEMI_HOURLY, CUSTOM
- **schedule** (list<Object>):
  - **start_minute** (int64)
  - **end_minute** (int64)
  - **days** (list<int64>)

**status** (enum)
- Rule status: ENABLED, DISABLED, DELETED, HAS_ISSUES

### Return Type
Returns a struct with the created rule ID:
```json
{
  "id": "numeric_string"
}
```

### Error Codes
| Error | Description |
|-------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |
| 2703 | Rules that turn off ads can't have cost conditions |
| 190 | Invalid OAuth 2.0 Access Token |
| 368 | Action deemed abusive or disallowed |

## Updating
You can't perform update operations on this endpoint.

## Deleting
You can't perform delete operations on this endpoint.

## Examples
HTTP GET request example

PHP SDK implementation

JavaScript SDK usage

Android SDK example

iOS SDK example

---
**Tags:** Facebook Marketing API, Ad Rules, Automation, Ad Account, Graph API, Campaign Management
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adrules_library/
**Processed:** 2025-06-25T15:16:22.674Z