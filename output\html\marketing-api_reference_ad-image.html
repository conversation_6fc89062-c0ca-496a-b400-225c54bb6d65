<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_Gb"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_ev"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_Lk"></div></span></div></div>

<h1 id="overview"><p>Ad, Image</p></h1>

<p>Upload and manage images to later use in <a href="/docs/marketing-api/adcreative/">ad creative</a>. Image formats, sizes and design guidelines depend up on your type of ad, see <a href="https://www.facebook.com/business/ads-guide/">Ads Guide</a>. See <a href="/docs/marketing-api/image-crops/">Image Crop</a> and <a href="https://www.facebook.com/business/ads-guide/">Ads Guide</a>.</p><p>For example, provide an image file such as <code>.bmp</code>, <code>.jpeg</code>, or <code>.gif</code>:</p><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_Cu"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_hi">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_E6">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_Rc">Java Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_kn">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_a_rv" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdImage</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Fields\AdImageFields</span><span class="pun">;</span><span class="pln">

$image </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdImage</span><span class="pun">(</span><span class="kwd">null</span><span class="pun">,</span><span class="pln"> </span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$image</span><span class="pun">-&gt;{</span><span class="typ">AdImageFields</span><span class="pun">::</span><span class="pln">FILENAME</span><span class="pun">}</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="str">'&lt;IMAGE_PATH&gt;'</span><span class="pun">;</span><span class="pln">

$image</span><span class="pun">-&gt;</span><span class="pln">create</span><span class="pun">();</span><span class="pln">
echo </span><span class="str">'Image Hash: '</span><span class="pun">.</span><span class="pln">$image</span><span class="pun">-&gt;{</span><span class="typ">AdImageFields</span><span class="pun">::</span><span class="pln">HASH</span><span class="pun">}.</span><span class="pln">PHP_EOL</span><span class="pun">;</span></code></pre></div></div><p></p><p>Once you have the image hash, you can use in in an ad creative:</p><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_e_53"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_f_Hf">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_g_1R">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_h_9G">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_i_io" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdCreative</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Ad</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Fields\AdCreativeFields</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Fields\AdFields</span><span class="pun">;</span><span class="pln">

</span><span class="com">// First, upload the ad image that you will use in your ad creative</span><span class="pln">
</span><span class="com">// Please refer to Ad Image Create for details.</span><span class="pln">

</span><span class="com">// Then, use the image hash returned from above</span><span class="pln">
$creative </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdCreative</span><span class="pun">(</span><span class="kwd">null</span><span class="pun">,</span><span class="pln"> </span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$creative</span><span class="pun">-&gt;</span><span class="pln">setData</span><span class="pun">(</span><span class="pln">array</span><span class="pun">(</span><span class="pln">
  </span><span class="typ">AdCreativeFields</span><span class="pun">::</span><span class="pln">TITLE </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'My Test Creative'</span><span class="pun">,</span><span class="pln">
  </span><span class="typ">AdCreativeFields</span><span class="pun">::</span><span class="pln">BODY </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'My Test Ad Creative Body'</span><span class="pun">,</span><span class="pln">
  </span><span class="typ">AdCreativeFields</span><span class="pun">::</span><span class="pln">OBJECT_URL </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'https://www.facebook.com/facebook'</span><span class="pun">,</span><span class="pln">
  </span><span class="typ">AdCreativeFields</span><span class="pun">::</span><span class="pln">IMAGE_HASH </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'&lt;IMAGE_HASH&gt;'</span><span class="pun">,</span><span class="pln">
</span><span class="pun">));</span><span class="pln">

</span><span class="com">// Finally, create your ad along with ad creative.</span><span class="pln">
</span><span class="com">// Please note that the ad creative is not created independently, rather its</span><span class="pln">
</span><span class="com">// data structure is appended to the ad group</span><span class="pln">
$ad </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">Ad</span><span class="pun">(</span><span class="kwd">null</span><span class="pun">,</span><span class="pln"> </span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$ad</span><span class="pun">-&gt;</span><span class="pln">setData</span><span class="pun">(</span><span class="pln">array</span><span class="pun">(</span><span class="pln">
  </span><span class="typ">AdFields</span><span class="pun">::</span><span class="pln">NAME </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'My Ad'</span><span class="pun">,</span><span class="pln">
  </span><span class="typ">AdFields</span><span class="pun">::</span><span class="pln">ADSET_ID </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="pun">&lt;</span><span class="pln">AD_SET_ID</span><span class="pun">&gt;,</span><span class="pln">
  </span><span class="typ">AdFields</span><span class="pun">::</span><span class="pln">CREATIVE </span><span class="pun">=&gt;</span><span class="pln"> $creative</span><span class="pun">,</span><span class="pln">
</span><span class="pun">));</span><span class="pln">
$ad</span><span class="pun">-&gt;</span><span class="pln">create</span><span class="pun">(</span><span class="pln">array</span><span class="pun">(</span><span class="pln">
  </span><span class="typ">Ad</span><span class="pun">::</span><span class="pln">STATUS_PARAM_NAME </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="typ">Ad</span><span class="pun">::</span><span class="pln">STATUS_PAUSED</span><span class="pun">,</span><span class="pln">
</span><span class="pun">));</span></code></pre></div></div><p></p>

<h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>Image for use in ad creatives can be uploaded and managed independently of the ad itself. The image used in an ad creative can be specified in the following ways:</p>

<ul><li>By image hash value of a previously uploaded image.</li><li>By uploading the image at ad or ad creative creation time.</li></ul>
</div><div><h3 id="examples_read">Examples</h3><h4 id="example_read_by_adaccount">By Ad Account</h4><p>Get available images for an ad account. Images used in every creative for the account should appear in the list.</p><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_l_Ka"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_m_Ub">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_n_zM">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_o_1t">Java Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_p_Y7">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_q_1H" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdAccount</span><span class="pun">;</span><span class="pln">

$account </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdAccount</span><span class="pun">(</span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$images </span><span class="pun">=</span><span class="pln"> $account</span><span class="pun">-&gt;</span><span class="pln">getAdImages</span><span class="pun">();</span></code></pre></div></div><p></p><h4 id="example_read_by_image">By Image</h4><p>To get specific images, specify hashes of the images in a <code>hashes</code>:</p><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_u_87"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_v_ap">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_w_Hh">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_x_+L">Java Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_y_u3">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_z_79" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdAccount</span><span class="pun">;</span><span class="pln">

$account </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdAccount</span><span class="pun">(</span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$images </span><span class="pun">=</span><span class="pln"> $account</span><span class="pun">-&gt;</span><span class="pln">getAdImages</span><span class="pun">(</span><span class="pln">
  array</span><span class="pun">(),</span><span class="pln">
  array</span><span class="pun">(</span><span class="pln">
    </span><span class="str">'hashes'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> array</span><span class="pun">(</span><span class="pln">
      </span><span class="str">'&lt;IMAGE_1_HASH&gt;'</span><span class="pun">,</span><span class="pln">
      </span><span class="str">'&lt;IMAGE_2_HASH&gt;'</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">),</span><span class="pln">
  </span><span class="pun">));</span></code></pre></div></div><p></p></div><div><h3 id="parameters">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id="fields">Fields</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><code>id</code></span></div><div class="_yb _yc"><span>token with structure: ID</span></div></td><td><p class="_yd"></p><div><div><p>The ID of the image.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>account_id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The ad account that owns the image.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>created_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Time the image was created.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>creatives</code></span></div><div class="_yb _yc"><span>list&lt;numeric string&gt;</span></div></td><td><p class="_yd"></p><div><div><p>A list of ad creative IDs that this ad image is being used in. Not applicable for creatives using <code>object_story_spec</code> and a URL in the <code>picture</code> field.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>hash</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>The hash which uniquely identifies the image.</p>
</div></div><p></p><div class="_2pic"><a href="https://developers.facebook.com/docs/graph-api/using-graph-api/#fields" target="blank"><span class="_1vet">Default</span></a></div></td></tr><tr><td><div class="_yc"><span><code>height</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><div><p>The height of the image.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>is_associated_creatives_in_adgroups</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>SELF_EXPLANATORY</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>name</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>The filename of the image. The maximum length of this string is 100 characters.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>original_height</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><div><p>The height of the image that was originally uploaded.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>original_width</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><div><p>The width of the image that was originally uploaded.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>permalink_url</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>A permanent URL of the image to use in story creatives.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>status</code></span></div><div class="_yb _yc"><span>enum {ACTIVE, INTERNAL, DELETED}</span></div></td><td><p class="_yd"></p><div><div><p>Status of the image.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>updated_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Time the image was updated.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>url</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>A temporary URL which the image can be retrieved at. <strong>Do not use this URL in ad creative creation</strong>.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>url_128</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>A temporary URL pointing to a version of the image resized to fit within a 128x128 pixel box</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>width</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><div><p>The width of the image.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>

<h2 id="Creating">Creating</h2><div><p>Upload an image or zip file, get back a hash, and use the hash in an ad or creative. You must include a filename extension such as <code>sample.jpg</code>, not <code>sample</code> or <code>sample.tmp</code>.</p><h3 id="examples_create">Examples</h3><h4 id="example_create_zip">Zip file</h4><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_13_1I"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_14_px">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_15_Vi">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_16_Qg">Java Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_17_wt">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_18_er" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdImage</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Fields\AdImageFields</span><span class="pun">;</span><span class="pln">

$images </span><span class="pun">=</span><span class="pln"> </span><span class="typ">AdImage</span><span class="pun">::</span><span class="pln">createFromZip</span><span class="pun">(</span><span class="str">'&lt;ZIP_PATH&gt;'</span><span class="pun">,</span><span class="pln"> </span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">

</span><span class="kwd">foreach</span><span class="pln"> </span><span class="pun">(</span><span class="pln">$images </span><span class="kwd">as</span><span class="pln"> $image</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo $image</span><span class="pun">-&gt;{</span><span class="typ">AdImageFields</span><span class="pun">::</span><span class="pln">HASH</span><span class="pun">}.</span><span class="pln">PHP_EOL</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span></code></pre></div></div><p></p><h4 id="example_create_bytes">bytes</h4><p></p><pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'bytes=iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAAOVBMVEX///87WZg7WZg7WZg7WZg7WZg7WZg7WZg7WZg7WZg7WZhMeMJEaa5Xi9tKdb0+Xp5Wi9tXjNxThNH+wk/7AAAACnRSTlMAsHIoaM7g/fx9Zr/g5QAAAGlJREFUeNplkFsOwCAIBPGJrtbX/Q/bqm1qwnxuJrBAE6OVD15pQy/WYePsDiIjp9FGyuC4DK7l6pOrVH4s41D6R4EzpJGXsa0MTQqp/yQo8hhHMuApoB1JQ5COnCN3yT6ys7xL3i7/cwMYsAveYa+MxAAAAABJRU5ErkJggg=='</span><span class="pln">
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/&lt;API_VERSION&gt;/act_&lt;ACCOUNT_ID&gt;/adimages"</span></pre><p></p><h4 id="example_create_ad_with_img_upload">Upload an Image on Create</h4><p>You can upload an image instead of using an image hash when you create an ad or ad creative. Add the image file to the multi-part MIME POST and specify the file name. For example:</p><p></p><pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'campaign_id=&lt;AD_SET_ID&gt;'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'creative={"title":"test title","body":"test","object_url":"http:\/\/www.test.com","image_file":"test.jpg"}'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'test.jpg=@test.jpg'</span><span class="pln">
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'name=My ad'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/&lt;API_VERSION&gt;/act_&lt;ACCOUNT_ID&gt;/ads"</span></pre><p></p><p>The response contains:</p><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>
Name
</th><th>
Description
</th></tr></thead><tbody class="_5m37" id="u_0_1c_zX"><tr class="row_0"><td><p>id</p>
</td><td><p>ID of the ad</p>
</td></tr></tbody></table></div><p><br></p><h4 id="example_create_copy">Copying Images</h4><p>To copy an ad image from one account to another, make a <code>POST</code> request to <code>/act_{DESTINATION_ACCOUNT_ID}/adimages</code>. Provide the source account ID without the <code>act_</code> prefix and a hash of the image in <code>copy_from</code>. This copies the image from the source to the destination account. <b>Your app's user must have access to read the creatives from the source account</b> or you cannot copy images from the account.</p><p></p><pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'copy_from={"source_account_id":"&lt;SOURCE_ACCOUNT_ID&gt;", "hash":"02bee5277ec507b6fd0f9b9ff2f22d9c"}'</span><span class="pln">
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> 
</span><span class="str">"https://graph.facebook.com/&lt;API_VERSION&gt;/act_&lt;DESTINATION_ACCOUNT_ID&gt;/adimages"</span></pre><p></p><p><br></p></div><div class="_844_"><div class="_3-98">You can make a POST request to <code>adimages</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/adimages/"><code>/act_{ad_account_id}/adimages</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href="/docs/marketing-api/reference/ad-image/">AdImage</a> will be created.</div><div><h3 id="parameters-2">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_1d_mj"><tr class="row_0"><td><div class="_yc"><span><code>bytes</code></span></div><div class="_yb">Base64 UTF-8 string</div></td><td><p class="_yd"></p><div><div><p>Image file. Example: <code>bytes = &lt;image content in bytes format&gt;</code></p>
</div></div><p></p></td></tr><tr class="row_1 _5m29 _5m27"><td><div class="_yc"><span><code>copy_from</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div><p>This copies the Ad Image from the source to the destination account.<br><code>{"source_account_id":"&lt;SOURCE_ACCOUNT_ID&gt;"</code>, <code>"hash":"02bee5277ec507b6fd0f9b9ff2f22d9c"}</code></p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>images</code> in the return type.</div><div class="_367u"> Map  {<div class="_uoj">string:  Map  {<div class="_uoj">string:  Struct  {<div class="_uoj"><code>hash</code>: string, </div><div class="_uoj"><code>url</code>: string, </div><div class="_uoj"><code>url_128</code>: string, </div><div class="_uoj"><code>url_256</code>: string, </div><div class="_uoj"><code>url_256_height</code>: string, </div><div class="_uoj"><code>url_256_width</code>: string, </div><div class="_uoj"><code>height</code>: int32, </div><div class="_uoj"><code>width</code>: int32, </div><div class="_uoj"><code>name</code>: string, </div>}</div>}</div>}</div><h3 id="error-codes-2">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr></tbody></table></div></div></div>

<h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Deleting">Deleting</h2><div><p>You can only delete ad images <b>not currently being used</b> in an ad creative.</p><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_1e_Oo"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_1f_d/">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_1g_uy">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_1h_g4">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_1i_2A" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdImage</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Fields\AdImageFields</span><span class="pun">;</span><span class="pln">

$image </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdImage</span><span class="pun">(&lt;</span><span class="pln">IMAGE_ID</span><span class="pun">&gt;,</span><span class="pln"> </span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$image</span><span class="pun">-&gt;{</span><span class="typ">AdImageFields</span><span class="pun">::</span><span class="pln">HASH</span><span class="pun">}</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="str">'&lt;IMAGE_HASH&gt;'</span><span class="pun">;</span><span class="pln">
$image</span><span class="pun">-&gt;</span><span class="pln">deleteSelf</span><span class="pun">();</span></code></pre></div></div><p></p></div><div class="_844_"><div class="_3-98">You can dissociate an&nbsp;<a href="/docs/marketing-api/reference/ad-image/">AdImage</a> from an&nbsp;<a href="/docs/marketing-api/reference/ad-account/">AdAccount</a> by making a DELETE request to <a href="/docs/marketing-api/reference/ad-account/adimages/"><code>/act_{ad_account_id}/adimages</code></a>.<div><h3 id="parameters-3">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_1l_QA"><tr class="row_0"><td><div class="_yc"><span><code>hash</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Hash of the image you wish to delete.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3 id="return-type-2">Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3 id="error-codes-3">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr></tbody></table></div></div></div>