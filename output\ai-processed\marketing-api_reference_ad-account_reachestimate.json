{"title": "Facebook Marketing API - Ad Account Reach Estimate", "summary": "This endpoint provides audience size estimation based on targeting specifications for Facebook ad accounts. It returns reach estimates in the form of upper and lower bounds for the potential audience size.", "content": "# Ad Account Reach Estimate\n\n## Overview\n\nThe Ad Account Reach Estimate endpoint is used to get audience size estimation based on a targeting specification using a specific ad account. This endpoint returns a range-based size estimation in the form of two fields: `users_lower_bound` and `users_upper_bound`.\n\n## Limitations\n\n- Reach estimates for custom audiences may not be available for certain businesses\n- Rate limiting applies to this endpoint\n\n## Endpoint\n\n```\nGET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate\n```\n\n## Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `object_store_url` | string | Used in mobile app campaigns. The URL of the app in the app store. | No |\n| `targeting_spec` | Targeting object | The targeting structure for reach estimate. `countries` is required. See [targeting specs documentation](/docs/marketing-api/targeting-specs). | Yes |\n\n## Response Format\n\nThe response returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n### Response Fields\n\n- **data**: A single [AdAccountReachEstimate](/docs/graph-api/reference/ad-account-reach-estimate/) node\n- **paging**: Pagination information (see [Graph API guide](/docs/graph-api/using-graph-api/#paging))\n\n## Example Request\n\n```http\nGET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate?targeting_spec=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22age_min%22%3A20%2C%22age_max%22%3A40%7D HTTP/1.1\nHost: graph.facebook.com\n```\n\n## Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 80004 | Too many calls to this ad-account. Wait and try again. |\n| 613 | API calls have exceeded the rate limit |\n| 200 | Permissions error |\n| 2641 | Ad includes or excludes locations that are currently restricted |\n| 2635 | Calling a deprecated version of the Ads API |\n| 190 | Invalid OAuth 2.0 Access Token |\n\n## Supported Operations\n\n- **Reading**: ✅ Supported\n- **Creating**: ❌ Not supported\n- **Updating**: ❌ Not supported\n- **Deleting**: ❌ Not supported", "keyPoints": ["Returns audience size estimates as upper and lower bounds for targeting specifications", "Requires targeting_spec parameter with countries field as mandatory", "Subject to rate limiting and may not work for all custom audiences", "Only supports GET operations - no create, update, or delete functionality", "Useful for mobile app campaigns with object_store_url parameter"], "apiEndpoints": ["GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate"], "parameters": ["targeting_spec", "object_store_url", "users_lower_bound", "users_upper_bound"], "examples": ["GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate?targeting_spec=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22age_min%22%3A20%2C%22age_max%22%3A40%7D"], "tags": ["Facebook Marketing API", "Reach Estimation", "Audience Targeting", "Ad Account", "Graph API", "v23.0"], "relatedTopics": ["Targeting Specifications", "AdAccountReachEstimate", "Graph API Pagination", "Rate Limiting", "Mobile App Campaigns", "Custom Audiences"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/reachestimate/", "processedAt": "2025-06-25T15:38:15.371Z", "processor": "openrouter-claude-sonnet-4"}