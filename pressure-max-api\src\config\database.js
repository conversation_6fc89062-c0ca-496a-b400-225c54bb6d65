const knex = require('knex');
const config = require('./config');
const logger = require('./logger');

class Database {
  constructor() {
    this.knex = null;
  }

  async initialize() {
    try {
      const knexConfig = {
        client: 'postgresql',
        connection: config.database.url || {
          host: config.database.host,
          port: config.database.port,
          database: config.database.name,
          user: config.database.user,
          password: config.database.password
        },
        pool: config.database.pool,
        migrations: {
          directory: './src/migrations',
          tableName: 'knex_migrations'
        },
        seeds: {
          directory: './src/seeds'
        }
      };

      this.knex = knex(knexConfig);

      // Test the connection
      await this.knex.raw('SELECT 1');
      
      logger.info('Database connection established successfully');
      return this.knex;
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  getKnex() {
    if (!this.knex) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.knex;
  }

  async close() {
    if (this.knex) {
      await this.knex.destroy();
      logger.info('Database connection closed');
    }
  }

  // Transaction helper
  async transaction(callback) {
    return this.knex.transaction(callback);
  }

  // Health check
  async healthCheck() {
    try {
      await this.knex.raw('SELECT 1');
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error.message, 
        timestamp: new Date().toISOString() 
      };
    }
  }
}

const database = new Database();

module.exports = database;
