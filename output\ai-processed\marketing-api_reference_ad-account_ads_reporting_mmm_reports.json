{"title": "Facebook Marketing API - Ad Account Ads Reporting MMM Reports", "summary": "This endpoint documentation covers the Ad Account Ads Reporting MMM Reports resource in the Facebook Marketing API. Currently, all CRUD operations (Reading, Creating, Updating, Deleting) are not supported on this endpoint.", "content": "# Ad Account Ads Reporting MMM Reports\n\n**Graph API Version:** v23.0\n\nThis endpoint is part of the Facebook Marketing API's Ad Account reporting functionality, specifically for MMM (Media Mix Modeling) reports.\n\n## Operations\n\n### Reading\nYou can't perform this operation on this endpoint.\n\n### Creating\nYou can't perform this operation on this endpoint.\n\n### Updating\nYou can't perform this operation on this endpoint.\n\n### Deleting\nYou can't perform this operation on this endpoint.\n\n## Status\nThis endpoint currently does not support any CRUD operations. All standard operations (GET, POST, PUT, DELETE) are disabled for this resource.", "keyPoints": ["All CRUD operations are currently disabled for this endpoint", "This is part of the Ad Account reporting functionality for MMM reports", "Uses Graph API version v23.0", "No reading, creating, updating, or deleting operations are supported"], "apiEndpoints": ["/ad-account/ads_reporting_mmm_reports"], "parameters": [], "examples": [], "tags": ["Facebook Marketing API", "Ad Account", "Reporting", "MMM Reports", "Media Mix Modeling", "Graph API"], "relatedTopics": ["Ad Account", "Ads Reporting", "Media Mix Modeling", "Facebook Marketing API", "Graph API v23.0"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "processedAt": "2025-06-25T15:17:25.035Z", "processor": "openrouter-claude-sonnet-4"}