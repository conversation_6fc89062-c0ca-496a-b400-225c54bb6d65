<h1 id="catalog">Catalog</h1>

<p>A Facebook catalog is an object (or container) of information about your products and where you can upload your inventory. Learn more about <a href="/docs/marketing-api/catalog/overview">product catalog</a>.</p>


<h2 id="common-uses">Common Uses</h2>

<ul>
<li><strong><a href="https://developers.facebook.com/docs/marketing-api/guides/collection">Collection Ads</a></strong> — Use them in immersive formats. </li>
<li><strong><a href="https://developers.facebook.com/docs/commerce-platform/catalog/">Commerce</a></strong> — Distribute products in Marketplace.    </li>
<li><strong><a href="https://developers.facebook.com/docs/marketing-api/dynamic-ads">Advantage+ Catalog Ads</a></strong> — Feature products in different formats to be served dynamically as personalized ads. </li>
<li><strong>Instagram Shopping</strong> — Feature in Instagram Shopping experiences, such as product tags on Instagram and soon on Instagram Shops.</li>
<li><strong>WhatsApp</strong> — Feature in conversational commerce in WhatsApp. </li>
</ul>


<h2 id="doc_contents">Documentation Contents</h2>
<table class="uiGrid _51mz _57v1 _5f0n" cellspacing="0" cellpadding="0"><tbody><tr class="_51mx"><td class="_51m- vTop hLeft _57v2 _2cs2"><h3 id="overview"><a href="/docs/marketing-api/catalog/overview">Overview</a></h3>

<p>Learn more about catalog and its components.</p>
</td><td class="_51m- vTop hLeft _57v2 _2cs2 _51mw"><h3 id="get-started"><a href="/docs/marketing-api/catalog/getting-started">Get Started</a></h3>

<p>Learn how to successfully set up a catalog for commerce or Advantage+ catalog ads, and more.</p>
</td></tr><tr class="_51mx"><td class="_51m- vTop hLeft _57v2 _2cs2"><h3 id="guides"><a href="/docs/marketing-api/catalog/guides">Guides</a></h3>

<p>Learn more about the various guides and how to use them in your catalog.</p>
</td><td class="_51m- vTop hLeft _57v2 _2cs2 _51mw"><h3 id="best-practices"><a href="https://developers.facebook.com/docs/marketing-api/catalog/best-practices">Best Practices</a></h3>

<p>Tips for using catalog effectively.</p>
</td></tr><tr class="_51mx"><td class="_51m- vTop hLeft _57v2 _2cs2"><h3 id="reference"><a href="/docs/marketing-api/catalog/reference">Reference</a></h3>

<p>Product specifications and endpoint references.</p>
</td><td class="_51m- vTop hLeft _57v2 _2cs2 _51mw"><h3 id="support"><a href="/docs/marketing-api/catalog/support">Support</a></h3>

<p>Solutions to common problems and troubleshooting tips.</p>
</td></tr></tbody></table>

<h2 id="see-also">See Also</h2>

<ul>
<li><a href="/docs/marketing-api/catalog-batch">Catalog Batch API</a></li>
</ul>


