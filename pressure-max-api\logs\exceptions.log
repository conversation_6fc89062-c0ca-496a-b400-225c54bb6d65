{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:05 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 29816,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119455744,
      heapTotal: 76582912,
      heapUsed: 50665248,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127540.234 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:05'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:26 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 23840,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119627776,
      heapTotal: 76582912,
      heapUsed: 50472040,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127561.843 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:26'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:44 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 21416,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119537664,
      heapTotal: 76320768,
      heapUsed: 49878536,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127580.078 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:45'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:12:55 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 24896,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118714368,
      heapTotal: 75534336,
      heapUsed: 50986064,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127590.953 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:12:55'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:13:08 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30112,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119099392,
      heapTotal: 75796480,
      heapUsed: 50059304,
      external: 2332419,
      arrayBuffers: 16795
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127603.609 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:13:08'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:13:30 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 9176,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120348672,
      heapTotal: 77107200,
      heapUsed: 49906280,
      external: 2307206,
      arrayBuffers: 16620
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127625.734 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:13:30'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:15:07 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 16988,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 119246848,
      heapTotal: 76058624,
      heapUsed: 50918584,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 127722.593 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:15:07'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:20:53 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 30772,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 118685696,
      heapTotal: 75796480,
      heapUsed: 50326840,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128068.25 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:20:53'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:22:04 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 27112,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121147392,
      heapTotal: 76582912,
      heapUsed: 50542264,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128139.671 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:22:04'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:22:22 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 19796,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 120819712,
      heapTotal: 76058624,
      heapUsed: 50878808,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128157.375 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:22:22'
}
{
  error: TypeError: nodemailer.createTransporter is not a function
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\routes\auth.js:29:37)
      at Module._compile (node:internal/modules/cjs/loader:1562:14)
      at Object..js (node:internal/modules/cjs/loader:1699:10)
      at Module.load (node:internal/modules/cjs/loader:1313:32)
      at Function._load (node:internal/modules/cjs/loader:1123:12)
      at TracingChannel.traceSync (node:diagnostics_channel:322:14)
      at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
      at Module.require (node:internal/modules/cjs/loader:1335:12)
      at require (node:internal/modules/helpers:136:16)
      at Object.<anonymous> (C:\Users\<USER>\Desktop\FB API SCRAPER\pressure-max-api\src\server.js:21:20),
  level: 'error',
  message: 'uncaughtException: nodemailer.createTransporter is not a function\n' +
    'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  stack: 'TypeError: nodemailer.createTransporter is not a function\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js:29:37)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n' +
    '    at Object..js (node:internal/modules/cjs/loader:1699:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1313:32)\n' +
    '    at Function._load (node:internal/modules/cjs/loader:1123:12)\n' +
    '    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n' +
    '    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1335:12)\n' +
    '    at require (node:internal/modules/helpers:136:16)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:21:20)',
  exception: true,
  date: 'Wed Jun 25 2025 10:22:36 GMT-0700 (Mountain Standard Time)',
  process: {
    pid: 1384,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.13.1',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js'
    ],
    memoryUsage: {
      rss: 121303040,
      heapTotal: 76320768,
      heapUsed: 50560136,
      external: 2332419,
      arrayBuffers: 16797
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 128171.546 },
  trace: [
    {
      column: 37,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\auth.js',
      function: null,
      line: 29,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1562,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Object..js',
      line: 1699,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1313,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Function._load',
      line: 1123,
      method: '_load',
      native: false
    },
    {
      column: 14,
      file: 'node:diagnostics_channel',
      function: 'TracingChannel.traceSync',
      line: 322,
      method: 'traceSync',
      native: false
    },
    {
      column: 24,
      file: 'node:internal/modules/cjs/loader',
      function: 'wrapModuleLoad',
      line: 217,
      method: null,
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1335,
      method: 'require',
      native: false
    },
    {
      column: 16,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 136,
      method: null,
      native: false
    },
    {
      column: 20,
      file: 'C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js',
      function: null,
      line: 21,
      method: null,
      native: false
    }
  ],
  service: 'pressure-max-api',
  environment: 'development',
  timestamp: '2025-06-25 10:22:36'
}
