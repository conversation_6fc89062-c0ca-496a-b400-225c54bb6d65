# Facebook Marketing API - Ad Account Product Audiences

## Summary
Documentation for creating product audiences within Facebook ad accounts using the Marketing API. Product audiences allow advertisers to target users based on their interactions with specific products, including events like AddToCart and Purchase with customizable inclusion and exclusion rules.

## Key Points
- Product audiences target users based on specific product interactions and events
- Only creation operations are supported - reading, updating, and deleting are not available
- Requires product_set_id, name, and event_sources as mandatory parameters
- Supports complex inclusion and exclusion rules with retention periods
- Returns audience ID and message upon successful creation

## API Endpoints
- `/act_{ad_account_id}/product_audiences`

## Parameters
- name
- product_set_id
- event_sources
- inclusions
- exclusions
- retention_seconds
- rule
- associated_audience_id
- creation_params
- description
- enable_fetch_or_create
- opt_out_link
- parent_audience_id
- subtype

## Content
# Ad Account Product Audiences

## Overview

Product audiences are custom audiences created based on user interactions with specific products in your catalog. This endpoint allows you to create targeted audiences using product-specific events and rules.

## Supported Operations

### Reading
Reading operations are not supported on this endpoint.

### Creating

You can create product audiences by making a POST request to the `product_audiences` edge:

**Endpoint:** `/act_{ad_account_id}/product_audiences`

#### Example Request

```http
POST /v23.0/act_<AD_ACCOUNT_ID>/product_audiences HTTP/1.1
Host: graph.facebook.com

name=Test+Iphone+Product+Audience&product_set_id=<PRODUCT_SET_ID>&inclusions=[{"retention_seconds":86400,"rule":{"and":[{"event":{"eq":"AddToCart"}},{"userAgent":{"i_contains":"iPhone"}}]}}]&exclusions=[{"retention_seconds":172800,"rule":{"event":{"eq":"Purchase"}}}]
```

#### PHP SDK Example

```php
$response = $fb->post(
  '/act_<AD_ACCOUNT_ID>/product_audiences',
  array (
    'name' => 'Test Iphone Product Audience',
    'product_set_id' => '<PRODUCT_SET_ID>',
    'inclusions' => '[{"retention_seconds":86400,"rule":{"and":[{"event":{"eq":"AddToCart"}},{"userAgent":{"i_contains":"iPhone"}}]}}]',
    'exclusions' => '[{"retention_seconds":172800,"rule":{"event":{"eq":"Purchase"}}}]',
  ),
  '{access-token}'
);
```

#### cURL Example

```bash
curl -X POST \
  -F 'name="Test Iphone Product Audience"' \
  -F 'product_set_id="<PRODUCT_SET_ID>"' \
  -F 'inclusions=[
       {
         "retention_seconds": 86400,
         "rule": {
           "and": [
             {
               "event": {
                 "eq": "AddToCart"
               }
             },
             {
               "userAgent": {
                 "i_contains": "iPhone"
               }
             }
           ]
         }
       }
     ]' \
  -F 'exclusions=[
       {
         "retention_seconds": 172800,
         "rule": {
           "event": {
             "eq": "Purchase"
           }
         }
       }
     ]' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/product_audiences
```

### Parameters

#### Required Parameters
- `name` (string): Name for the product audience
- `product_set_id` (numeric string or integer): ID of the product set
- `event_sources` (array): Event sources configuration with required `id` and `type`

#### Optional Parameters
- `associated_audience_id` (int64): Associated audience identifier
- `creation_params` (dictionary): Additional creation parameters
- `description` (string): Audience description
- `enable_fetch_or_create` (boolean): Enable fetch or create functionality
- `inclusions` (list): Rules for including users in the audience
- `exclusions` (list): Rules for excluding users from the audience
- `opt_out_link` (string): Opt-out link for the audience
- `parent_audience_id` (int64): Parent audience identifier
- `subtype` (enum): Audience subtype

#### Inclusion/Exclusion Rule Structure

Both `inclusions` and `exclusions` parameters accept objects with:
- `retention_seconds` (integer): How long to retain users (required for retention object)
- `rule` (object): The targeting rule
- `booking_window` (object): Time window configuration
- `count` (object): Count-based rules
- `pixel_id` (int64): Associated pixel ID

### Return Type

The endpoint returns a struct containing:
- `id` (numeric string): The created audience ID
- `message` (string): Response message

### Error Codes

- **100**: Invalid parameter

### Updating and Deleting

Update and delete operations are not supported on this endpoint.

## Event Source Types

Supported event source types include:
- `APP`: Mobile app events
- `OFFLINE_EVENTS`: Offline conversion events
- `PAGE`: Facebook page events
- `PIXEL`: Facebook pixel events

## Audience Subtypes

Available audience subtypes include: CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST

## Examples
POST request to create iPhone product audience with AddToCart inclusion and Purchase exclusion

PHP SDK implementation with retention rules

cURL command with complex rule structure

JavaScript SDK usage pattern

---
**Tags:** Facebook Marketing API, Product Audiences, Ad Account, Custom Audiences, Targeting, E-commerce, Product Catalog  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/product_audiences/  
**Processed:** 2025-06-25T16:29:06.882Z