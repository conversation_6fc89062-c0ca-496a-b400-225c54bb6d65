# Facebook Marketing API - Ad Account Product Audiences

## Summary
Documentation for creating product audiences within Facebook ad accounts using the Marketing API. Product audiences allow advertisers to target users based on their interactions with specific products, including events like AddToCart and Purchase with customizable inclusion and exclusion rules.

## Key Points
- Product audiences target users based on specific product interactions and events
- Only POST (create) operations are supported - no read, update, or delete operations
- Requires product_set_id, name, and event_sources as mandatory parameters
- Supports complex inclusion and exclusion rules with retention periods
- Returns audience ID and message upon successful creation

## API Endpoints
- `/act_{ad_account_id}/product_audiences`

## Parameters
- name
- product_set_id
- event_sources
- inclusions
- exclusions
- retention_seconds
- rule
- associated_audience_id
- creation_params
- description
- enable_fetch_or_create
- opt_out_link
- parent_audience_id
- subtype

## Content
# Ad Account Product Audiences

## Overview

Product audiences are custom audiences created based on user interactions with specific products in your catalog. This endpoint allows you to create targeted audiences using product-specific events and user behavior data.

## Supported Operations

### Reading
Reading operations are not supported on this endpoint.

### Creating

You can create product audiences by making a POST request to the `product_audiences` edge:

**Endpoint:** `/act_{ad_account_id}/product_audiences`

When posting to this edge, an AdAccount will be created.

#### Example Request

```http
POST /v23.0/act_<AD_ACCOUNT_ID>/product_audiences HTTP/1.1
Host: graph.facebook.com

name=Test+Iphone+Product+Audience&product_set_id=<PRODUCT_SET_ID>&inclusions=[{"retention_seconds":86400,"rule":{"and":[{"event":{"eq":"AddToCart"}},{"userAgent":{"i_contains":"iPhone"}}]}}]&exclusions=[{"retention_seconds":172800,"rule":{"event":{"eq":"Purchase"}}}]
```

#### PHP SDK Example

```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->post(
    '/act_<AD_ACCOUNT_ID>/product_audiences',
    array (
      'name' => 'Test Iphone Product Audience',
      'product_set_id' => '<PRODUCT_SET_ID>',
      'inclusions' => '[{"retention_seconds":86400,"rule":{"and":[{"event":{"eq":"AddToCart"}},{"userAgent":{"i_contains":"iPhone"}}]}}]',
      'exclusions' => '[{"retention_seconds":172800,"rule":{"event":{"eq":"Purchase"}}}]',
    ),
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### cURL Example

```bash
curl -X POST \
  -F 'name="Test Iphone Product Audience"' \
  -F 'product_set_id="<PRODUCT_SET_ID>"' \
  -F 'inclusions=[
       {
         "retention_seconds": 86400,
         "rule": {
           "and": [
             {
               "event": {
                 "eq": "AddToCart"
               }
             },
             {
               "userAgent": {
                 "i_contains": "iPhone"
               }
             }
           ]
         }
       }
     ]' \
  -F 'exclusions=[
       {
         "retention_seconds": 172800,
         "rule": {
           "event": {
             "eq": "Purchase"
           }
         }
       }
     ]' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/product_audiences
```

### Parameters

#### Required Parameters
- `name` (string): Name for the product audience
- `product_set_id` (numeric string or integer): ID of the product set
- `event_sources` (array): Event sources configuration with required `id` and `type`

#### Optional Parameters
- `associated_audience_id` (int64): Associated audience identifier
- `creation_params` (dictionary): Additional creation parameters
- `description` (string): Audience description
- `enable_fetch_or_create` (boolean): Enable fetch or create functionality
- `inclusions` (list): Rules for including users in the audience
- `exclusions` (list): Rules for excluding users from the audience
- `opt_out_link` (string): Opt-out link for the audience
- `parent_audience_id` (int64): Parent audience identifier
- `subtype` (enum): Audience subtype

#### Inclusion/Exclusion Rule Structure

Both `inclusions` and `exclusions` parameters accept objects with:
- `retention_seconds` (integer): How long to retain users based on the rule
- `rule` (object): The targeting rule definition
- `booking_window` (object): Time window configuration
- `count` (object): Event count requirements

### Return Type

This endpoint supports read-after-write and returns:

```json
{
  "id": "numeric string",
  "message": "string"
}
```

### Error Codes

- **100**: Invalid parameter

### Updating
Updating operations are not supported on this endpoint.

### Deleting
Deleting operations are not supported on this endpoint.

## Examples
HTTP POST request with URL-encoded parameters

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK Bundle parameters

iOS SDK NSDictionary parameters

cURL command with form data

JSON rule structure for inclusions and exclusions

---
**Tags:** Facebook Marketing API, Product Audiences, Ad Account, Custom Audiences, Targeting, E-commerce, Product Catalog
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/product_audiences/
**Processed:** 2025-06-25T15:32:13.217Z