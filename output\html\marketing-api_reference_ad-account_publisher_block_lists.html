<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_er"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_9p"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_TP"></div></span></div></div>

<h1 id="overview">Ad Account Publisher Block Lists</h1>

<h2 id="Reading">Reading</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Creating">Creating</h2><div class="_844_"><div class="_3-98">You can make a POST request to <code>publisher_block_lists</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/publisher_block_lists/"><code>/act_{ad_account_id}/publisher_block_lists</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/publisher-block-list/">PublisherBlockList</a> will be created.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_5_Kr"><tr class="row_0"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Name of the block list</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div>}</div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div></div>

<h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div>