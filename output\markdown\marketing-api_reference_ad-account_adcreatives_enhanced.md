# Facebook Marketing API - Ad Account Ad Creatives Reference

## Summary
Complete reference for managing ad creatives within Facebook ad accounts, including reading existing creatives, creating new ones with various media types and configurations, and understanding the available parameters and error handling.

## Key Points
- Ad creatives contain all media and content elements used in Facebook ads
- Reading creatives returns paginated results with data, paging, and summary information
- Creating creatives supports various media types including images, videos, and interactive components
- Asset feed specifications enable Dynamic Creative optimization with multiple variations
- Platform customizations allow different media for different Facebook placements

## API Endpoints
- `GET /act_{ad_account_id}/adcreatives`
- `POST /act_{ad_account_id}/adcreatives`

## Parameters
- name
- actor_id
- body
- title
- image_file
- image_hash
- image_url
- image_crops
- asset_feed_spec
- call_to_action
- platform_customizations
- object_story_id
- object_story_spec
- degrees_of_freedom_spec
- branded_content
- interactive_components_spec

## Content
# Ad Account Ad Creatives

The Ad Creatives endpoint manages creative content for an ad account that can be used in ads, including images, videos, and other media. Using an ad account creative for a particular ad is subject to validation rules based on the ad type and other requirements.

## Reading Ad Creatives

To retrieve an account's ad creatives, make an HTTP GET call to `/act_{ad_account_id}/adcreatives`.

### Example Request

```bash
curl -G \
  -d 'fields=name' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Response Format

Reading from this edge returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Response Fields

- **data**: A list of AdCreative nodes
- **paging**: Pagination information for navigating through results
- **summary**: Aggregated information about the edge, such as counts

### Summary Fields

| Field | Type | Description |
|-------|------|-------------|
| `total_count` | unsigned int32 | Total number of creatives in the ad account |

### Reading Error Codes

| Error | Description |
|-------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |
| 80004 | Too many calls to this ad-account. Rate limiting applied |
| 190 | Invalid OAuth 2.0 Access Token |
| 2500 | Error parsing graph query |

## Creating Ad Creatives

You can create new ad creatives by making a POST request to `/act_{ad_account_id}/adcreatives`.

### Limitations

- When creating ad creatives, if the `object_story_id` being used is already in use by an existing creative, the API will return the existing creative_id instead of creating a new one
- Using `radius` can cause errors when targeting multiple locations

### Example Creation Request

```bash
curl -X POST \
  -F 'name="Sample Promoted Post"' \
  -F 'object_story_id="<PAGE_ID>_<POST_ID>"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Key Parameters

#### Basic Creative Information

- **name** (string): Name of the ad creative as seen in the ad account's library
- **actor_id** (int64): The actor ID (Page ID) of this creative
- **body** (string): The body text of the ad (supports emoji)
- **title** (string): Title for the creative

#### Media Specifications

- **image_file** (string): Reference to a local image file (max 8MB)
- **image_hash** (string): Hash for an uploaded image
- **image_url** (URL): URL for the image in the creative
- **image_crops** (object): Crop dimensions for the image

#### Asset Feed Specification

```json
{
  "images": [
    {
      "hash": "string",
      "url": "URL",
      "image_crops": {},
      "url_tags": "string"
    }
  ],
  "videos": [
    {
      "video_id": "int64",
      "thumbnail_id": "int64",
      "thumbnail_url": "URL"
    }
  ],
  "bodies": [
    {
      "text": "string",
      "url_tags": "string"
    }
  ],
  "titles": [
    {
      "text": "string",
      "url_tags": "string"
    }
  ]
}
```

#### Call to Action Configuration

```json
{
  "type": "LEARN_MORE",
  "value": {
    "link": "https://example.com",
    "link_title": "Learn More",
    "link_description": "Click to learn more"
  }
}
```

#### Platform Customizations

Specify different media for different Facebook placements:

```json
{
  "instagram": {
    "image_url": "URL",
    "image_hash": "string",
    "image_crops": {}
  }
}
```

### Advanced Features

#### Dynamic Creative Optimization

- **degrees_of_freedom_spec**: Specifies transformation types enabled for the creative
- **creative_features_spec**: Advanced creative features like product metadata automation

#### Branded Content

```json
{
  "partners": [
    {
      "fb_page_id": "numeric_string",
      "ig_user_id": "numeric_string"
    }
  ]
}
```

#### Interactive Components

```json
{
  "child_attachments": [
    {
      "components": [
        {
          "poll_spec": {
            "option_a_text": "Option A",
            "option_b_text": "Option B",
            "question_text": "Your question?"
          }
        }
      ]
    }
  ]
}
```

### Return Type

Successful creation returns:

```json
{
  "id": "numeric_string",
  "success": true
}
```

### Creation Error Codes

| Error | Description |
|-------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |
| 500 | Message contains banned content |
| 1500 | Invalid URL supplied |
| 80004 | Rate limiting applied |
| 105 | Too many parameters |
| 368 | Action deemed abusive or disallowed |
| 194 | Missing required parameter |
| 2635 | Deprecated API version |

## Updating and Deleting

Both updating and deleting operations are not supported on this endpoint. Ad creatives are typically immutable once created.

## Best Practices

1. **Image Requirements**: Ensure images don't exceed 8MB and use appropriate dimensions
2. **Content Validation**: Follow Facebook's advertising policies to avoid content rejection
3. **Rate Limiting**: Be mindful of API rate limits, especially for bulk operations
4. **Asset Management**: Use the asset feed specification for Dynamic Creative campaigns
5. **Platform Optimization**: Use platform customizations for placement-specific media

## Examples
curl -G -d 'fields=name' -d 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives

curl -X POST -F 'name="Sample Promoted Post"' -F 'object_story_id="<PAGE_ID>_<POST_ID>"' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives

Asset feed specification with images, videos, bodies, and titles arrays

Call to action configuration with type and value objects

Platform customizations for Instagram-specific media

---
**Tags:** Facebook Marketing API, Ad Creatives, Ad Account, Media Management, Dynamic Creative, Asset Feed, Platform Customization
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/
**Processed:** 2025-06-25T15:13:55.119Z