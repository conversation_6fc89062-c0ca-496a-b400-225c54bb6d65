{"title": "Facebook Marketing API - Ad Account Ad Place Page Sets", "summary": "This endpoint manages Ad Place Page Sets for Facebook ad accounts, allowing creation of location-based page sets for targeting users based on their location types. The endpoint only supports creation operations, with reading, updating, and deleting operations not available.", "content": "# Ad Account Ad Place Page Sets\n\nThis endpoint applies to published Pages and manages Ad Place Page Sets for Facebook ad accounts.\n\n## Supported Operations\n\n### Reading\nReading operations are not supported on this endpoint.\n\n### Creating\nYou can create Ad Place Page Sets by making a POST request to the `ad_place_page_sets` edge.\n\n**Endpoint Path:**\n- `/act_{ad_account_id}/ad_place_page_sets`\n\nWhen posting to this edge, an [AdPlacePageSet](https://developers.facebook.com/docs/marketing-api/reference/ad-place-page-set/) will be created.\n\n#### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `location_types` | list<enum {recent, home}> | Type of user location the page set targets (e.g., 'recent', 'home') | No |\n| `name` | string | Name of The Place PageSet | Yes |\n| `parent_page` | numeric string or integer | The parent page ID for all the locations pages | Yes |\n\n#### Return Type\nThis endpoint supports read-after-write and will read the node represented by `id` in the return type.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 100 | Invalid parameter |\n\n### Updating\nUpdating operations are not supported on this endpoint.\n\n### Deleting\nDeleting operations are not supported on this endpoint.", "keyPoints": ["Only creation (POST) operations are supported - reading, updating, and deleting are not available", "Creates AdPlacePageSet objects for location-based targeting", "Requires a parent page ID and name, with optional location types", "Supports read-after-write functionality", "Applies specifically to published Pages"], "apiEndpoints": ["/act_{ad_account_id}/ad_place_page_sets"], "parameters": ["location_types (list<enum>)", "name (string, required)", "parent_page (numeric string/integer, required)"], "examples": [], "tags": ["Facebook Marketing API", "Ad Account", "Place Page Sets", "Location Targeting", "Graph API"], "relatedTopics": ["AdPlacePageSet", "Ad Account", "Location Targeting", "Published Pages", "Graph API v23.0"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "processedAt": "2025-06-25T16:19:33.784Z", "processor": "openrouter-claude-sonnet-4"}