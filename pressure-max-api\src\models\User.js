const database = require('../config/database');
const logger = require('../config/logger');

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - email
 *         - password
 *         - firstName
 *         - lastName
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the user
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         role:
 *           type: string
 *           enum: [admin, user, manager]
 *           description: User's role in the system
 *         isActive:
 *           type: boolean
 *           description: Whether the user account is active
 *         tenantId:
 *           type: string
 *           format: uuid
 *           description: ID of the tenant/organization the user belongs to
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *           description: List of permissions granted to the user
 *         lastLoginAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp of the user's last login
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was last updated
 */

class User {
  constructor(data = {}) {
    this.id = data.id;
    this.email = data.email;
    this.password = data.password;
    this.firstName = data.firstName || data.first_name;
    this.lastName = data.lastName || data.last_name;
    this.role = data.role || 'user';
    this.isActive = data.isActive !== undefined ? data.isActive : data.is_active !== undefined ? data.is_active : true;
    this.tenantId = data.tenantId || data.tenant_id;
    this.permissions = data.permissions || [];
    this.profileImage = data.profileImage || data.profile_image;
    this.phone = data.phone;
    this.timezone = data.timezone || 'UTC';
    this.lastLoginAt = data.lastLoginAt || data.last_login_at;
    this.emailVerifiedAt = data.emailVerifiedAt || data.email_verified_at;
    this.createdAt = data.createdAt || data.created_at;
    this.updatedAt = data.updatedAt || data.updated_at;
  }

  static get tableName() {
    return 'users';
  }

  static get knex() {
    return database.getKnex();
  }

  // Create a new user
  static async create(userData) {
    try {
      const [user] = await this.knex(this.tableName)
        .insert({
          email: userData.email,
          password: userData.password,
          first_name: userData.firstName,
          last_name: userData.lastName,
          role: userData.role || 'user',
          tenant_id: userData.tenantId,
          permissions: JSON.stringify(userData.permissions || []),
          phone: userData.phone,
          timezone: userData.timezone || 'UTC',
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning('*');

      logger.audit('user_created', user.id, { email: user.email });
      return new User(user);
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    try {
      const user = await this.knex(this.tableName)
        .where('id', id)
        .first();

      return user ? new User(user) : null;
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    try {
      const user = await this.knex(this.tableName)
        .where('email', email.toLowerCase())
        .first();

      return user ? new User(user) : null;
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  // Find users by tenant
  static async findByTenant(tenantId, options = {}) {
    try {
      const { page = 1, limit = 20, search, role, isActive } = options;
      const offset = (page - 1) * limit;

      let query = this.knex(this.tableName)
        .where('tenant_id', tenantId);

      // Apply filters
      if (search) {
        query = query.where(function() {
          this.where('first_name', 'ilike', `%${search}%`)
            .orWhere('last_name', 'ilike', `%${search}%`)
            .orWhere('email', 'ilike', `%${search}%`);
        });
      }

      if (role) {
        query = query.where('role', role);
      }

      if (isActive !== undefined) {
        query = query.where('is_active', isActive);
      }

      // Get total count
      const [{ count }] = await query.clone().count('* as count');

      // Get users
      const users = await query
        .orderBy('created_at', 'desc')
        .limit(limit)
        .offset(offset);

      return {
        users: users.map(user => new User(user)),
        pagination: {
          page,
          limit,
          total: parseInt(count),
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error finding users by tenant:', error);
      throw error;
    }
  }

  // Update user
  async update(updateData) {
    try {
      const updates = {
        updated_at: new Date()
      };

      // Map camelCase to snake_case
      if (updateData.firstName !== undefined) updates.first_name = updateData.firstName;
      if (updateData.lastName !== undefined) updates.last_name = updateData.lastName;
      if (updateData.email !== undefined) updates.email = updateData.email.toLowerCase();
      if (updateData.role !== undefined) updates.role = updateData.role;
      if (updateData.isActive !== undefined) updates.is_active = updateData.isActive;
      if (updateData.permissions !== undefined) updates.permissions = JSON.stringify(updateData.permissions);
      if (updateData.phone !== undefined) updates.phone = updateData.phone;
      if (updateData.timezone !== undefined) updates.timezone = updateData.timezone;
      if (updateData.profileImage !== undefined) updates.profile_image = updateData.profileImage;

      const [updatedUser] = await User.knex(User.tableName)
        .where('id', this.id)
        .update(updates)
        .returning('*');

      // Update current instance
      Object.assign(this, new User(updatedUser));

      logger.audit('user_updated', this.id, { updates: Object.keys(updates) });
      return this;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  // Update password
  async updatePassword(hashedPassword) {
    try {
      await User.knex(User.tableName)
        .where('id', this.id)
        .update({
          password: hashedPassword,
          updated_at: new Date()
        });

      logger.audit('password_updated', this.id);
      return true;
    } catch (error) {
      logger.error('Error updating password:', error);
      throw error;
    }
  }

  // Update last login
  async updateLastLogin() {
    try {
      const now = new Date();
      await User.knex(User.tableName)
        .where('id', this.id)
        .update({
          last_login_at: now,
          updated_at: now
        });

      this.lastLoginAt = now;
      logger.audit('user_login', this.id);
      return true;
    } catch (error) {
      logger.error('Error updating last login:', error);
      throw error;
    }
  }

  // Verify email
  async verifyEmail() {
    try {
      const now = new Date();
      await User.knex(User.tableName)
        .where('id', this.id)
        .update({
          email_verified_at: now,
          updated_at: now
        });

      this.emailVerifiedAt = now;
      logger.audit('email_verified', this.id);
      return true;
    } catch (error) {
      logger.error('Error verifying email:', error);
      throw error;
    }
  }

  // Soft delete user
  async delete() {
    try {
      await User.knex(User.tableName)
        .where('id', this.id)
        .update({
          is_active: false,
          deleted_at: new Date(),
          updated_at: new Date()
        });

      this.isActive = false;
      logger.audit('user_deleted', this.id);
      return true;
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  // Get user's full name
  get fullName() {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  // Check if user has permission
  hasPermission(permission) {
    return this.permissions.includes(permission);
  }

  // Check if user has any of the given permissions
  hasAnyPermission(permissions) {
    return permissions.some(permission => this.hasPermission(permission));
  }

  // Check if user has all of the given permissions
  hasAllPermissions(permissions) {
    return permissions.every(permission => this.hasPermission(permission));
  }

  // Convert to JSON (exclude sensitive data)
  toJSON() {
    const user = { ...this };
    delete user.password;
    return user;
  }

  // Convert to public JSON (minimal data for public APIs)
  toPublicJSON() {
    return {
      id: this.id,
      firstName: this.firstName,
      lastName: this.lastName,
      fullName: this.fullName,
      profileImage: this.profileImage
    };
  }
}

module.exports = User;
