# Facebook Marketing API - Ad Account Promote Pages Reference

## Summary
This endpoint allows you to retrieve all promoted Pages associated with a specific Ad Account. It's a read-only endpoint that requires specific permissions to access Page data and manage ads for those Pages.

## Key Points
- Read-only endpoint that retrieves all promoted Pages for an Ad Account
- Requires pages_show_list and pages_manage_ads permissions
- Returns a list of Page objects with pagination support
- Does not support create, update, or delete operations
- Subject to rate limiting with specific error codes for different failure scenarios

## API Endpoints
- `GET /v23.0/{ad-account-id}/promote_pages`

## Parameters
- ad-account-id (path parameter)
- access-token (required for authentication)

## Content
# Ad Account Promote Pages

## Overview

The Ad Account Promote Pages endpoint allows you to get all the promoted Pages for an Ad Account. This is a read-only endpoint that returns a list of Page objects.

## Reading

### Requirements

To use this endpoint, you need the following permissions:

- `pages_show_list` permission - to get access to your app user's Pages
- `pages_manage_ads` permission - to get access to create and manage ads for your app user's Pages

### Endpoint

```
GET /v23.0/{ad-account-id}/promote_pages
```

### Example Request

```http
GET /v23.0/{ad-account-id}/promote_pages HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK Example

```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/promote_pages',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK Example

```javascript
FB.api(
    "/{ad-account-id}/promote_pages",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Parameters

This endpoint doesn't have any parameters.

### Response Format

The response returns a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

#### Fields

- **data**: A list of Page nodes
- **paging**: Pagination information (see Graph API guide for details)

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 80004 | Too many calls to this ad-account. Wait and try again. Refer to rate limiting documentation. |
| 190 | Invalid OAuth 2.0 Access Token |

## Operations

- **Creating**: Not supported on this endpoint
- **Updating**: Not supported on this endpoint  
- **Deleting**: Not supported on this endpoint

## Examples
HTTP GET request example

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest example

iOS SDK FBSDKGraphRequest example

---
**Tags:** Facebook Marketing API, Ad Account, Pages, Promote Pages, Graph API, Read Operations
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/promote_pages/
**Processed:** 2025-06-25T15:32:39.399Z