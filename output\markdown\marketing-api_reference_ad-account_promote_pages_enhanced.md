# Facebook Marketing API - Ad Account Promote Pages Reference

## Summary
Reference documentation for the Ad Account Promote Pages endpoint in Facebook's Marketing API. This endpoint allows reading promoted Pages for an Ad Account but does not support create, update, or delete operations.

## Key Points
- Retrieves all promoted Pages for a specific Ad Account
- Requires pages_show_list and pages_manage_ads permissions
- Returns a list of Page nodes with pagination support
- Only supports read operations - create, update, and delete are not available
- Subject to rate limiting with specific error codes for common issues

## API Endpoints
- `GET /v23.0/{ad-account-id}/promote_pages`

## Parameters
- ad-account-id (path parameter)

## Content
# Ad Account Promote Pages

## Overview

The Ad Account Promote Pages endpoint allows you to retrieve all promoted Pages associated with a specific Ad Account using the Facebook Marketing API.

## Reading

Get all the promoted Pages for an Ad Account.

### Requirements

To use this endpoint, you need the following permissions:

- `pages_show_list` - Required to get access to your app user's Pages
- `pages_manage_ads` - Required to create and manage ads for your app user's Pages

### HTTP Request

```http
GET /v23.0/{ad-account-id}/promote_pages HTTP/1.1
Host: graph.facebook.com
```

### Parameters

This endpoint doesn't require any parameters.

### Response Format

Reading from this edge returns a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

#### Response Fields

- **`data`**: A list of [Page](/docs/graph-api/reference/page/) nodes
- **`paging`**: Pagination information (see [Graph API guide](/docs/graph-api/using-graph-api/#paging) for details)

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 80004 | Too many calls to this ad-account. Wait and try again. See [rate limiting documentation](https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management) |
| 190 | Invalid OAuth 2.0 Access Token |

## Unsupported Operations

The following operations are **not supported** on this endpoint:
- Creating
- Updating  
- Deleting

## Additional Resources

- [Using Graph API guide](/docs/graph-api/using-graph-api/)
- [Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fpromote_pages&version=v23.0)

## Examples
GET /v23.0/{ad-account-id}/promote_pages HTTP/1.1
Host: graph.facebook.com

---
**Tags:** Facebook Marketing API, Ad Account, Pages, Promote Pages, Graph API, Marketing, Advertising
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/promote_pages/
**Processed:** 2025-06-25T15:37:32.343Z