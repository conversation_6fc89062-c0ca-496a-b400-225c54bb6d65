{"title": "Facebook Marketing API - Ad Account Asyncadrequestsets Reference", "summary": "Reference documentation for the Ad Account Asyncadrequestsets endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous ad request sets but does not support reading, updating, or deleting operations.", "content": "# Ad Account Asyncadrequestsets\n\nThis endpoint manages asynchronous ad request sets for Facebook Marketing API ad accounts.\n\n## Supported Operations\n\n### Reading\nReading operations are **not supported** on this endpoint.\n\n### Creating\nYou can make a POST request to the `asyncadrequestsets` edge from:\n- `/act_{ad_account_id}/asyncadrequestsets`\n\nWhen posting to this edge, no Graph object will be created.\n\n#### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `ad_specs` | `list<dictionary { non-empty string : <string> }>` | Specs for ads in the request set | Yes |\n| `name` | `UTF-8 encoded string` | Name of the request set | Yes |\n| `notification_mode` | `enum{OFF, ON_COMPLETE}` | Specify `0` for no notifications and `1` for notification on completion | No |\n| `notification_uri` | `URL` | If notifications are enabled, specify the URL to send them | No |\n\n#### Return Type\nThis endpoint supports read-after-write and will read the node represented by `id` in the return type.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n\n### Updating\nUpdating operations are **not supported** on this endpoint.\n\n### Deleting\nDeleting operations are **not supported** on this endpoint.", "keyPoints": ["Only POST (create) operations are supported on this endpoint", "No Graph object is created when posting to this edge", "Supports read-after-write functionality", "Requires ad_specs and name parameters for creation", "Optional notification system with completion callbacks"], "apiEndpoints": ["/act_{ad_account_id}/asyncadrequestsets"], "parameters": ["ad_specs", "name", "notification_mode", "notification_uri"], "examples": [], "tags": ["Facebook Marketing API", "Ad Account", "Async Requests", "POST API", "v23.0"], "relatedTopics": ["Ad Account", "Graph API", "Read-after-write", "Async operations", "Notification system"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "processedAt": "2025-06-25T16:25:12.206Z", "processor": "openrouter-claude-sonnet-4"}