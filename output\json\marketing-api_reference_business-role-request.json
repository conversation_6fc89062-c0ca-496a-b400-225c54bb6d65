{"title": "Business Role Request", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_jO\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_5Q\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_8U\"></div></span></div></div>\n\n<h1 id=\"overview\">Business Role Request</h1>\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Represents a business user request. See the requests from an admin of the Business for people to join as member of this business.</p>\n</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_gI\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_ir\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_m6\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_H+\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_oM\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_Hr\">iOS SDK</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%7Bbusiness-role-request-id%7D&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_b_mB\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/{</span><span class=\"pln\">business</span><span class=\"pun\">-</span><span class=\"pln\">role</span><span class=\"pun\">-</span><span class=\"pln\">request</span><span class=\"pun\">-</span><span class=\"pln\">id</span><span class=\"pun\">}</span><span class=\"pln\"> HTTP</span><span class=\"pun\">/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Business role invitation request ID.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>created_by</code></span></div><div class=\"_yb _yc\"><span>BusinessUser|SystemUser</span></div></td><td><p class=\"_yd\"></p><div><div><p>User who sent the invitation to join this business.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>created_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>Admin sent this request to someone to join a business at this time.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>email</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Email of user invited to join the business.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>expiration_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>Invitation to join business expires at this time.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>finance_role</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>When you invite someone to join business, pre-assign the Finance role.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>invited_user_type</code></span></div><div class=\"_yb _yc\"><span>list&lt;enum&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Invited user type of this role request</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>owner</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/business/\">Business</a></div></td><td><p class=\"_yd\"></p><div><div><p>Invite someone to join this business.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>role</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>Business role for user invited to the business.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>Status of the invitation, such as accepted, declined, expired and so on.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>updated_by</code></span></div><div class=\"_yb _yc\"><span>BusinessUser|SystemUser</span></div></td><td><p class=\"_yd\"></p><div><div><p>User who updated the invitation.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>updated_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>Time invitation updated.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Creating\">Creating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div>\n\n<h2 id=\"Updating\">Updating</h2><div class=\"_844_\"><div class=\"_3-98\">You can update a&nbsp;<a href=\"/docs/marketing-api/reference/business-role-request/\">BusinessRoleRequest</a> by making a POST request to <a href=\"/docs/marketing-api/reference/business-role-request/\"><code>/{business_role_request_id}</code></a>.<div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_g_Qq\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>role</code></span></div><div class=\"_yb\">enum {FINANCE_EDITOR, FINANCE_ANALYST, ADS_RIGHTS_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER_CENTER_ADMIN, PARTNER_CENTER_ANALYST, PARTNER_CENTER_OPERATIONS, PARTNER_CENTER_MARKETING, PARTNER_CENTER_EDUCATION, MANAGE, DEFAULT, FINANCE_EDIT, FINANCE_VIEW}</div></td><td><p class=\"_yd\"></p><div><div><p>Update invitation to include this role, such as <code>ADMIN</code>.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\"><div class=\"_3-98\">You can delete a&nbsp;<a href=\"/docs/marketing-api/reference/business-role-request/\">BusinessRoleRequest</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/business-role-request/\"><code>/{business_role_request_id}</code></a>.<div><h3 id=\"parameters-3\">Parameters</h3>This endpoint doesn't have any parameters.</div><h3 id=\"return-type-2\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-3\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/business-role-request/"], "url": "https://developers.facebook.com/docs/marketing-api/reference/business-role-request", "timestamp": "2025-06-25T15:44:23.100Z"}