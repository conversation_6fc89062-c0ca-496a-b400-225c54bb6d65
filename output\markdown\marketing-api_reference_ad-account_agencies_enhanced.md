# Facebook Marketing API - Ad Account Agencies Reference

## Summary
This reference documentation covers the Ad Account Agencies endpoint in the Facebook Marketing API, which allows you to read and manage agencies associated with ad accounts. The endpoint supports reading agency relationships and deleting associations, but does not support creating or updating operations.

## Key Points
- The endpoint only supports reading and deleting operations, not creating or updating
- Returns Business nodes with additional access-related fields like access_requested_time and access_status
- Requires proper OAuth 2.0 access token and is subject to rate limiting
- DELETE operation requires a business parameter to specify which agency to dissociate
- Response includes pagination support for large result sets

## API Endpoints
- `GET /v23.0/{ad-account-id}/agencies`
- `DELETE /act_{ad_account_id}/agencies`

## Parameters
- ad-account-id
- business
- access_requested_time
- access_status
- access_updated_time
- permitted_tasks

## Content
# Ad Account Agencies

The Ad Account Agencies endpoint allows you to manage agencies associated with ad accounts in the Facebook Marketing API.

## Reading

Retrieve agencies associated with ad accounts.

### Endpoint
```
GET /v23.0/{ad-account-id}/agencies
```

### Example Request

**HTTP:**
```http
GET /v23.0/{ad-account-id}/agencies HTTP/1.1
Host: graph.facebook.com
```

**PHP SDK:**
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/agencies',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

**JavaScript SDK:**
```javascript
FB.api(
    "/{ad-account-id}/agencies",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Parameters
This endpoint doesn't have any parameters.

### Response Format
```json
{
    "data": [],
    "paging": {}
}
```

#### Data Fields
Returns a list of Business nodes with the following additional fields:

| Field | Type | Description | Default |
|-------|------|-------------|----------|
| `access_requested_time` | datetime | The creation time of the access request | ✓ |
| `access_status` | enum | The status of the access request | ✓ |
| `access_updated_time` | datetime | The update time of the access request | ✓ |
| `permitted_tasks` | list<string> | The permissions of tasks associated with the access request | ✓ |

### Error Codes
| Error | Description |
|-------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 80004 | Too many calls to this ad-account. Wait and try again |

## Creating
You can't perform this operation on this endpoint.

## Updating
You can't perform this operation on this endpoint.

## Deleting

Dissociate a Business from an AdAccount.

### Endpoint
```
DELETE /act_{ad_account_id}/agencies
```

### Parameters
| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `business` | numeric string | Business ID to dissociate | ✓ |

### Return Type
```json
{
  "success": bool
}
```

### Error Codes
| Error | Description |
|-------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |

## Examples
GET /v23.0/{ad-account-id}/agencies HTTP/1.1

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest example

iOS SDK FBSDKGraphRequest example

---
**Tags:** Facebook Marketing API, Ad Account, Agencies, Business, Graph API, REST API
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/agencies/
**Processed:** 2025-06-25T15:20:48.867Z