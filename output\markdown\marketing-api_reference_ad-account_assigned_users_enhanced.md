# Facebook Marketing API - Ad Account Assigned Users Reference

## Summary
Complete reference for managing business and system users assigned to Facebook Ad Accounts through the Marketing API. Covers reading, updating, and deleting user assignments with task-based permissions.

## Key Points
- Task-based permissions system replaced role-based permissions in v3.1
- Supports reading, updating, and deleting user assignments but not creating new ones
- Three main permission levels: MANAGE (admin), ADVERTISE (general user), and ANALYZE (reports only)
- Business and system users can be assigned with specific task combinations
- Rate limiting applies to ad account operations

## API Endpoints
- `GET /v23.0/{ad-account-id}/assigned_users`
- `POST /act_{ad_account_id}/assigned_users`
- `DELETE /act_{ad_account_id}/assigned_users`

## Parameters
- business
- tasks
- user
- permitted_tasks
- total_count

## Content
# Ad Account Assigned Users

Manage business and system users assigned to Facebook Ad Accounts through the Marketing API.

## Reading Assigned Users

Retrieve business and system users assigned to an Ad Account.

### Endpoint
```
GET /v23.0/{ad-account-id}/assigned_users
```

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|---------|
| `business` | numeric string or integer | The business associated with this Ad Account | Yes |

### Response Format

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Data Fields

Each AssignedUser node includes:

| Field | Type | Description |
|-------|------|-------------|
| `permitted_tasks` | list<string> | Tasks that are assignable on this object |
| `tasks` | list<string> | All unpacked roles/tasks of this particular user on this object |

#### Summary Fields

| Field | Type | Description |
|-------|------|-------------|
| `total_count` | unsigned int32 | Total number of business and system users assigned to this Ad Account |

### Code Examples

#### HTTP
```http
GET /v23.0/{ad-account-id}/assigned_users HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
try {
  $response = $fb->get(
    '/{ad-account-id}/assigned_users',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/assigned_users",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

## Updating User Assignments

Assign users to Ad Accounts with task-based permissions.

### Endpoint
```
POST /act_{ad_account_id}/assigned_users
```

### Task-Based Permissions

As of v3.1, Facebook uses task-based permissions instead of role-based permissions:

#### Ad Account Tasks
- **ADMIN** → `['MANAGE', 'ADVERTISE', 'ANALYZE']` - Manage all aspects of ad campaigns, reporting, billing and ad account permissions
- **GENERAL_USER** → `['ADVERTISE', 'ANALYZE']` - Create ads using funding source, run reports
- **GENERAL_USER** → `['ANALYZE']` - Run reports only

#### Business Manager Tasks
- **MANAGER** → `['MANAGE', 'CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`
- **CONTENT_CREATOR** → `['CREATE_CONTENT', 'MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`
- **MODERATOR** → `['MODERATE', 'ADVERTISE', 'ANALYZE', 'DRAFT']`
- **ADVERTISER** → `['ADVERTISE', 'ANALYZE', 'DRAFT']`
- **INSIGHTS_ANALYST** → `['ANALYZE', 'DRAFT']`
- **CREATIVE_HUB_MOCKUPS_MANAGER** → `['DRAFT']`

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|---------|
| `tasks` | array<enum> | AdAccount permission tasks: MANAGE, ADVERTISE, ANALYZE, DRAFT, AA_ANALYZE | No |
| `user` | UID | Business user id or system user id | Yes |

### Return Type
```json
{
  "success": bool
}
```

## Deleting User Assignments

Remove user assignments from Ad Accounts.

### Endpoint
```
DELETE /act_{ad_account_id}/assigned_users
```

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|---------|
| `user` | UID | Business user id or system user id | Yes |

### Return Type
```json
{
  "success": bool
}
```

## Error Codes

| Error | Description |
|-------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | The action attempted has been deemed abusive or is otherwise disallowed |
| 2620 | Invalid call to update account permissions |
| 3919 | There was an unexpected technical issue. Please try again |
| 80004 | Too many calls to this ad-account. Wait and try again |

## Notes

- Creating new assigned users through this endpoint is not supported
- Task-based permissions replace role-based permissions as of v3.1
- The endpoint supports read-after-write for update operations
- Rate limiting applies to ad account operations

## Examples
HTTP GET request for reading assigned users

PHP SDK implementation for retrieving assigned users

JavaScript SDK API call

Task-based permission mapping from roles

JSON response structure with data, paging, and summary

---
**Tags:** Facebook Marketing API, Ad Account, User Management, Permissions, Business Manager, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/assigned_users/  
**Processed:** 2025-06-25T16:24:22.909Z