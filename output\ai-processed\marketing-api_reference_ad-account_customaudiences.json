{"title": "Facebook Marketing API - Ad Account Custom Audiences Reference", "summary": "Complete reference documentation for managing custom audiences within Facebook ad accounts, including reading, creating, and managing audience data through the Marketing API. Covers endpoints, parameters, examples, and error handling for custom audience operations.", "content": "# Ad Account Custom Audiences\n\nThe custom audiences associated with the ad account. This endpoint allows you to manage custom audiences for targeting in Facebook advertising campaigns.\n\n**Note:** To retrieve the IDs of lookalike audiences based on your custom audiences, use the `lookalike_audience_ids` field. See [Lookalike Audiences - Managing Audiences](/docs/marketing-api/audiences/guides/lookalike-audiences#read) for more information.\n\n## Reading Custom Audiences\n\nRetrieve custom audiences associated with an ad account.\n\n### Endpoint\n```\nGET /v23.0/act_<AD_ACCOUNT_ID>/customaudiences\n```\n\n### Example Request\n```bash\ncurl -X GET -G \\\n  -d 'fields=\"id\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `business_id` | numeric string or integer | Optional. Assists with filters, such as recently used |\n| `fetch_primary_audience` | boolean | Default: `false`. Fetch primary audience |\n| `fields` | list<string> | Fields to be retrieved. Default behavior returns only IDs |\n| `filtering` | list<Filter Object> | Filters on the report data |\n| `pixel_id` | numeric string | Optional. Fetches audiences associated to specific pixel |\n\n#### Filter Object Structure\n| Field | Type | Description |\n|-------|------|-------------|\n| `field` | string | Required. Field to filter on |\n| `operator` | enum | Required. Filter operator (EQUAL, NOT_EQUAL, etc.) |\n| `value` | string | Required. Filter value |\n\n### Response Format\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n## Creating Custom Audiences\n\nCreate a new custom audience. You can create a maximum of 500 custom audiences per ad account.\n\n### Endpoint\n```\nPOST /v23.0/act_<AD_ACCOUNT_ID>/customaudiences\n```\n\n### Example Request\n```bash\ncurl -X POST \\\n  -F 'name=\"My new Custom Audience\"' \\\n  -F 'subtype=\"CUSTOM\"' \\\n  -F 'description=\"People who purchased on my website\"' \\\n  -F 'customer_file_source=\"USER_PROVIDED_ONLY\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences\n```\n\n### Key Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `name` | string | The name of this custom audience |\n| `subtype` | enum | Type of custom audience (CUSTOM, WEBSITE, APP, etc.) |\n| `description` | string | The description for this custom audience |\n| `customer_file_source` | enum | Source of customer information |\n| `pixel_id` | numeric string | The pixel associated with this audience |\n| `retention_days` | int64 | Days to keep user in cluster (1-180, default: forever) |\n| `rule` | string | Audience rule for website custom audiences |\n| `lookalike_spec` | JSON string | Specification for creating lookalike audience |\n\n### Subtype Options\n- `CUSTOM`: Standard custom audience (limit: 500)\n- `WEBSITE`: Website traffic audience\n- `APP`: Mobile app audience\n- `LOOKALIKE`: Lookalike audience (limit: 10,000)\n- `OFFLINE_CONVERSION`: Offline events audience\n- And more...\n\n### Return Type\n```json\n{\n  \"id\": \"numeric_string\",\n  \"message\": \"string\"\n}\n```\n\n## Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | Action deemed abusive or disallowed |\n| 2654 | Failed to create custom audience |\n| 2663 | Terms of service not accepted |\n| 2667 | Account permissions don't allow custom audience creation |\n| 80003 | Too many calls to ad account (rate limiting) |\n\n## Important Notes\n\n1. **Audience Limits**: Maximum of 500 custom audiences per ad account\n2. **Two-Step Process**: First create a blank audience, then add people via the users edge\n3. **Rate Limiting**: Be aware of API rate limits for custom audience operations\n4. **Permissions**: Ensure proper permissions for custom audience creation\n5. **Terms of Service**: Must accept Facebook's custom audience terms of service", "keyPoints": ["Custom audiences can be read, created, but not updated or deleted through this endpoint", "Maximum limit of 500 custom audiences per ad account", "Creating custom audiences requires a two-step process: create blank audience, then add users", "Various audience subtypes available including CUSTOM, WEBSITE, APP, and LOOKALIKE", "Rate limiting applies with specific error code 80003 for too many calls"], "apiEndpoints": ["GET /v23.0/act_<AD_ACCOUNT_ID>/customaudiences", "POST /v23.0/act_<AD_ACCOUNT_ID>/customaudiences"], "parameters": ["business_id", "fetch_primary_audience", "fields", "filtering", "pixel_id", "name", "subtype", "description", "customer_file_source", "retention_days", "rule", "lookalike_spec"], "examples": ["GET request to retrieve custom audiences with fields parameter", "POST request to create new custom audience with name, subtype, and description", "cURL examples for both reading and creating operations", "PHP SDK, JavaScript SDK, Android SDK, and iOS SDK code samples"], "tags": ["Facebook Marketing API", "Custom Audiences", "Ad Account", "Audience Management", "Targeting", "API Reference"], "relatedTopics": ["Lookalike Audiences", "Custom Audience Users Edge", "Website Custom Audiences", "Product Audiences", "Video Remarketing Audiences", "Offline Conversion Datasets", "Facebook Pixel"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiences/", "processedAt": "2025-06-25T16:26:05.649Z", "processor": "openrouter-claude-sonnet-4"}