{"title": "Facebook Marketing API - Ad Account Applications Reference", "summary": "Reference documentation for the Ad Account Applications endpoint in Facebook's Marketing API. This endpoint allows reading applications associated with an ad account but does not support creating, updating, or deleting operations.", "content": "# Ad Account Applications\n\nThe Ad Account Applications endpoint provides access to applications associated with a specific ad account in Facebook's Marketing API.\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/applications\n```\n\n### Example Requests\n\n#### HTTP\n```http\nGET /v23.0/{ad-account-id}/applications HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/applications',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/applications\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n#### Android SDK\n```java\nnew GraphRequest(\n    AccessToken.getCurrentAccessToken(),\n    \"/{ad-account-id}/applications\",\n    null,\n    HttpMethod.GET,\n    new GraphRequest.Callback() {\n        public void onCompleted(GraphResponse response) {\n            /* handle the result */\n        }\n    }\n).executeAsync();\n```\n\n#### iOS SDK\n```objc\nFBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]\n                               initWithGraphPath:@\"/{ad-account-id}/applications\"\n                                      parameters:params\n                                      HTTPMethod:@\"GET\"];\n[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,\n                                      id result,\n                                      NSError *error) {\n    // Handle the result\n}];\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Format\nReading from this edge returns a JSON formatted result:\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Fields\n- **data**: A list of Application nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Error Codes\n| Error | Description |\n|-------|-------------|\n| 200 | Permissions error |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 80004 | Too many calls to this ad-account. Wait and try again. See rate limiting documentation. |\n| 100 | Invalid parameter |\n| 2500 | Error parsing graph query |\n\n## Operations Not Supported\n\n### Creating\nYou can't perform this operation on this endpoint.\n\n### Updating\nYou can't perform this operation on this endpoint.\n\n### Deleting\nYou can't perform this operation on this endpoint.", "keyPoints": ["Only supports reading operations - creating, updating, and deleting are not available", "Returns a list of Application nodes associated with the specified ad account", "No parameters required for the GET request", "Standard Facebook Graph API pagination is supported", "Multiple SDK examples provided for different platforms"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/applications"], "parameters": ["ad-account-id (required in URL path)", "access-token (required for authentication)"], "examples": ["HTTP GET request example", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest implementation", "iOS SDK FBSDKGraphRequest implementation"], "tags": ["Facebook Marketing API", "Ad Account", "Applications", "Graph API", "Reference Documentation", "Read-only Endpoint"], "relatedTopics": ["Graph API Usage Guide", "Application Reference", "Rate Limiting", "Pagination", "OAuth 2.0 Access Tokens", "Erro<PERSON>"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/applications/", "processedAt": "2025-06-25T15:21:18.732Z", "processor": "openrouter-claude-sonnet-4"}