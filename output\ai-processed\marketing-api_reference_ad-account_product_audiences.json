{"title": "Facebook Marketing API - Ad Account Product Audiences", "summary": "Documentation for creating product audiences within Facebook ad accounts using the Marketing API. Product audiences allow advertisers to target users based on their interactions with specific products, including events like AddToCart and Purchase with customizable inclusion and exclusion rules.", "content": "# Ad Account Product Audiences\n\n## Overview\n\nProduct audiences are custom audiences created based on user interactions with specific products in your catalog. This endpoint allows you to create targeted audiences using product-specific events and rules.\n\n## Supported Operations\n\n### Reading\nReading operations are not supported on this endpoint.\n\n### Creating\n\nYou can create product audiences by making a POST request to the `product_audiences` edge:\n\n**Endpoint:** `/act_{ad_account_id}/product_audiences`\n\n#### Example Request\n\n```http\nPOST /v23.0/act_<AD_ACCOUNT_ID>/product_audiences HTTP/1.1\nHost: graph.facebook.com\n\nname=Test+Iphone+Product+Audience&product_set_id=<PRODUCT_SET_ID>&inclusions=[{\"retention_seconds\":86400,\"rule\":{\"and\":[{\"event\":{\"eq\":\"AddToCart\"}},{\"userAgent\":{\"i_contains\":\"iPhone\"}}]}}]&exclusions=[{\"retention_seconds\":172800,\"rule\":{\"event\":{\"eq\":\"Purchase\"}}}]\n```\n\n#### PHP SDK Example\n\n```php\n$response = $fb->post(\n  '/act_<AD_ACCOUNT_ID>/product_audiences',\n  array (\n    'name' => 'Test Iphone Product Audience',\n    'product_set_id' => '<PRODUCT_SET_ID>',\n    'inclusions' => '[{\"retention_seconds\":86400,\"rule\":{\"and\":[{\"event\":{\"eq\":\"AddToCart\"}},{\"userAgent\":{\"i_contains\":\"iPhone\"}}]}}]',\n    'exclusions' => '[{\"retention_seconds\":172800,\"rule\":{\"event\":{\"eq\":\"Purchase\"}}}]',\n  ),\n  '{access-token}'\n);\n```\n\n#### cURL Example\n\n```bash\ncurl -X POST \\\n  -F 'name=\"Test Iphone Product Audience\"' \\\n  -F 'product_set_id=\"<PRODUCT_SET_ID>\"' \\\n  -F 'inclusions=[\n       {\n         \"retention_seconds\": 86400,\n         \"rule\": {\n           \"and\": [\n             {\n               \"event\": {\n                 \"eq\": \"AddToCart\"\n               }\n             },\n             {\n               \"userAgent\": {\n                 \"i_contains\": \"iPhone\"\n               }\n             }\n           ]\n         }\n       }\n     ]' \\\n  -F 'exclusions=[\n       {\n         \"retention_seconds\": 172800,\n         \"rule\": {\n           \"event\": {\n             \"eq\": \"Purchase\"\n           }\n         }\n       }\n     ]' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/product_audiences\n```\n\n### Parameters\n\n#### Required Parameters\n- `name` (string): Name for the product audience\n- `product_set_id` (numeric string or integer): ID of the product set\n- `event_sources` (array): Event sources configuration with required `id` and `type`\n\n#### Optional Parameters\n- `associated_audience_id` (int64): Associated audience identifier\n- `creation_params` (dictionary): Additional creation parameters\n- `description` (string): Audience description\n- `enable_fetch_or_create` (boolean): Enable fetch or create functionality\n- `inclusions` (list): Rules for including users in the audience\n- `exclusions` (list): Rules for excluding users from the audience\n- `opt_out_link` (string): Opt-out link for the audience\n- `parent_audience_id` (int64): Parent audience identifier\n- `subtype` (enum): Audience subtype\n\n#### Inclusion/Exclusion Rule Structure\n\nBoth `inclusions` and `exclusions` parameters accept objects with:\n- `retention_seconds` (integer): How long to retain users (required for retention object)\n- `rule` (object): The targeting rule\n- `booking_window` (object): Time window configuration\n- `count` (object): Count-based rules\n- `pixel_id` (int64): Associated pixel ID\n\n### Return Type\n\nThe endpoint returns a struct containing:\n- `id` (numeric string): The created audience ID\n- `message` (string): Response message\n\n### Error Codes\n\n- **100**: Invalid parameter\n\n### Updating and Deleting\n\nUpdate and delete operations are not supported on this endpoint.\n\n## Event Source Types\n\nSupported event source types include:\n- `APP`: Mobile app events\n- `OFFLINE_EVENTS`: Offline conversion events\n- `PAGE`: Facebook page events\n- `PIXEL`: Facebook pixel events\n\n## Audience Subtypes\n\nAvailable audience subtypes include: CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST", "keyPoints": ["Product audiences target users based on specific product interactions and events", "Only creation operations are supported - reading, updating, and deleting are not available", "Requires product_set_id, name, and event_sources as mandatory parameters", "Supports complex inclusion and exclusion rules with retention periods", "Returns audience ID and message upon successful creation"], "apiEndpoints": ["/act_{ad_account_id}/product_audiences"], "parameters": ["name", "product_set_id", "event_sources", "inclusions", "exclusions", "retention_seconds", "rule", "associated_audience_id", "creation_params", "description", "enable_fetch_or_create", "opt_out_link", "parent_audience_id", "subtype"], "examples": ["POST request to create iPhone product audience with AddToCart inclusion and Purchase exclusion", "PHP SDK implementation with retention rules", "cURL command with complex rule structure", "JavaScript SDK usage pattern"], "tags": ["Facebook Marketing API", "Product Audiences", "Ad Account", "Custom Audiences", "Targeting", "E-commerce", "Product Catalog"], "relatedTopics": ["Ad Account", "Custom Audiences", "Product Sets", "Facebook Pixel", "Event Sources", "Audience Targeting", "Marketing API", "Graph API"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/product_audiences/", "processedAt": "2025-06-25T16:29:06.882Z", "processor": "openrouter-claude-sonnet-4"}