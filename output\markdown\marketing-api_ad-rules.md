# Ad Rules Engine

On This Page

[Ad Rules Engine](#ad-rules-engine)

[Documentation Contents](#documentation-contents)

[Overview](#overview)

[Guides](#guides)

# Ad Rules Engine

A central rule management service that helps you easily, efficiently and intelligently manage ads. Without it, you must query the Marketing API to monitor an ad's performance and manually take actions on certain conditions. Since we can express most conditions as logical expressions, we can automate management two ways: using [_Schedule-based_](/docs/marketing-api/ad-rules/scheduled-based-rules) or [_Trigger-based_](/docs/marketing-api/ad-rules/trigger-based-rules) Based rules.

New to this? Try the rules-based notification quickstart in your [App Dashboard, Quickstarts](/apps/).

## Documentation Contents

### [Overview](/docs/marketing-api/ad-rules/overview)

Core concepts and usage requirements. Learn about [**Evaluation Spec**](/docs/marketing-api/ad-rules/overview/evaluation-spec), [**Execution Spec**](/docs/marketing-api/ad-rules/overview/execution-spec), and [**Change Spec**](/docs/marketing-api/ad-rules/overview/change-spec).

### [Guides](/docs/marketing-api/ad-rules/guides)

Use case based guides: [**Trigger Based Ad Rules**](/docs/marketing-api/ad-rules/guides/trigger-based-rules), [**Schedule Based Rules**](/docs/marketing-api/ad-rules/guides/scheduled-based-rules), [**Advanced Scheduling**](/docs/marketing-api/ad-rules/guides/advanced-scheduling), [**Rebalance Budget Ad Rules**](/docs/marketing-api/ad-rules/guides/rebalance-budget), [**ROAS Ad Rules**](/docs/marketing-api/ad-rules/guides/roas-ad-rules), and [**API Calls**](/docs/marketing-api/ad-rules/guides/api-calls).

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)