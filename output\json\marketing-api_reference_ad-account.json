{"title": "Ad Account", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_1n_Ef\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Ad Account</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#volume\">Ad Volume</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#running-or-in-review\">Running Or In Review</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#breakdown-by-actors\">Breakdown By Actors</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example--querying\">Example, Querying</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#limits\">Limits</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#-digital-services-act-saved-beneficiary-payor-information\"> Digital Services Act Saved Beneficiary/Payor Information</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#fields\">Fields</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#edges\">Edges</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-2\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-2\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-3\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type-2\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-3\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-4\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type-3\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-4\">Error Codes</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_oK\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_aP\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_7x\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_3I\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Ad Account</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><p>Represents a business, person or other entity who creates and manages ads on Facebook. Multiple people can manage an account, and each person can have one or more levels of access to an account, see <a href=\"/docs/marketing-api/business-manager-api\">Business Manager API</a>.</p><div class=\"_57yz _5s-k _3-8p\"><div class=\"_57y-\"><p>In response to Apple’s new policy, we are announcing breaking changes that will affect SDKAdNetwork, Marketing API and Ads Insights API endpoints.</p>\n\n<p>To learn more about how Apple’s iOS 14.5 requirements will impact Facebook advertising, visit our Business Help Center aricles and changelog:</p>\n\n<ul>\n<li><a href=\"https://www.facebook.com/business/help/****************?id=***************\">Facebook SDK for iOS, App Events API and Mobile Measurement Partners Updates for Apple's iOS 14 Requirements</a></li>\n<li><a href=\"https://www.facebook.com/business/help/***************\">Facebook Pixel Updates for Apple's iOS 14 Requirements</a></li>\n<li><a href=\"https://developers.facebook.com/docs/graph-api/changelog/non-versioned-changes/jan-19-2021\">January 19, 2021 - Breaking Changes</a></li>\n</ul>\n</div></div><div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>The <code>agency_client_declaration</code> field requires <a href=\"https://www.facebook.com/business/help/***************?id=***************\">Admin privileges</a> for all operations starting with v10.0 and will be required for all versions on May 25, 2021.</p>\n</div></div><h2 id=\"volume\">Ad Volume</h2>\n\n<p>You can view the volume of ads <em>running or in review</em> for your ad accounts. These ads will count against the ads limit per page that we will enact in early 2021. Query the number of ads running or in review for a given ad account.</p>\n<div class=\"_57yz _57z0 _3-8p\"><div class=\"_57y-\"><p>Ad Limit Per Page enforcement begins for when a Page reaches its ad limit enforcement date. Enforcement date can be queried <a href=\"https://developers.facebook.com/docs/marketing-api/insights-api/ads-volume\">here</a>.</p>\n\n<p>When a Page is at its ad limit:</p>\n\n<ul>\n<li>New ads (or ads scheduled to begin at that time) do not publish successfully. </li>\n<li>Actions on existing ads are limited to pausing and archiving until the number of ads running or in review is below the ad limit.</li>\n</ul>\n</div></div><p>To see the ads volume for your ad account:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"access_token=&lt;access_token&gt;\"</span><span class=\"pln\"> \n  </span><span class=\"str\">\"https://graph.facebook.com/&lt;API_VERSION&gt;/act_&lt;ad_account_ID&gt;/ads_volume\"</span></pre><p>The response looks like this:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"str\">\"data\"</span><span class=\"pun\">:[{</span><span class=\"str\">\"ads_running_or_in_review_count\"</span><span class=\"pun\">:</span><span class=\"lit\">2</span><span class=\"pun\">}]}</span></pre><p>For information on managing ads volume, see <a href=\"https://www.facebook.com/business/help/****************\">About Managing Ad Volume</a>.</p>\n\n<h3 id=\"running-or-in-review\">Running Or In Review</h3>\n\n<p>To see if an ad is running or in review, we check <code>effective_status</code>, <code>configured_status</code>, and the ad account's status:</p>\n\n<ul>\n<li>If an ad has <code>effective_status</code> of <code>1</code> - <code>active</code>, we consider it a <em>running</em> or <em>in review</em>.</li>\n<li>If an ad has <code>configured_status</code> of <code>active</code> and <code>effective_status</code> of <code>9</code> - <code>pending review</code>, or <code>17</code> - <code>pending processing</code> we consider it a <em>running</em> or <em>in review</em>.</li>\n<li>The ad can be <em>running</em> or <em>in review</em> only if the ad account status is in <code>1</code> - <code>active</code>, <code>8</code> - <code>pending settlement</code>, <code>9</code> - <code>in grace period</code>.        </li>\n</ul>\n\n<p>We also determine if an ad is running or in review based on the ad set's schedule.</p>\n\n<ul>\n<li>If start time is before current time, and current time is before end time, then we consider the ad running or in review.</li>\n<li>If start time is before current time and the ad set has no end time, we also consider it running or in review.</li>\n</ul>\n\n<p>For example, if the ad set is scheduled to run in the future, the ads are not running or in review. However if the ad set is scheduled to run from now until three months from now, we consider the ads running or in review.</p>\n\n<p>If you are using special ads scheduling features, such as <em>day-parting</em>, we consider the ad running or in review the <em>whole day</em>, not just for the part of the day when the ad starts running.</p>\n\n<h3 id=\"breakdown-by-actors\">Breakdown By Actors</h3>\n\n<p>We’ve added the <code>show_breakdown_by_actor</code> parameter to the <code>act_123/ads_volume</code> endpoint so you can query ad volume and ad limits-related information for each page. For more details, see <a href=\"/docs/marketing-api/insights-api/ads-volume#breakdown-by-actors\">Breakdown by Actors</a>.</p>\n<hr><h3 id=\"example--querying\">Example, Querying</h3>\n<p>For example, query for all ad sets in this ad account:</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_u7\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_XJ\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_DD\">Python Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_z7\">Java Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_G+\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_a_HI\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdAccount</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdSetFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$account </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$adsets </span><span class=\"pun\">=</span><span class=\"pln\"> $account</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getAdSets</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">CONFIGURED_STATUS</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">EFFECTIVE_STATUS</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">));</span><span class=\"pln\">\n\n</span><span class=\"kwd\">foreach</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">$adsets </span><span class=\"kwd\">as</span><span class=\"pln\"> $adset</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo $adset</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">}.</span><span class=\"pln\">PHP_EOL</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_b_YL\" style=\"\"><code><span class=\"kwd\">from</span><span class=\"pln\"> facebookads</span><span class=\"pun\">.</span><span class=\"pln\">adobjects</span><span class=\"pun\">.</span><span class=\"pln\">adaccount </span><span class=\"kwd\">import</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pln\">\n</span><span class=\"kwd\">from</span><span class=\"pln\"> facebookads</span><span class=\"pun\">.</span><span class=\"pln\">adobjects</span><span class=\"pun\">.</span><span class=\"pln\">adset </span><span class=\"kwd\">import</span><span class=\"pln\"> </span><span class=\"typ\">AdSet</span><span class=\"pln\">\n\naccount </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">)</span><span class=\"pln\">\nadsets </span><span class=\"pun\">=</span><span class=\"pln\"> account</span><span class=\"pun\">.</span><span class=\"pln\">get_ad_sets</span><span class=\"pun\">(</span><span class=\"pln\">fields</span><span class=\"pun\">=[</span><span class=\"typ\">AdSet</span><span class=\"pun\">.</span><span class=\"typ\">Field</span><span class=\"pun\">.</span><span class=\"pln\">name</span><span class=\"pun\">])</span><span class=\"pln\">\n\n</span><span class=\"kwd\">for</span><span class=\"pln\"> adset </span><span class=\"kwd\">in</span><span class=\"pln\"> adsets</span><span class=\"pun\">:</span><span class=\"pln\">\n    </span><span class=\"kwd\">print</span><span class=\"pun\">(</span><span class=\"pln\">adset</span><span class=\"pun\">[</span><span class=\"typ\">AdSet</span><span class=\"pun\">.</span><span class=\"typ\">Field</span><span class=\"pun\">.</span><span class=\"pln\">name</span><span class=\"pun\">])</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_c_nq\" style=\"\"><code><span class=\"typ\">APINodeList</span><span class=\"pun\">&lt;</span><span class=\"typ\">AdSet</span><span class=\"pun\">&gt;</span><span class=\"pln\"> adSets </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;,</span><span class=\"pln\"> context</span><span class=\"pun\">).</span><span class=\"pln\">getAdSets</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">requestNameField</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">requestConfiguredStatusField</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">requestEffectiveStatusField</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">execute</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_d_73\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=name,configured_status,effective_status'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v2.11/act_&lt;AD_ACCOUNT_ID&gt;/adsets</span></code></pre></div></div><p></p><h3 id=\"limits\">Limits</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nLimit\n</th><th>\nValue\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_e_sR\"><tr class=\"row_0\"><td><p>Maximum number of ad accounts per person</p>\n</td><td><p>25</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p>Maximum number of people with access, per ad account</p>\n</td><td><p>25</p>\n</td></tr><tr class=\"row_2\"><td><p>Maximum number of ads per regular ad account</p>\n</td><td><p>6,000 non-archived non-deleted ads</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p>Maximum number of ads per bulk ad account</p>\n</td><td><p>50,000 non-archived non-deleted ads</p>\n</td></tr><tr class=\"row_4\"><td><p>Maximum number of archived ads per ad account</p>\n</td><td><p>100,000 archived ads</p>\n</td></tr><tr class=\"row_5 _5m29\"><td><p>Maximum number of ad sets per regular ad account</p>\n</td><td><p>6,000 non-archived non-deleted ad sets</p>\n</td></tr><tr class=\"row_6\"><td><p>Maximum number of ad sets per bulk ad account</p>\n</td><td><p>10,000 non-archived non-deleted ad sets</p>\n</td></tr><tr class=\"row_7 _5m29\"><td><p>Maximum number of archived ad sets per ad account</p>\n</td><td><p>100,000 archived ad sets</p>\n</td></tr><tr class=\"row_8\"><td><p>Maximum number of ad campaigns per regular ad account</p>\n</td><td><p>6,000 non-archived non-deleted ad campaigns</p>\n</td></tr><tr class=\"row_9 _5m29\"><td><p>Maximum number of ad campaigns per bulk ad account</p>\n</td><td><p>10,000 non-archived non-deleted ad campaigns</p>\n</td></tr><tr class=\"row_10\"><td><p>Maximum number of archived ad campaigns per ad account</p>\n</td><td><p>100,000 archived ad campaigns</p>\n</td></tr><tr class=\"row_11 _5m29\"><td><p>Maximum number of images per ad account</p>\n</td><td><p>Unlimited</p>\n</td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>An ad account is an account used for managing ads on Facebook</p>\n</div><div><p>Finding people with access to this account:</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_f_k2\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_g_zz\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_h_PE\">Python Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_i_LL\">Java Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_j_+X\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_k_ph\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdAccount</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\UserFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$account </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$users </span><span class=\"pun\">=</span><span class=\"pln\"> $account</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getUsers</span><span class=\"pun\">();</span><span class=\"pln\">\n\n</span><span class=\"kwd\">foreach</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">$users </span><span class=\"kwd\">as</span><span class=\"pln\"> $user</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo $user</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">UserFields</span><span class=\"pun\">::</span><span class=\"pln\">ID</span><span class=\"pun\">}.</span><span class=\"pln\">PHP_EOL</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_l_0q\" style=\"\"><code><span class=\"kwd\">from</span><span class=\"pln\"> facebookads</span><span class=\"pun\">.</span><span class=\"pln\">adobjects</span><span class=\"pun\">.</span><span class=\"pln\">adaccount </span><span class=\"kwd\">import</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pln\">\n</span><span class=\"kwd\">from</span><span class=\"pln\"> facebookads</span><span class=\"pun\">.</span><span class=\"pln\">adobjects</span><span class=\"pun\">.</span><span class=\"pln\">adaccountuser </span><span class=\"kwd\">import</span><span class=\"pln\"> </span><span class=\"typ\">AdAccountUser</span><span class=\"pln\">\n\naccount </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">)</span><span class=\"pln\">\nusers </span><span class=\"pun\">=</span><span class=\"pln\"> account</span><span class=\"pun\">.</span><span class=\"pln\">get_users</span><span class=\"pun\">()</span><span class=\"pln\">\n</span><span class=\"kwd\">for</span><span class=\"pln\"> user </span><span class=\"kwd\">in</span><span class=\"pln\"> users</span><span class=\"pun\">:</span><span class=\"pln\">\n    </span><span class=\"kwd\">print</span><span class=\"pun\">(</span><span class=\"pln\">user</span><span class=\"pun\">[</span><span class=\"typ\">AdAccountUser</span><span class=\"pun\">.</span><span class=\"typ\">Field</span><span class=\"pun\">.</span><span class=\"pln\">id</span><span class=\"pun\">])</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_m_Ik\" style=\"\"><code><span class=\"typ\">APINodeList</span><span class=\"pun\">&lt;</span><span class=\"typ\">AdAccountUser</span><span class=\"pun\">&gt;</span><span class=\"pln\"> adAccountUsers </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;,</span><span class=\"pln\"> context</span><span class=\"pun\">).</span><span class=\"pln\">getUsers</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">execute</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_n_35\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v2.11/act_&lt;AD_ACCOUNT_ID&gt;/users</span></code></pre></div></div><p></p><p>Get list of accepted Terms of Service, where id is the Facebook terms of service content id:</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_o_9n\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_p_vL\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_q_0J\">Python Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_r_11\">Java Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_s_LV\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_t_CV\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdAccount</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdAccountFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$account </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$account</span><span class=\"pun\">-&gt;</span><span class=\"pln\">read</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdAccountFields</span><span class=\"pun\">::</span><span class=\"pln\">TOS_ACCEPTED</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">));</span><span class=\"pln\">\n\n</span><span class=\"com\">// Dump TOS Accepted info.</span><span class=\"pln\">\nvar_dump</span><span class=\"pun\">(</span><span class=\"pln\">$account</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">AdAccountFields</span><span class=\"pun\">::</span><span class=\"pln\">TOS_ACCEPTED</span><span class=\"pun\">});</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_u_Tl\" style=\"\"><code><span class=\"kwd\">from</span><span class=\"pln\"> facebookads</span><span class=\"pun\">.</span><span class=\"pln\">adobjects</span><span class=\"pun\">.</span><span class=\"pln\">adaccount </span><span class=\"kwd\">import</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pln\">\n\naccount </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">)</span><span class=\"pln\">\naccount</span><span class=\"pun\">.</span><span class=\"pln\">remote_read</span><span class=\"pun\">(</span><span class=\"pln\">fields</span><span class=\"pun\">=[</span><span class=\"typ\">AdAccount</span><span class=\"pun\">.</span><span class=\"typ\">Field</span><span class=\"pun\">.</span><span class=\"pln\">tos_accepted</span><span class=\"pun\">])</span><span class=\"pln\">\n\n</span><span class=\"kwd\">for</span><span class=\"pln\"> tos </span><span class=\"kwd\">in</span><span class=\"pln\"> account</span><span class=\"pun\">[</span><span class=\"typ\">AdAccount</span><span class=\"pun\">.</span><span class=\"typ\">Field</span><span class=\"pun\">.</span><span class=\"pln\">tos_accepted</span><span class=\"pun\">]:</span><span class=\"pln\">\n    </span><span class=\"kwd\">print</span><span class=\"pun\">(</span><span class=\"pln\">tos</span><span class=\"pun\">)</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_v_rQ\" style=\"\"><code><span class=\"typ\">AdAccount</span><span class=\"pln\"> adAccount </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;,</span><span class=\"pln\"> context</span><span class=\"pun\">).</span><span class=\"kwd\">get</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">requestTosAcceptedField</span><span class=\"pun\">()</span><span class=\"pln\">\n  </span><span class=\"pun\">.</span><span class=\"pln\">execute</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_w_If\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=tos_accepted'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v2.11/act_&lt;AD_ACCOUNT_ID&gt;</span></code></pre></div></div><p></p><p></p><h3 id=\"-digital-services-act-saved-beneficiary-payor-information\"> Digital Services Act Saved Beneficiary/Payor Information</h3>\nUse the following code examples to download the beneficiary and payor information.\n<br><br><h4>Android SDK</h4><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">      \n</span><span class=\"typ\">GraphRequest</span><span class=\"pln\"> request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"pln\">newGraphPathRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n accessToken</span><span class=\"pun\">,</span><span class=\"pln\">\n </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n   </span><span class=\"lit\">@Override</span><span class=\"pln\">\n   </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n     </span><span class=\"com\">// Insert your code here</span><span class=\"pln\">\n   </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">});</span><span class=\"pln\">\n\n</span><span class=\"typ\">Bundle</span><span class=\"pln\"> parameters </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\nparameters</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"fields\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"default_dsa_payor,default_dsa_beneficiary\"</span><span class=\"pun\">);</span><span class=\"pln\">\nrequest</span><span class=\"pun\">.</span><span class=\"pln\">setParameters</span><span class=\"pun\">(</span><span class=\"pln\">parameters</span><span class=\"pun\">);</span><span class=\"pln\">\nrequest</span><span class=\"pun\">.</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span><span class=\"pln\">\niOS SDK\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n    initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;\"</span><span class=\"pln\">\n           parameters</span><span class=\"pun\">:@{</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"default_dsa_payor,default_dsa_beneficiary\"</span><span class=\"pun\">,}</span><span class=\"pln\">\n           </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"GET\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\"> id result</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Insert your code here</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span><span class=\"pln\">\n</span><span class=\"typ\">Javascript</span><span class=\"pln\"> SDK</span><span class=\"pun\">:</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">'GET'</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">{</span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"str\">\"default_dsa_payor,default_dsa_beneficiary\"</span><span class=\"pun\">},</span><span class=\"pln\">\n  </span><span class=\"kwd\">function</span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"com\">// Insert your code here</span><span class=\"pln\">\n  </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></pre><h4>cURL</h4><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n</span><span class=\"str\">\"https://graph.facebook.com/</span><code><span class=\"str\">v23.0</span></code><span class=\"str\">/act_&lt;AD_ACCOUNT_ID&gt;?fields=default_dsa_payor%2Cdefault_dsa_beneficiary&amp;access_token=&lt;ACCESS_TOKEN&gt;\"</span><span class=\"pln\">\n      </span></pre>\nThe return value is in JSON format. For example:\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"str\">\"default_dsa_payor\"</span><span class=\"pun\">:</span><span class=\"str\">\"payor2\"</span><span class=\"pun\">,</span><span class=\"str\">\"default_dsa_beneficiary\"</span><span class=\"pun\">:</span><span class=\"str\">\"bene2\"</span><span class=\"pun\">,</span><span class=\"str\">\"id\"</span><span class=\"pun\">:</span><span class=\"str\">\"act_426197654150180\"</span><span class=\"pun\">}</span></pre><p></p></div><div><h3 id=\"parameters\">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The string <code>act_{ad_account_id}</code>.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the Ad Account.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>account_status</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>Status of the account: <br><code>1 = ACTIVE</code><br><code>2 = DISABLED</code><br><code>3 = UNSETTLED</code><br><code>7 = PENDING_RISK_REVIEW</code><br><code>8 = PENDING_SETTLEMENT</code><br><code>9 = IN_GRACE_PERIOD</code><br><code>100 = PENDING_CLOSURE</code><br><code>101 = CLOSED</code><br><code>201 = ANY_ACTIVE</code><br><code>202 = ANY_CLOSED</code><br></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ad_account_promotable_objects</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/graph-api/reference/ad-account-promotable-objects/\">AdAccountPromotableObjects</a></div></td><td><p class=\"_yd\"></p><div><div><p>Ad Account creation request purchase order fields associated with this Ad Account.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>age</code></span></div><div class=\"_yb _yc\"><span>float</span></div></td><td><p class=\"_yd\"></p><div><div><p>Amount of time the ad account has been open, in days.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>agency_client_declaration</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/agency-client-declaration/\">AgencyClientDeclaration</a></div></td><td><p class=\"_yd\"></p><div><div><p>Details of the agency advertising on behalf of this client account, if applicable. Requires Business Manager <a href=\"https://www.facebook.com/business/help/***************?id=***************\">Admin</a> privileges.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>amount_spent</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Current amount spent by the account with respect to <code>spend_cap</code>. Or total amount in the absence of <code>spend_cap</code>. See <a href=\"https://business.facebook.com/business/help/***************?id=****************\">why amount spent is different in ad account spending limit</a> for more info.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>attribution_spec</code></span></div><div class=\"_yb _yc\"><span>list&lt;AttributionSpec&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p><strong>Deprecated due to iOS 14 changes.</strong> Please visit the <a href=\"https://developers.facebook.com/docs/graph-api/changelog/non-versioned-changes/jan-19-2021\">changelog</a> for more information.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>balance</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Bill amount due for this Ad Account.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>brand_safety_content_filter_levels</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Brand safety content filter levels set for in-content ads (Facebook in-stream videos and Ads on Facebook Reels) and Audience Network along with feed ads (Facebook Feed, Instagram feed, Facebook Reels feed and Instagram Reels feed) if applicable.</p>\n\n<p>Refer to <a href=\"https://developers.facebook.com/docs/marketing-api/audiences/reference/placement-targeting/#placement-targeting\">Placement Targeting</a> for a list of supported values.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/business/\">Business</a></div></td><td><p class=\"_yd\"></p><div><div><p>The <a href=\"/docs/marketing-api/businessmanager\">Business Manager</a>, if this ad account is owned by one</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_city</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>City for business address</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_country_code</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Country code for the business address</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The business name for the account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_state</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>State abbreviation for business address</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_street</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>First line of the business street address for the account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_street2</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Second line of the business street address for the account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>business_zip</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Zip code for business address</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>can_create_brand_lift_study</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If we can create a new automated brand lift study under the Ad Account.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>capabilities</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>List of capabilities an Ad Account can have. See <a href=\"/docs/marketing-api/reference/ad-account/capabilities\">capabilities</a></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>created_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>The time the account was created in ISO 8601 format.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>currency</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The currency used for the account, based on the corresponding value in the account settings. See <a href=\"/docs/marketing-api/currencies\">supported currencies</a></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>default_dsa_beneficiary</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>This is the default value for creating L2 object of dsa_beneficiary</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>default_dsa_payor</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>This is the default value for creating L2 object of dsa_payor</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>direct_deals_tos_accepted</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether DirectDeals ToS are accepted.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>disable_reason</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>The reason why the account was disabled. Possible reasons are:<br>\n<code>0 = NONE</code><br>\n<code>1 = ADS_INTEGRITY_POLICY</code><br>\n<code>2 = ADS_IP_REVIEW</code><br>\n<code>3 = RISK_PAYMENT</code><br>\n<code>4 = GRAY_ACCOUNT_SHUT_DOWN</code><br>\n<code>5 = ADS_AFC_REVIEW</code><br>\n<code>6 = BUSINESS_INTEGRITY_RAR</code><br>\n<code>7 = PERMANENT_CLOSE</code><br>\n<code>8 = UNUSED_RESELLER_ACCOUNT</code><br>\n<code>9 = UNUSED_ACCOUNT</code><br>\n<code>10 = UMBRELLA_AD_ACCOUNT</code><br>\n<code>11 = BUSINESS_MANAGER_INTEGRITY_POLICY</code><br>\n<code>12 = MISREPRESENTED_AD_ACCOUNT</code><br>\n<code>13 = AOAB_DESHARE_LEGAL_ENTITY</code><br>\n<code>14 = CTX_THREAD_REVIEW</code><br>\n<code>15 = COMPROMISED_AD_ACCOUNT</code><br></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>end_advertiser</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The entity the ads will target. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>end_advertiser_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The name of the entity the ads will target.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>existing_customers</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The custom audience ids that are used by advertisers to define their existing customers. This definition is primarily used by Automated Shopping Ads.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>expired_funding_source_details</code></span></div><div class=\"_yb _yc\"><span>FundingSourceDetails</span></div></td><td><p class=\"_yd\"></p><div><div><p><code>ID</code> = ID of the payment method<br>\n                                                                                 <code>COUPON</code> = Details of the Facebook Ads Coupon from the payment method<br>\n<code>COUPONS</code> = List of active Facebook Ads Coupon from the ad account<br>\n<code>COUPON_ID</code> = ID of the Facebook Ads Coupon<br>\n                                                                                 <code>AMOUNT</code> = Amount of Facebook Ads Coupon<br>\n                                                                                 <code>CURRENCY</code> = Currency of the Facebook Ads Coupon<br>\n                                                                                 <code>DISPLAY_AMOUNT</code> = How the amount of Facebook Ads Coupon is displayed<br>\n                                                                                 <code>EXPIRATION</code> = When the coupon expired<br>\n                                                                                 <code>START_DATE</code> = When the coupon started<br>\n                                                                                 <code>DISPLAY_STRING</code> = How the payment method is shown<br>\n<code>CAMPAIGN_IDS</code> = List of campaigns the coupon can be applied to, empty if the coupon is applied on the ad account level.<br>\n<code>ORIGINAL_AMOUNT</code> = Amount of Facebook Ads Coupon When Issued<br>\n<code>ORIGINAL_DISPLAY_AMOUNT</code> = How the Facebook Ads Coupon displayed When Issued<br> \n                                                                                 <code>TYPE</code> = Type of the funding source<br>\n                                                                                 <code>0 = UNSET</code><br>\n                                                                                 <code>1 = CREDIT_CARD</code><br>\n                                                                                 <code>2 = FACEBOOK_WALLET</code><br>\n                                                                                 <code>3 = FACEBOOK_PAID_CREDIT</code><br>\n                                                                                 <code>4 = FACEBOOK_EXTENDED_CREDIT</code><br>\n                                                                                 <code>5 = ORDER</code><br>\n                                                                                 <code>6 = INVOICE</code><br>\n                                                                                 <code>7 = FACEBOOK_TOKEN</code><br>\n                                                                                 <code>8 = EXTERNAL_FUNDING</code><br>\n                                                                                 <code>9 = FEE</code><br>\n                                                                                 <code>10 = FX</code><br>\n                                                                                 <code>11 = DISCOUNT</code><br>\n                                                                                 <code>12 = PAYPAL_TOKEN</code><br>\n                                                                                 <code>13 = PAYPAL_BILLING_AGREEMENT</code><br>\n                                                                                 <code>14 = FS_NULL</code><br>\n                                                                                 <code>15 = EXTERNAL_DEPOSIT</code><br>\n                                                                                 <code>16 = TAX</code><br>\n                                                                                 <code>17 = DIRECT_DEBIT</code><br>\n                                                                                 <code>18 = DUMMY</code><br>\n                                                                                 <code>19 = ALTPAY</code><br>\n                                                                                 <code>20 = STORED_BALANCE</code><br></p>\n\n<p>To access this field, the user making the API call must have a <code>MANAGE</code> task permission for that specific ad account. See <a href=\"/docs/marketing-api/reference/ad-account/assigned_users/\">Ad Account, Assigned Users</a> for more information.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>extended_credit_invoice_group</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/extended-credit-invoice-group/\">ExtendedCreditInvoiceGroup</a></div></td><td><p class=\"_yd\"></p><div><div><p>The extended credit invoice group that the ad account belongs to</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>failed_delivery_checks</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/adgroup/deliverychecks/\">list&lt;DeliveryCheck&gt;</a></div></td><td><p class=\"_yd\"></p><div><div><p>Failed delivery checks</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>fb_entity</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>fb_entity</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>funding_source</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID of the payment method. If the account does not have a payment method it will still be possible to create ads but these ads will get no delivery. Not available if the account is disabled</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>funding_source_details</code></span></div><div class=\"_yb _yc\"><span>FundingSourceDetails</span></div></td><td><p class=\"_yd\"></p><div><div><p><code>ID</code> = ID of the payment method<br>\n                                                                                 <code>COUPON</code> = Details of the Facebook Ads Coupon from the payment method<br>\n<code>COUPONS</code> = List of active Facebook Ads Coupon from the ad account<br>\n<code>COUPON_ID</code> = ID of the Facebook Ads Coupon<br>\n                                                                                 <code>AMOUNT</code> = Amount of Facebook Ads Coupon<br>\n                                                                                 <code>CURRENCY</code> = Currency of the Facebook Ads Coupon<br>\n                                                                                 <code>DISPLAY_AMOUNT</code> = How the amount of Facebook Ads Coupon is displayed<br>\n                                                                                 <code>EXPIRATION</code> = When the coupon will expire<br>\n                                                                                 <code>START_DATE</code> = When the coupon starts<br>\n                                                                                 <code>DISPLAY_STRING</code> = How the payment method is shown<br>\n<code>CAMPAIGN_IDS</code> = List of campaigns the coupon can be applied to, empty if the coupon is applied on the ad account level.<br>\n<code>ORIGINAL_AMOUNT</code> = Amount of Facebook Ads Coupon When Issued<br>\n<code>ORIGINAL_DISPLAY_AMOUNT</code> = How the Facebook Ads Coupon displayed When Issued<br> \n                                                                                 <code>TYPE</code> = Type of the funding source<br>\n                                                                                 <code>0 = UNSET</code><br>\n                                                                                 <code>1 = CREDIT_CARD</code><br>\n                                                                                 <code>2 = FACEBOOK_WALLET</code><br>\n                                                                                 <code>3 = FACEBOOK_PAID_CREDIT</code><br>\n                                                                                 <code>4 = FACEBOOK_EXTENDED_CREDIT</code><br>\n                                                                                 <code>5 = ORDER</code><br>\n                                                                                 <code>6 = INVOICE</code><br>\n                                                                                 <code>7 = FACEBOOK_TOKEN</code><br>\n                                                                                 <code>8 = EXTERNAL_FUNDING</code><br>\n                                                                                 <code>9 = FEE</code><br>\n                                                                                 <code>10 = FX</code><br>\n                                                                                 <code>11 = DISCOUNT</code><br>\n                                                                                 <code>12 = PAYPAL_TOKEN</code><br>\n                                                                                 <code>13 = PAYPAL_BILLING_AGREEMENT</code><br>\n                                                                                 <code>14 = FS_NULL</code><br>\n                                                                                 <code>15 = EXTERNAL_DEPOSIT</code><br>\n                                                                                 <code>16 = TAX</code><br>\n                                                                                 <code>17 = DIRECT_DEBIT</code><br>\n                                                                                 <code>18 = DUMMY</code><br>\n                                                                                 <code>19 = ALTPAY</code><br>\n                                                                                 <code>20 = STORED_BALANCE</code><br></p>\n\n<p>To access this field, the user making the API call must have a <code>MANAGE</code> task permission for that specific ad account. See <a href=\"/docs/marketing-api/reference/ad-account/assigned_users/\">Ad Account, Assigned Users</a> for more information.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>has_migrated_permissions</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether this account has migrated permissions</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>has_page_authorized_adaccount</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Indicates whether a Facebook page has authorized this ad account to place ads with political content. If you try to place an ad with political content using this ad account for this page, and this page has not authorized this ad account for ads with political content, your ad will be disapproved. See <a href=\"/docs/graph-api/changelog/breaking-changes#4-23-2018\">Breaking Changes, Marketing API, Ads with Political Content</a> and <a href=\"https://www.facebook.com/policies/ads\">Facebook Advertising Policies</a></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>io_number</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The Insertion Order (IO) number.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_attribution_spec_system_default</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If the attribution specification of ad account is generated from system default values</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_direct_deals_enabled</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether the account is enabled to run Direct Deals</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_in_3ds_authorization_enabled_market</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If the account is in a market requiring to go through payment process going through 3DS authorization</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_notifications_enabled</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Get the notifications status of the user for this ad account. This will return true or false depending if notifications are enabled or not</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_personal</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>Indicates if this ad account is being used for private, non-business purposes. This affects how value-added tax (VAT) is assessed. <b>Note:</b> This is not related to whether an ad account is attached to a business.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_prepay_account</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If this ad account is a prepay. Other option would be a postpay account. <br><br></p>\n\n<p>To access this field, the user making the API call must have a <code>ADVERTISE</code> or <code>MANAGE</code> task permission for that specific ad account. See <a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/assigned_users/\">Ad Account, Assigned Users</a> for more information.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_tax_id_required</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If tax id for this ad account is required or not. <br><br></p>\n\n<p>To access this field, the user making the API call must have a <code>ADVERTISE</code> or <code>MANAGE</code> task permission for that specific ad account. See <a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-account/assigned_users/\">Ad Account, Assigned Users</a> for more information.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>line_numbers</code></span></div><div class=\"_yb _yc\"><span>list&lt;integer&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The line numbers</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>media_agency</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The agency, this could be your own business. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID. In absence of one, you can use <code>NONE</code> or <code>UNFOUND</code>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>min_campaign_group_spend_cap</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The minimum required spend cap of Ad Campaign.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>min_daily_budget</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>The minimum daily budget for this Ad Account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Name of the account. If not set, the name of the first admin visible to the user will be returned.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>offsite_pixels_tos_accepted</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Indicates whether the offsite pixel Terms Of Service contract was signed. This feature can be accessible before v2.9</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>owner</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the account owner</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>partner</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>This could be Facebook Marketing Partner, if there is one. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID. In absence of one, you can use <code>NONE</code> or <code>UNFOUND</code>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>rf_spec</code></span></div><div class=\"_yb _yc\"><span>ReachFrequencySpec</span></div></td><td><p class=\"_yd\"></p><div><div><p>Reach and Frequency limits configuration. <a href=\"/docs/marketing-api/reachandfrequency\">See Reach and Frequency</a></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>show_checkout_experience</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether or not to show the pre-paid checkout experience to an advertiser. If <code>true</code>, the advertiser is eligible for checkout, or they are already locked in to checkout and haven't graduated to postpay.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>spend_cap</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The maximum amount that can be spent by this Ad Account. When the amount is reached, all delivery stops. A value of <code>0</code> means no spending-cap. Setting a new spend cap only applies to spend <strong>AFTER</strong> the time at which you set it. Value specified in basic unit of the currency, for example 'cents' for <code>USD</code>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>tax_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Tax ID</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>tax_id_status</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>VAT status code for the account.<br><code>0</code>: Unknown<br><code>1</code>: VAT not required- US/CA<br><code>2</code>: VAT information required<br><code>3</code>: VAT information submitted<br><code>4</code>: Offline VAT validation failed<br><code>5</code>: Account is a personal account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>tax_id_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Type of Tax ID</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>timezone_id</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>The <a href=\"/docs/marketing-api/reference/ad-account/timezone-ids\">timezone ID</a> of this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>timezone_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Name for the time zone</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>timezone_offset_hours_utc</code></span></div><div class=\"_yb _yc\"><span>float</span></div></td><td><p class=\"_yd\"></p><div><div><p>Time zone difference from UTC (Coordinated Universal Time).</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>tos_accepted</code></span></div><div class=\"_yb _yc\"><span>map&lt;string, int32&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Checks if this specific ad account has signed the Terms of Service contracts. Returns <code>1</code>, if terms were accepted.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>user_tasks</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>user_tasks</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>user_tos_accepted</code></span></div><div class=\"_yb _yc\"><span>map&lt;string, int32&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Checks if a user has signed the Terms of Service contracts related to the Business that contains a specific ad account. Must include user's access token to get information. This verification is not valid for <a href=\"/docs/marketing-api/businessmanager/systemuser\">system users</a>.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"edges\">Edges</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/account_controls/\"><code>account_controls</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAccountBusinessConstraints&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Account Controls is for Advantage+ shopping campaigns where advertisers can set audience controls for minimum age and excluded geo location.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/activities/\"><code>activities</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdActivity&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The activities of this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/adcreatives/\"><code>adcreatives</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdCreative&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ad creatives of this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/\"><code>ads_reporting_mmm_reports</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdsReportBuilderMMMReport&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Marketing mix modeling (MMM) reports generated for this ad account.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/\"><code>ads_reporting_mmm_schedulers</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdsReportBuilderMMMReportScheduler&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Get all MMM report schedulers by this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/advertisable_applications/\"><code>advertisable_applications</code></a></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_x_mI\"><i class=\"img sp_WbXBGqjC54o sx_ba4112\"></i></a></div><div class=\"_yb _yc\"><span>Edge&lt;Application&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>All advertisable apps associated with this account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/applications/\"><code>applications</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Application&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Applications connected to the ad accounts</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/asyncadcreatives/\"><code>asyncadcreatives</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAsyncRequestSet&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The async ad creative creation requests associated with this ad account.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/broadtargetingcategories/\"><code>broadtargetingcategories</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;BroadTargetingCategories&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Broad targeting categories (BCTs) can be used for targeting</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/customaudiencestos/\"><code>customaudiencestos</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;CustomAudiencesTOS&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The custom audiences term of services available to the ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/customconversions/\"><code>customconversions</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;CustomConversion&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The custom conversions owned by/shared with this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/delivery_estimate/\"><code>delivery_estimate</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAccountDeliveryEstimate&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The delivery estimate for a given ad set configuration for this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/\"><code>deprecatedtargetingadsets</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdCampaign&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Ad sets with deprecating targeting options for this ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/dsa_recommendations/\"><code>dsa_recommendations</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAccountDsaRecommendations&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>dsa_recommendations</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/impacting_ad_studies/\"><code>impacting_ad_studies</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdStudy&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ad studies that contain this ad account or any of its descendant ad objects</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/instagram_accounts/\"><code>instagram_accounts</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ShadowIGUser&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Instagram accounts connected to the ad accounts</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/mcmeconversions/\"><code>mcmeconversions</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdsMcmeConversion&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>mcmeconversions</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/minimum_budgets/\"><code>minimum_budgets</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;MinimumBudget&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Returns minimum daily budget values by currency</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/promote_pages/\"><code>promote_pages</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Page&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>All pages that have been promoted under the ad account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/reachestimate/\"><code>reachestimate</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAccountReachEstimate&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The reach estimate of a given <a href=\"/docs/marketing-api/targeting-specs\">targeting spec</a> for this ad                                account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/saved_audiences/\"><code>saved_audiences</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;SavedAudience&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Saved audiences in the account</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/targetingbrowse/\"><code>targetingbrowse</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAccountTargetingUnified&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Unified browse</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-account/targetingsearch/\"><code>targetingsearch</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAccountTargetingUnified&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Unified search</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>3018</td><td>The start date of the time range cannot be beyond 37 months from the current date</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr><tr><td>1150</td><td>An unknown error occurred.</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div><p>To create a new ad account for your business you must specify <code>name</code>, <code>currency</code>, <code>timezone_id</code>, <code>end_advertiser</code>, <code>media_agency</code>, and <code>partner</code>. Provide <code>end_advertiser</code>, <code>media_agency</code>, and <code>partner</code>:</p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\"><p>They must be Facebook Page Aliases, Facebook Page ID or an Facebook app ID. For example, to provide your company as an end advertiser you specify my company or <code>***********</code>.</p></div></li><li><div class=\"fcb\">The End Advertiser ID is the Facebook primary Page ID or Facebook app ID. Further reference to this field (for formatting and acceptable values) may be found <a href=\"/docs/marketing-api/reference/business/adaccount/\">here</a>.</div></li><li><div class=\"fcb\"><p>If your ad account has no End Advertiser, Media Agency, or Partner, specify <code>NONE</code>.</p></div></li><li><div class=\"fcb\"><p>If your ad account has an End Advertiser, Media Agency, or Partner, that are not represented on Facebook by Page or app, specify <code>UNFOUND</code>.</p></div></li></ul><p><b>Once you set <code>end_advertiser</code> to a value other than <code>NONE</code> or <code>UNFOUND</code> you cannot change it.</b></p><p>Create an ad account:\n</p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"name=MyAdAccount\"</span><span class=\"pln\"> \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"currency=USD\"</span><span class=\"pln\"> \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"timezone_id=1\"</span><span class=\"pln\"> \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"end_advertiser=&lt;END_ADVERTISER_ID&gt;\"</span><span class=\"pln\"> \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"media_agency=&lt;MEDIA_AGENCY_ID&gt;\"</span><span class=\"pln\"> \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"partner=NONE\"</span><span class=\"pln\"> \\\n</span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">\"access_token=&lt;ACCESS_TOKEN&gt;\"</span><span class=\"pln\"> \\\n</span><span class=\"str\">\"https://graph.facebook.com/&lt;API_VERSION&gt;/&lt;BUSINESS_ID&gt;/adaccount\"</span></pre><p></p><p>If you have an extended credity line with Facebook, you can set <code>invoice</code> to <code>true</code> and we associate your new ad account to this credit line.</p><p>The response:\n</p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"str\">\"id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"act_&lt;ADACCOUNT_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"account_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;ADACCOUNT_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"business_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;BUSINESS_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"end_advertiser_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;END_ADVERTISER_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"media_agency_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;MEDIA_AGENCY_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"partner_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"NONE\"</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></pre><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>product_audiences</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/product_audiences/\"><code>/act_{ad_account_id}/product_audiences</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> will be created.</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_y_H1\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_z_zr\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_10_+l\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_11_uW\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_12_6K\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_13_BP\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_14_yT\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fproduct_audiences%3Fname%3DTest%2BIphone%2BProduct%2BAudience%26product_set_id%3D%253CPRODUCT_SET_ID%253E%26inclusions%3D%255B%257B%2522retention_seconds%2522%253A86400%252C%2522rule%2522%253A%257B%2522and%2522%253A%255B%257B%2522event%2522%253A%257B%2522eq%2522%253A%2522AddToCart%2522%257D%257D%252C%257B%2522userAgent%2522%253A%257B%2522i_contains%2522%253A%2522iPhone%2522%257D%257D%255D%257D%257D%255D%26exclusions%3D%255B%257B%2522retention_seconds%2522%253A172800%252C%2522rule%2522%253A%257B%2522event%2522%253A%257B%2522eq%2522%253A%2522Purchase%2522%257D%257D%257D%255D&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_15_PX\" style=\"\"><code><span class=\"pln\">POST </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/product_audiences HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com\n\nname</span><span class=\"pun\">=</span><span class=\"typ\">Test</span><span class=\"pun\">+</span><span class=\"typ\">Iphone</span><span class=\"pun\">+</span><span class=\"typ\">Product</span><span class=\"pun\">+</span><span class=\"typ\">Audience</span><span class=\"pun\">&amp;</span><span class=\"pln\">product_set_id</span><span class=\"pun\">=%</span><span class=\"lit\">3CPRODUCT</span><span class=\"pln\">_SET_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">&amp;</span><span class=\"pln\">inclusions</span><span class=\"pun\">=%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22retention</span><span class=\"pln\">_seconds</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A86400</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22rule</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22and</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22event</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22eq</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22AddToCart</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22userAgent</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22i</span><span class=\"pln\">_contains</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22iPhone</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">&amp;</span><span class=\"pln\">exclusions</span><span class=\"pun\">=%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22retention</span><span class=\"pln\">_seconds</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A172800</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22rule</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22event</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22eq</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22Purchase</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_16_3c\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"pln\">post</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences'</span><span class=\"pun\">,</span><span class=\"pln\">\n    array </span><span class=\"pun\">(</span><span class=\"pln\">\n      </span><span class=\"str\">'name'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'Test Iphone Product Audience'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'product_set_id'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'&lt;PRODUCT_SET_ID&gt;'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'inclusions'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'[{\"retention_seconds\":86400,\"rule\":{\"and\":[{\"event\":{\"eq\":\"AddToCart\"}},{\"userAgent\":{\"i_contains\":\"iPhone\"}}]}}]'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'exclusions'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'[{\"retention_seconds\":172800,\"rule\":{\"event\":{\"eq\":\"Purchase\"}}}]'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">),</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_17_HY\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"POST\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"Test Iphone Product Audience\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"product_set_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;PRODUCT_SET_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"inclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":86400,\\\"rule\\\":{\\\"and\\\":[{\\\"event\\\":{\\\"eq\\\":\\\"AddToCart\\\"}},{\\\"userAgent\\\":{\\\"i_contains\\\":\\\"iPhone\\\"}}]}}]\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"exclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":172800,\\\"rule\\\":{\\\"event\\\":{\\\"eq\\\":\\\"Purchase\\\"}}}]\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_18_nd\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"name\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"Test Iphone Product Audience\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"product_set_id\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;PRODUCT_SET_ID&gt;\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"inclusions\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":86400,\\\"rule\\\":{\\\"and\\\":[{\\\"event\\\":{\\\"eq\\\":\\\"AddToCart\\\"}},{\\\"userAgent\\\":{\\\"i_contains\\\":\\\"iPhone\\\"}}]}}]\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"exclusions\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"[{\\\"retention_seconds\\\":172800,\\\"rule\\\":{\\\"event\\\":{\\\"eq\\\":\\\"Purchase\\\"}}}]\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">POST</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_19_hx\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"Test Iphone Product Audience\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"product_set_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"&lt;PRODUCT_SET_ID&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"inclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"[{\\\"retention_seconds\\\":86400,\\\"rule\\\":{\\\"and\\\":[{\\\"event\\\":{\\\"eq\\\":\\\"AddToCart\\\"}},{\\\"userAgent\\\":{\\\"i_contains\\\":\\\"iPhone\\\"}}]}}]\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"exclusions\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"[{\\\"retention_seconds\\\":172800,\\\"rule\\\":{\\\"event\\\":{\\\"eq\\\":\\\"Purchase\\\"}}}]\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"POST\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_1a_I4\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"Test Iphone Product Audience\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'product_set_id=\"&lt;PRODUCT_SET_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'inclusions=[\n       {\n         \"retention_seconds\": 86400,\n         \"rule\": {\n           \"and\": [\n             {\n               \"event\": {\n                 \"eq\": \"AddToCart\"\n               }\n             },\n             {\n               \"userAgent\": {\n                 \"i_contains\": \"iPhone\"\n               }\n             }\n           ]\n         }\n       }\n     ]'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'exclusions=[\n       {\n         \"retention_seconds\": 172800,\n         \"rule\": {\n           \"event\": {\n             \"eq\": \"Purchase\"\n           }\n         }\n       }\n     ]'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/product_audiences</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1b_kb\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>associated_audience_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>creation_params</code></span></div><div class=\"_yb\">dictionary { string : &lt;string&gt; }</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>description</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>enable_fetch_or_create</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>enable_fetch_or_create</p>\n</div></div><p></p></td></tr><tr class=\"row_4 _5m27\"><td><div class=\"_yc\"><span><code>event_sources</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>event_sources</p>\n</div></div><p></p></td></tr><tr class=\"row_4-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_4-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {APP, OFFLINE_EVENTS, PAGE, PIXEL}</div></td><td><p class=\"_yd\"></p><div><div><p>type</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_5 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>exclusions</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_5-0 _5m29 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>booking_window</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-0-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-0-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>count</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-3 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-4 _5m29 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-4-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_5-4-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_5-5 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-6 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-7 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>rule</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_5-8 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6 _5m27\"><td><div class=\"_yc\"><span><code>inclusions</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_6-0 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>booking_window</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-0-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-0-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>count</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-3 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-4 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-4-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>min_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_6-4-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>max_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_6-5 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-6 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>retention_seconds</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-7 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>rule</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_6-8 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>opt_out_link</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>parent_audience_id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>subtype</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div><div class=\"_uoj\"><code>message</code>: string, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>ad_accounts</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/custom-audience/ad_accounts/\"><code>/{custom_audience_id}/ad_accounts</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> will be created.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1c_I6\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>adaccounts</code></span></div><div class=\"_yb\">list&lt;numeric string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Array of new ad account IDs to receive access to the custom audience</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>permissions</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p><code>targeting</code> or <code>targeting_and_insights</code>. If <code>targeting</code> the recipient ad account can target the audience in ads.        <code>targeting_and_insights</code> also allows recipient account to view the        audience in Audience Insights tool</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>relationship_type</code></span></div><div class=\"_yb\">array&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>relationship_type</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>replace</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p><code>true</code> or <code>false</code>. If <code>true</code> the list of <code>adaccounts</code>\n              provided in the call will replace the existing set of ad accounts\n              this audience is shared with.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div><div class=\"_uoj\"><code>sharing_data</code>:  List  [<div class=\"_uoj\"> Struct  {<div class=\"_uoj\"><code>ad_acct_id</code>: string, </div><div class=\"_uoj\"><code>business_id</code>: numeric string, </div><div class=\"_uoj\"><code>audience_share_status</code>: string, </div><div class=\"_uoj\"><code>errors</code>:  List  [<div class=\"_uoj\">string</div>], </div>}</div>], </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>owned_ad_accounts</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/business/owned_ad_accounts/\"><code>/{business_id}/owned_ad_accounts</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> will be created.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1d_ex\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>adaccount_id</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Ad account ID.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>access_status</code>: string, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>3979</td><td>You have exceeded the number of allowed ad accounts for your Business Manager at this time.</td></tr><tr><td>3994</td><td>Personal accounts that do not have any history of activity are not eligible for migration to a business manager. Instead create an ad account inside your business manager.</td></tr><tr><td>3980</td><td>One or more of the ad accounts in your Business Manager are currently in bad standing or in review. All of your accounts must be in good standing in order to create new ad accounts.</td></tr><tr><td>3936</td><td>You've already tried to claim this ad account. You'll see a notification if your request is accepted.</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr><tr><td>3944</td><td>Your Business Manager already has access to this object.</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>adaccount</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/business/adaccount/\"><code>/{business_id}/adaccount</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> will be created.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1e_Sf\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>ad_account_created_from_bm_flag</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>ad_account_created_from_bm_flag</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>currency</code></span></div><div class=\"_yb\">ISO 4217 Currency Code</div></td><td><p class=\"_yd\"></p><div><div><p>The currency used for the account</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>end_advertiser</code></span></div><div class=\"_yb\"></div></td><td><p class=\"_yd\"></p><div><div><p>The entity the ads will target. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID. In absence of one, you can use <code>NONE</code> or <code>UNFOUND</code>. Note that once a value other than <code>NONE</code> or <code>UNFOUND</code> is set, it cannot be modified any more.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>funding_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>ID of the <a href=\"/docs/marketing-api/businessmanager/#invoice-funding\">payment method</a>. If the account does not have a payment method it will still be possible to create ads but these ads will get no delivery.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>invoice</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>If business manager has Business Manager Owned Normal Credit Line on file on the FB CRM, it will attach the ad account to that credit line.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>invoice_group_id</code></span></div><div class=\"_yb\">numeric string</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the invoice group this adaccount should be enrolled in</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>invoicing_emails</code></span></div><div class=\"_yb\">array&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Emails addressed where invoices will be sent.</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>io</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>If corporate channel is direct sales.</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>media_agency</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The agency, this could be your own business. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID. In absence of one, you can use <code>NONE</code> or <code>UNFOUND</code></p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The name of the ad account</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>partner</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The advertising partner for this account, if there is one. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID. In absence of one, you can use <code>NONE</code> or <code>UNFOUND</code>.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>po_number</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Purchase order number</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>timezone_id</code></span></div><div class=\"_yb\">unsigned int32</div></td><td><p class=\"_yd\"></p><div><div><p>ID for the timezone. See <a href=\"/docs/marketing-api/reference/ad-account/timezone-ids\">here</a>.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: token with structure: AdAccount ID, </div><div class=\"_uoj\"><code>account_id</code>: numeric string, </div><div class=\"_uoj\"><code>business_id</code>: numeric string, </div><div class=\"_uoj\"><code>end_advertiser_id</code>: string, </div><div class=\"_uoj\"><code>media_agency_id</code>: string, </div><div class=\"_uoj\"><code>partner_id</code>: string, </div><div class=\"_uoj\"><code>seer_ad_account_restricted_by_soft_desc_challenge</code>: bool, </div><div class=\"_uoj\"><code>soft_desc_challenge_credential_id</code>: string, </div><div class=\"_uoj\"><code>soft_desc_challenge_localized_auth_amount</code>: int32, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3979</td><td>You have exceeded the number of allowed ad accounts for your Business Manager at this time.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>457</td><td>The session has an invalid origin</td></tr><tr><td>3980</td><td>One or more of the ad accounts in your Business Manager are currently in bad standing or in review. All of your accounts must be in good standing in order to create new ad accounts.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>3902</td><td>There was a technical issue and your new ad account wasn't created. Please try again.</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr><tr><td>104</td><td>Incorrect signature</td></tr><tr><td>23007</td><td>This credit card can't be set as your account's primary payment method, because your account is set up to be billed after your ads have delivered. This setup can't be changed. Please try a different card or payment method.</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div><span><p><strong>Notice:</strong></p>\n\n<ul>\n<li>The <code>default_dsa_payor</code> and <code>default_dsa_beneficiary</code> values can be set to both of them or none of them. The API does not allow only one of them to exist in the data storage. </li>\n<li>To unset the values: pass two empty strings at the same time, the values will be unset in the data storage. It does not allow you to unset only one of them. </li>\n</ul>\n</span></div><div class=\"_844_\"><div class=\"_3-98\">You can update an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> by making a POST request to <a href=\"/docs/marketing-api/reference/ad-account/\"><code>/act_{ad_account_id}</code></a>.<div><h3 id=\"parameters-3\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1f_YZ\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>agency_client_declaration</code></span></div><div class=\"_yb\">dictionary { string : &lt;string&gt; }</div></td><td><p class=\"_yd\"></p><div><div><p>Details of the agency advertising on behalf of this client account, if applicable. Requires Business Manager <a href=\"https://www.facebook.com/business/help/***************?id=***************\">Admin</a> privileges.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>attribution_spec</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p><strong>Deprecated due to iOS 14 changes.</strong> Please visit the <a href=\"https://developers.facebook.com/docs/graph-api/changelog/non-versioned-changes/jan-19-2021\">changelog</a> for more information.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event_type</code></span></div><div class=\"_yb\">enum {CLICK_THROUGH, VIEW_THROUGH, ENGAGED_VIDEO_VIEW}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>window_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>business_info</code></span></div><div class=\"_yb\">dictionary { string : &lt;string&gt; }</div></td><td><p class=\"_yd\"></p><div><div><p>Business Info</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>custom_audience_info</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Custom audience info for Automated Shopping Ads.</p>\n</div></div><p></p></td></tr><tr class=\"row_3-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>new_customer_tag</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Label value for new customer in Automated Shoppings Ad's custom audience type URL parameter.</p>\n</div></div><p></p></td></tr><tr class=\"row_3-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>existing_customer_tag</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Label value for existing customer in Automated Shoppings Ad's custom audience type URL parameter.</p>\n</div></div><p></p></td></tr><tr class=\"row_3-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>audience_type_param_name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>field name for audience type in Automated Shoppings Ad's custom audience type UTM parameter.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>default_dsa_beneficiary</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>This is the default value for creating L2 targeting EU's beneficiary.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>default_dsa_payor</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>This is the default value for creating L2 targeting EU's payor.</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>end_advertiser</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The entity the ads will target. Must be a Facebook Page Alias, Facebook Page ID or an Facebook App ID.</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>is_notifications_enabled</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>If notifications are enabled or not for this account</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>media_agency</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Page or Facebook App. Once it is set to any values other than <code>NONE</code> or <code>UNFOUND</code>, it cannot be modified any more</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The name of the ad account</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>partner</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Page or Facebook App. Once it is set to any values other than <code>NONE</code> or <code>UNFOUND</code>, it cannot be modified any more</p>\n</div></div><p></p></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>spend_cap</code></span></div><div class=\"_yb\">float</div></td><td><p class=\"_yd\"></p><div><div><p>The total amount that this account can spend, after which all campaigns will be paused, based on <code>amount_spent</code>. A value of 0 signifies no spending-cap and setting a new spend cap only applies to spend AFTER the time at which you set it. Value specified in standard denomination of the currency, e.g. 23.50 for USD $23.50.</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>spend_cap_action</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Setting this parameter to <code>reset</code> sets the <code>amount_spent</code> back to 0. Setting it to <code>delete</code> removes the <code>spend_cap</code> from the account.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type-2\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-3\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can update an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> by making a POST request to <a href=\"/docs/marketing-api/reference/ad-account/assigned_users/\"><code>/act_{ad_account_id}/assigned_users</code></a>.<div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1g_WH\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>tasks</code></span></div><div class=\"_yb\">array&lt;enum {MANAGE, ADVERTISE, ANALYZE, DRAFT, AA_ANALYZE}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>AdAccount permission tasks to assign this user</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>user</code></span></div><div class=\"_yb\">UID</div></td><td><p class=\"_yd\"></p><div><div><p>Business user id or system user id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2620</td><td>Invalid call to update account permissions</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\"><div class=\"_3-98\">You can dissociate an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> from an&nbsp;<a href=\"/docs/marketing-api/reference/ads-pixel/\">AdsPixel</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/ads-pixel/shared_accounts/\"><code>/{ads_pixel_id}/shared_accounts</code></a>.<div><h3 id=\"parameters-4\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1h_V4\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb\">numeric string</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>business</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>SELF_EXPLANATORY</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3 id=\"return-type-3\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-4\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can dissociate an&nbsp;<a href=\"/docs/marketing-api/reference/ad-account/\">AdAccount</a> from a&nbsp;<a href=\"/docs/marketing-api/reference/custom-audience/\">CustomAudience</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/custom-audience/ad_accounts/\"><code>/{custom_audience_id}/ad_accounts</code></a>.<div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_1i_99\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>adaccounts</code></span></div><div class=\"_yb\">list&lt;numeric string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Array of ad account IDs to revoke access to the custom audience</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/business-manager-api", "/docs/marketing-api/insights-api/ads-volume#breakdown-by-actors", "/docs/marketing-api/businessmanager", "/docs/marketing-api/reference/ad-account/capabilities", "/docs/marketing-api/reachandfrequency", "/docs/marketing-api/reference/ad-account/timezone-ids", "/docs/marketing-api/businessmanager/systemuser", "/docs/marketing-api/targeting-specs", "/docs/marketing-api/reference/business/adaccount/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/custom-audience/ad_accounts/", "/docs/marketing-api/reference/business/owned_ad_accounts/", "/docs/marketing-api/businessmanager/#invoice-funding", "/docs/marketing-api/reference/ads-pixel/", "/docs/marketing-api/reference/ads-pixel/shared_accounts/", "/docs/marketing-api/reference/custom-audience/"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account", "timestamp": "2025-06-25T15:10:04.239Z"}