const axios = require('axios');

async function testOAuthURL() {
  try {
    console.log('🔗 Testing OAuth URL generation...');
    
    const response = await axios.get('http://localhost:3000/api/v1/facebook/oauth-url', {
      params: {
        redirectUri: 'http://localhost:3001/'
      }
    });

    console.log('✅ OAuth URL Response:', {
      success: response.data.success,
      hasOAuthUrl: !!response.data.oauthUrl,
      hasState: !!response.data.state,
      stateFormat: response.data.state?.startsWith('fb_auth_') ? 'Valid' : 'Invalid',
      appId: response.data.appId,
      scopes: response.data.scopes?.length || 0
    });
    
    console.log('🔐 Generated state:', response.data.state);
    console.log('🔗 OAuth URL (truncated):', response.data.oauthUrl?.substring(0, 100) + '...');
    
    return response.data.state;
  } catch (error) {
    console.error('❌ OAuth URL test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testOAuthCallback(state) {
  try {
    console.log('\n🔄 Testing OAuth callback...');
    
    // Test with valid state
    const validResponse = await axios.get('http://localhost:3000/api/v1/facebook/oauth-callback', {
      params: {
        code: 'test_authorization_code_12345',
        state: state
      }
    });

    console.log('✅ Valid callback response:', {
      success: validResponse.data.success,
      message: validResponse.data.message,
      hasCode: !!validResponse.data.code,
      state: validResponse.data.state
    });

    // Test with invalid state
    try {
      await axios.get('http://localhost:3000/api/v1/facebook/oauth-callback', {
        params: {
          code: 'test_authorization_code_12345',
          state: 'invalid_state_parameter'
        }
      });
    } catch (invalidError) {
      console.log('✅ Invalid state properly rejected:', {
        status: invalidError.response?.status,
        error: invalidError.response?.data?.error,
        type: invalidError.response?.data?.type
      });
    }

    // Test with missing code
    try {
      await axios.get('http://localhost:3000/api/v1/facebook/oauth-callback', {
        params: {
          state: state
        }
      });
    } catch (missingCodeError) {
      console.log('✅ Missing code properly rejected:', {
        status: missingCodeError.response?.status,
        error: missingCodeError.response?.data?.error,
        type: missingCodeError.response?.data?.type
      });
    }

  } catch (error) {
    console.error('❌ OAuth callback test failed:', error.response?.data || error.message);
  }
}

async function testSimpleLogin() {
  try {
    console.log('\n🔐 Testing simple login (development mode)...');
    
    const response = await axios.post('http://localhost:3000/api/v1/facebook/login', {}, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Simple login response:', {
      success: response.data.success,
      message: response.data.message,
      user: {
        id: response.data.user?.id,
        name: response.data.user?.name,
        email: response.data.user?.email,
        hasPicture: !!response.data.user?.picture
      },
      hasAccessToken: !!response.data.accessToken,
      loginTime: response.data.loginTime,
      note: response.data.note
    });

  } catch (error) {
    console.error('❌ Simple login test failed:', error.response?.data || error.message);
  }
}

async function testOAuthErrorHandling() {
  try {
    console.log('\n❌ Testing OAuth error handling...');
    
    // Test OAuth error from Facebook
    try {
      await axios.get('http://localhost:3000/api/v1/facebook/oauth-callback', {
        params: {
          error: 'access_denied',
          error_description: 'User denied the request'
        }
      });
    } catch (oauthError) {
      console.log('✅ OAuth error properly handled:', {
        status: oauthError.response?.status,
        error: oauthError.response?.data?.error,
        details: oauthError.response?.data?.details,
        type: oauthError.response?.data?.type
      });
    }

  } catch (error) {
    console.error('❌ OAuth error handling test failed:', error.message);
  }
}

async function runAllOAuthTests() {
  console.log('🧪 Running OAuth Fix Tests...\n');
  
  const state = await testOAuthURL();
  
  if (state) {
    await testOAuthCallback(state);
  }
  
  await testSimpleLogin();
  await testOAuthErrorHandling();
  
  console.log('\n🎉 OAuth Tests Completed!');
  console.log('\n📋 Summary:');
  console.log('✅ OAuth URL Generation - Dynamic state parameters');
  console.log('✅ OAuth Callback - Proper state validation');
  console.log('✅ Simple Login - Development bypass');
  console.log('✅ Error Handling - Comprehensive error responses');
  console.log('✅ Security - State parameter validation prevents CSRF');
}

runAllOAuthTests();
