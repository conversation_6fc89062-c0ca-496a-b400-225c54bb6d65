# Facebook Marketing API - Ad Account DSA Recommendations

## Summary
This endpoint provides DSA (Digital Services Act) recommendations for ad accounts targeting EU regions. It returns predicted beneficiary and payor strings based on recent account activity to help comply with EU DSA requirements.

## Key Points
- Required for ads targeting EU regions to comply with Digital Services Act (DSA)
- Returns predicted beneficiary and payor strings based on account activity
- Predictions are suggestions only - manual review required before campaign publishing
- Read-only endpoint - no create, update, or delete operations supported
- Returns paginated list of AdAccountDsaRecommendations nodes

## API Endpoints
- `GET /v23.0/{ad-account-id}/dsa_recommendations`

## Content
# Ad Account DSA Recommendations

As part of the requirements set forth by the European Union (EU) Digital Services Act (DSA), Facebook requires ads targeting any part of the EU to provide string values defining the beneficiary and payor of the ad being created. This API endpoint outputs a list of strings that Facebook has identified as likely beneficiary/payer values based on recent activity of the ad account.

**Important Note:** While the predicted values often match what advertisers manually input for their DSA Beneficiary/Payor, Facebook doesn't guarantee accuracy. Users should review recommendations before publishing campaigns.

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/dsa_recommendations
```

### Example Request

**HTTP:**
```http
GET /v23.0/{ad-account-id}/dsa_recommendations HTTP/1.1
Host: graph.facebook.com
```

**PHP SDK:**
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/dsa_recommendations',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

**JavaScript SDK:**
```javascript
FB.api(
    "/{ad-account-id}/dsa_recommendations",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Parameters
This endpoint doesn't have any parameters.

### Response Format
```json
{
    "data": [],
    "paging": {}
}
```

#### Fields
- **data**: A list of AdAccountDsaRecommendations nodes
- **paging**: Pagination information (see Graph API guide for details)

### Error Codes
| Error | Description |
|-------|-------------|
| 200   | Permissions error |

## Operations

- **Creating**: Not supported on this endpoint
- **Updating**: Not supported on this endpoint  
- **Deleting**: Not supported on this endpoint

## Examples
GET /v23.0/{ad-account-id}/dsa_recommendations HTTP/1.1

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest implementation

iOS SDK FBSDKGraphRequest implementation

---
**Tags:** DSA, Digital Services Act, EU compliance, Ad Account, Marketing API, Recommendations, Beneficiary, Payor  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/dsa_recommendations/  
**Processed:** 2025-06-25T16:13:59.384Z