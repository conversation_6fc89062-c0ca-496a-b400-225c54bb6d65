{"title": "Facebook Marketing API - Business Role Request Reference", "summary": "Complete reference for the Business Role Request endpoint in Facebook Marketing API v23.0. Covers reading business user invitation requests, updating role assignments, and managing business membership invitations with detailed field descriptions and error handling.", "content": "# Business Role Request\n\n## Overview\n\nRepresents a business user request in the Facebook Marketing API. This endpoint allows admins to view and manage requests for people to join as members of a business.\n\n## Reading Business Role Requests\n\n### Endpoint\n```\nGET /v23.0/{business-role-request-id}\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Response Fields\n\n| Field | Type | Description | Default |\n|-------|------|-------------|----------|\n| `id` | numeric string | Business role invitation request ID | ✓ |\n| `created_by` | BusinessUser\\|SystemUser | User who sent the invitation to join this business | |\n| `created_time` | datetime | Time when admin sent this request | |\n| `email` | string | Email of user invited to join the business | ✓ |\n| `expiration_time` | datetime | When the invitation expires | |\n| `finance_role` | enum | Pre-assigned Finance role for the invitation | |\n| `invited_user_type` | list<enum> | Type of user being invited | |\n| `owner` | Business | The business the user is invited to join | |\n| `role` | enum | Business role assigned to the invited user | ✓ |\n| `status` | enum | Invitation status (accepted, declined, expired, etc.) | ✓ |\n| `updated_by` | BusinessUser\\|SystemUser | User who last updated the invitation | |\n| `updated_time` | datetime | Time when invitation was last updated | |\n\n## Updating Business Role Requests\n\n### Endpoint\n```\nPOST /{business_role_request_id}\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `role` | enum | Update invitation role. Options: FINANCE_EDITOR, FINANCE_ANALYST, ADS_RIGHTS_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER_CENTER_ADMIN, PARTNER_CENTER_ANALYST, PARTNER_CENTER_OPERATIONS, PARTNER_CENTER_MARKETING, PARTNER_CENTER_EDUCATION, MANAGE, DEFAULT, FINANCE_EDIT, FINANCE_VIEW |\n\n### Return Type\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n## Deleting Business Role Requests\n\n### Endpoint\n```\nDELETE /{business_role_request_id}\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Return Type\n```json\n{\n  \"success\": \"bool\"\n}\n```\n\n## Error Codes\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 200 | Permissions error |\n| 368 | Action deemed abusive or disallowed |\n| 415 | Two factor authentication required |\n\n## Notes\n\n- Creating new business role requests is not supported through this endpoint\n- The endpoint supports read-after-write functionality for updates\n- Two-factor authentication may be required when accessing protected business assets", "keyPoints": ["Business Role Request endpoint manages invitations for users to join businesses", "Supports reading, updating, and deleting business role requests but not creating new ones", "Provides detailed invitation status tracking including creation time, expiration, and current status", "Supports multiple business roles including admin, employee, developer, and various partner center roles", "Requires proper permissions and may need two-factor authentication for protected business assets"], "apiEndpoints": ["GET /v23.0/{business-role-request-id}", "POST /{business_role_request_id}", "DELETE /{business_role_request_id}"], "parameters": ["business-role-request-id", "role", "id", "email", "status", "created_by", "created_time", "expiration_time", "finance_role", "invited_user_type", "owner", "updated_by", "updated_time"], "examples": ["GET /v23.0/{business-role-request-id} HTTP/1.1", "POST request to update role parameter", "DELETE request to remove business role request"], "tags": ["Facebook Marketing API", "Business Management", "User Roles", "Invitations", "Graph API", "Business Role Request", "API Reference"], "relatedTopics": ["Business", "BusinessUser", "SystemUser", "Graph API", "Business Management", "User Permissions", "Two-Factor Authentication", "Partner Center Roles"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/business-role-request", "processedAt": "2025-06-25T15:44:41.972Z", "processor": "openrouter-claude-sonnet-4"}