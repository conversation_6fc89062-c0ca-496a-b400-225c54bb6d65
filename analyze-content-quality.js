#!/usr/bin/env node

const ContentQualityAnalyzer = require('./content-quality-analyzer');

async function main() {
  console.log('🔍 Analyzing Facebook Marketing API Documentation Quality...\n');
  
  try {
    const analyzer = new ContentQualityAnalyzer('./output');
    
    // Run the analysis
    const results = await analyzer.analyzeAllContent();
    
    // Generate detailed report
    await analyzer.generateReport(results, './output/content-quality-report.json');
    
    // Display summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 CONTENT QUALITY ANALYSIS SUMMARY');
    console.log('='.repeat(60));
    console.log(`📄 Total pages analyzed: ${results.summary.total}`);
    console.log(`⚠️ Pages needing reprocessing: ${results.summary.needsReprocessing}`);
    console.log(`📈 Average quality score: ${results.summary.averageScore.toFixed(1)}/100`);
    
    if (results.needsReprocessing.length > 0) {
      console.log('\n🔧 Pages with lowest quality scores:');
      results.needsReprocessing.slice(0, 10).forEach((page, index) => {
        console.log(`   ${index + 1}. ${page.filename} (score: ${page.score})`);
        if (page.issues.length > 0) {
          console.log(`      Issues: ${page.issues.slice(0, 2).join(', ')}${page.issues.length > 2 ? '...' : ''}`);
        }
      });
      
      console.log('\n💡 Recommendations:');
      console.log('   1. Run "npm run reprocess" to improve content quality');
      console.log('   2. Check the detailed report at: ./output/content-quality-report.json');
      console.log('   3. Use "npm run reprocess-dry-run" to preview changes first');
    } else {
      console.log('\n✅ All content is high quality! No reprocessing needed.');
    }
    
    console.log('\n📋 Detailed report saved to: ./output/content-quality-report.json');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  }
}

main();
