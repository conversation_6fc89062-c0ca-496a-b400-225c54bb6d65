# Facebook Marketing API - Ad Account Adsets Reference

## Summary
Complete reference documentation for managing ad sets within Facebook ad accounts, including reading, creating, updating, and deleting ad sets through the Marketing API. Covers all parameters, fields, examples, and error codes for ad set operations.

## Key Points
- iOS 14.5 changes affect mobile app custom audiences and app connections targeting
- Either daily_budget or lifetime_budget must be specified when creating ad sets
- Targeting object requires 'countries' field at minimum
- Promoted object requirements vary by campaign objective
- Updating and deleting operations are not supported on this endpoint

## API Endpoints
- `GET /v23.0/act_<AD_ACCOUNT_ID>/adsets`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adsets`

## Parameters
- name
- campaign_id
- targeting
- optimization_goal
- billing_event
- daily_budget
- lifetime_budget
- bid_strategy
- bid_amount
- promoted_object
- status
- start_time
- end_time

## Content
# Ad Account Adsets

## Overview

Due to the iOS 14.5 launch, changes have been made to this endpoint:
- Mobile App Custom Audiences for inclusion targeting is no longer supported for the `POST /{ad-account-id}/adsets` endpoint for iOS 14.5 SKAdNetwork campaigns
- New iOS 14.5 app install campaigns will no longer be able to use app connections targeting

## Reading Ad Sets

Retrieve the ad sets of an ad account.

### Endpoint
```
GET /v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Example Request
```bash
curl -X GET -G \
  -d 'fields="name,id,status"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `date_preset` | enum | Predefined date range for aggregating insights metrics |
| `effective_status` | list<enum> | Filter by effective status (ACTIVE, PAUSED, DELETED, etc.) |
| `is_completed` | boolean | Filter ad sets by completed status |
| `time_range` | object | Custom date range with 'since' and 'until' fields |
| `updated_since` | integer | Time since the ad set has been updated |

### Response Fields

The response returns a JSON object with:
- `data`: Array of AdSet nodes
- `paging`: Pagination information
- `summary`: Aggregated information (insights, total_count)

## Creating Ad Sets

Create a new ad set within an ad account.

### Endpoint
```
POST /v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Example Request
```bash
curl -X POST \
  -F 'name="My First AdSet"' \
  -F 'daily_budget=10000' \
  -F 'bid_amount=300' \
  -F 'billing_event="IMPRESSIONS"' \
  -F 'optimization_goal="REACH"' \
  -F 'campaign_id="<AD_CAMPAIGN_ID>"' \
  -F 'promoted_object={"page_id":"<PAGE_ID>"}' \
  -F 'targeting={"geo_locations":{"countries":["US"]},"age_min":20,"age_max":24}' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `name` | string | Ad set name (max 400 characters) |
| `campaign_id` | numeric string | The ad campaign ID to add this ad set to |
| `targeting` | object | Targeting specifications (countries required) |
| `optimization_goal` | enum | What the ad set optimizes for |
| `billing_event` | enum | The billing event (IMPRESSIONS, CLICKS, etc.) |

### Budget Parameters

Either `daily_budget` or `lifetime_budget` must be specified:
- `daily_budget`: Daily budget in account currency
- `lifetime_budget`: Lifetime budget (requires `end_time`)

### Bidding Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `bid_strategy` | enum | Bidding strategy (LOWEST_COST_WITHOUT_CAP, etc.) |
| `bid_amount` | integer | Bid cap or target cost |

### Targeting Object

The targeting object supports:
- Geographic targeting (`geo_locations`)
- Demographic targeting (`age_min`, `age_max`, `genders`)
- Interest targeting (`flexible_spec`)
- Platform targeting (`publisher_platforms`, `device_platforms`)
- Placement targeting (`facebook_positions`)

### Promoted Object

Required for certain campaign objectives:
- **CONVERSIONS**: `pixel_id`, `custom_event_type`
- **PAGE_LIKES**: `page_id`
- **APP_INSTALLS**: `application_id`, `object_store_url`
- **LEAD_GENERATION**: `page_id`

### Optimization Goals

Supported optimization goals include:
- `APP_INSTALLS`: Optimize for app installations
- `LINK_CLICKS`: Optimize for link clicks
- `IMPRESSIONS`: Maximize impressions
- `REACH`: Optimize for unique reach
- `CONVERSIONS`: Optimize for conversions
- `THRUPLAY`: Optimize for video completion

### Response

Returns an object with:
```json
{
  "id": "<ADSET_ID>",
  "success": true
}
```

## Updating Ad Sets

Updating ad sets is not supported on this endpoint.

## Deleting Ad Sets

Deleting ad sets has been deprecated with Marketing API V8.

## Error Codes

Common error codes:
- `100`: Invalid parameter
- `200`: Permissions error
- `190`: Invalid OAuth 2.0 Access Token
- `80004`: Rate limit exceeded
- `2641`: Restricted locations in targeting
- `368`: Abusive or disallowed action

## Examples
GET request to retrieve ad sets with fields

POST request to create ad set with targeting and budget

Targeting object with geo_locations and demographics

Promoted object for different campaign objectives

---
**Tags:** Facebook Marketing API, Ad Sets, Advertising, Campaign Management, Targeting, Bidding, iOS 14.5
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/
**Processed:** 2025-06-25T15:18:32.994Z