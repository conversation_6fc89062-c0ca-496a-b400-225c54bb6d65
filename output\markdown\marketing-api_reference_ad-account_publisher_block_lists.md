# Ad Account Publisher Block Lists

Graph API Version

[v23.0](#)

# Ad Account Publisher Block Lists

## Reading

You can't perform this operation on this endpoint.

## Creating

You can make a POST request to `publisher_block_lists` edge from the following paths:

*   [`/act_{ad_account_id}/publisher_block_lists`](/docs/marketing-api/reference/ad-account/publisher_block_lists/)

When posting to this edge, a [PublisherBlockList](/docs/marketing-api/reference/publisher-block-list/) will be created.

### Parameters

Parameter

Description

`name`

string

Name of the block list

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

Struct {

`id`: numeric string,

}

### Error Codes

Error

Description

200

Permissions error

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.