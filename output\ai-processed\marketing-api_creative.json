{"title": "Facebook Marketing API - Ad Creative", "summary": "Comprehensive guide to creating and managing ad creatives in the Facebook Marketing API. Covers creative objects, placements, previews, and the complete workflow for building visual ad content.", "content": "# Ad Creative\n\nUse Facebook ads with your existing customers and to reach new ones. Each guide describes Facebook ads products to help meet your advertising goals. There are several types of ad units with a variety of appearances, placement and creative options. For guidelines on ads units as creative content, see [Facebook Ads Guide](https://www.facebook.com/business/ads-guide/?tab0=Mobile%20News%20Feed).\n\n## Creative\n\nAn ad creative is an object that contains all the data for visually rendering the ad itself. In the API, there are different types of ads that you can create on Facebook, all listed [here](/docs/reference/ads-api/adcreative#overview).\n\nIf you have a [campaign](/docs/marketing-api/reference/ad-campaign-group) with the Page Post Engagement Objective, you can now create an ad that promotes a post made by the page. This is considered a Page post ad. Page post ads require a field called `object_story_id`, which is the `id` property of a Page post. Learn more about [Ad Creative, Reference](/docs/reference/ads-api/adcreative#create).\n\nAn ad creative has three parts:\n\n- Ad creative itself, defined by the visual attributes of the creative object\n- Placement that the ad runs on\n- Preview of the unit itself, per placement\n\n### Creating an Ad Creative\n\nTo create the ad creative object, make the following call:\n\n```bash\ncurl -X POST \\\n  -F 'name=\"Sample Promoted Post\"' \\\n  -F 'object_story_id=\"<PAGE_ID>_<POST_ID>\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\nThe response to the API call is the `id` of the creative object. Store this; you need it for the ad object:\n\n```bash\ncurl -X POST \\\n  -F 'name=\"My Ad\"' \\\n  -F 'adset_id=\"<AD_SET_ID>\"' \\\n  -F 'creative={\n       \"creative_id\": \"<CREATIVE_ID>\"\n     }' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads\n```\n\n### Limits\n\nThere are limits on the creative's text, image size, image aspect ratio and other aspects of the creative. See the [Ads Guide](https://www.facebook.com/business/ads-guide).\n\n### Read\n\nIn the Ads API, each field you want to retrieve needs to be asked for explicitly, except for `id`. Each object's Reference has a section for reading back the object and lists what fields are readable. For the creative, it's the same fields as specified when creating the object, and `id`.\n\n```bash\ncurl -G \\\n  -d 'fields=name,object_story_id' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\nhttps://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n## Placements\n\nA placement is where your ad is shown on Facebook, such as on Feed on desktop, Feed on a mobile device or on the right column. See [Ads Product Guide](https://www.facebook.com/business/ads-guide/).\n\nWe encourage you to run ads across the full range of available placements. Facebook's ad auction is designed to deliver ad impressions to the placement most likely to drive campaign results at the lowest possible cost.\n\nThe easiest way to take advantage of this optimization is to leave this field blank. You can also select specific placements in an ad set's target_spec.\n\nThis example has a page post ad. The available placements are Mobile Feed, Desktop Feed and Right column of Facebook. In the API, see [Placement Options](/docs/reference/ads-api/targeting-specs/#placement). If you choose `desktopfeed` and `rightcolumn` as the `page_type`, the ad runs on Desktop Feed and Right column placements.\n\n```bash\ncurl -X POST \\\n  -F 'name=Desktop Ad Set' \\\n  -F 'campaign_id=<CAMPAIGN_ID>' \\\n  -F 'daily_budget=10000' \\\n  -F 'targeting={ \n    \"geo_locations\": {\"countries\":[\"US\"]}, \n    \"publisher_platforms\": [\"facebook\",\"audience_network\"] \n  }' \\\n  -F 'optimization_goal=LINK_CLICKS' \\\n  -F 'billing_event=IMPRESSIONS' \\\n  -F 'bid_amount=1000' \\\n  -F 'status=PAUSED' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n## Preview an Ad\n\nYou preview an ad in one of two ways—with [ad preview API](/docs/reference/ads-api/generatepreview/) or the [ad preview plugin](/docs/reference/ads-api/ad-preview-plugin).\n\nThere are three ways to generate a preview with the API:\n\n1. By ad ID\n2. By ad creative ID\n3. By supplying a creative spec\n\nFollowing the reference docs for the preview API, the minimum required API call is:\n\n```bash\ncurl -G \\\n  --data-urlencode 'creative=\"<CREATIVE_SPEC>\"' \\\n  -d 'ad_format=\"<AD_FORMAT>\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\nThe creative spec is an array of each field and value required to create the ad creative.\n\nTake `object_story_id` and use it in the preview API call:\n\n```bash\ncurl -G \\\n  -d 'creative={\"object_story_id\":\"<PAGE_ID>_<POST_ID>\"}' \\\n  -d 'ad_format=<AD_FORMAT>' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\nFor Desktop Feed and Right column placements, you need to make two API calls:\n\n```bash\ncurl -G \\\n  -d 'creative={\"object_story_id\":\"<PAGE_ID>_<POST_ID>\"}' \\\n  -d 'ad_format=DESKTOP_FEED_STANDARD' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\n```bash\ncurl -G \\\n  -d 'creative={\"object_story_id\":\"<PAGE_ID>_<POST_ID>\"}' \\\n  -d 'ad_format=RIGHT_COLUMN_STANDARD' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\nThe response is an iFrame that's valid for 24 hrs.\n\n## See More\n\n- [Ad Creative](/docs/marketing-api/reference/ad-creative)\n- [Facebook App Ads](/docs/app-ads)\n- [Ads Guide](https://www.facebook.com/business/ads-guide)", "keyPoints": ["Ad creatives contain all visual rendering data for Facebook ads", "Page post ads require an object_story_id field referencing a page post", "Ad creatives have three components: creative object, placement, and preview", "Placements determine where ads appear (desktop feed, mobile feed, right column)", "Ad previews can be generated by ad ID, creative ID, or creative spec"], "apiEndpoints": ["/act_{AD_ACCOUNT_ID}/adcreatives", "/act_{AD_ACCOUNT_ID}/ads", "/act_{AD_ACCOUNT_ID}/adsets", "/act_{AD_ACCOUNT_ID}/generatepreviews", "/{CREATIVE_ID}"], "parameters": ["name", "object_story_id", "creative_id", "adset_id", "status", "campaign_id", "daily_budget", "targeting", "optimization_goal", "billing_event", "bid_amount", "ad_format", "creative", "fields", "access_token"], "examples": ["Creating ad creative with object_story_id", "Creating ad with creative_id reference", "Reading creative fields", "Creating ad set with placement targeting", "Generating ad previews for different formats"], "tags": ["facebook-marketing-api", "ad-creative", "advertising", "placements", "previews", "page-post-ads"], "relatedTopics": ["Ad Campaign Groups", "Ad Sets", "Targeting Specs", "Placement Options", "Ad Preview API", "Facebook Ads Guide", "Page Post Engagement"], "difficulty": "intermediate", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/creative", "processedAt": "2025-06-25T15:06:06.983Z", "processor": "openrouter-claude-sonnet-4"}