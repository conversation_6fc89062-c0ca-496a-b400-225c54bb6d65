{"title": "Facebook Marketing API: Ad Creative", "summary": "This documentation covers the creation and management of ad creatives in the Facebook Marketing API. It explains how to construct ad creatives, specify placements, and generate ad previews across different platforms and formats.", "content": "## Ad Creative Overview\n\nAn ad creative is an object that contains all the visual rendering data for an ad in the Facebook Marketing API. Developers can create various types of ad units with different appearances, placements, and creative options.\n\n### Key Components\n- Ad creative object\n- Placement specifications\n- Ad preview generation\n\n### Creating an Ad Creative\n\n```bash\ncurl -X POST \\\n  -F 'name=\"Sample Promoted Post\"' \\\n  -F 'object_story_id=\"<PAGE_ID>_<POST_ID>\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Placement Options\nFacebook supports multiple placements including:\n- Mobile Feed\n- Desktop Feed\n- Right column\n\n### Ad Preview Generation\n\n```bash\ncurl -G \\\n  --data-urlencode 'creative=\"<CREATIVE_SPEC>\"' \\\n  -d 'ad_format=\"<AD_FORMAT>\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```", "keyPoints": ["Ad creatives define the visual attributes of an advertisement", "Supports multiple ad placements and formats", "Requires object_story_id for page post ads", "Previews can be generated for different platforms", "Follows Facebook Graph API versioning"], "apiEndpoints": ["/act_{AD_ACCOUNT_ID}/adcreatives", "/act_{AD_ACCOUNT_ID}/generatepreviews"], "parameters": ["name", "object_story_id", "access_token", "ad_format", "creative_spec"], "examples": ["Creating a page post ad", "Generating ad previews for different placements", "Reading ad creative details"], "tags": ["Marketing API", "Ad Creatives", "Facebook Advertising", "API Reference"], "relatedTopics": ["Page Post Engagement", "Ad Placements", "Ad Preview Generation", "Marketing API Objectives"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/creative", "processedAt": "2025-06-25T14:58:02.060Z", "processor": "openrouter-claude-3.5-haiku"}