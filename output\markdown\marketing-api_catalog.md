# Catalog

# Catalog

A Facebook catalog is an object (or container) of information about your products and where you can upload your inventory. Learn more about [product catalog](/docs/marketing-api/catalog/overview).

## Common Uses

*   **[Collection Ads](https://developers.facebook.com/docs/marketing-api/guides/collection)** — Use them in immersive formats.
*   **[Commerce](https://developers.facebook.com/docs/commerce-platform/catalog/)** — Distribute products in Marketplace.
*   **[Advantage+ Catalog Ads](https://developers.facebook.com/docs/marketing-api/dynamic-ads)** — Feature products in different formats to be served dynamically as personalized ads.
*   **Instagram Shopping** — Feature in Instagram Shopping experiences, such as product tags on Instagram and soon on Instagram Shops.
*   **WhatsApp** — Feature in conversational commerce in WhatsApp.

## Documentation Contents

### [Overview](/docs/marketing-api/catalog/overview)

Learn more about catalog and its components.

### [Get Started](/docs/marketing-api/catalog/getting-started)

Learn how to successfully set up a catalog for commerce or Advantage+ catalog ads, and more.

### [Guides](/docs/marketing-api/catalog/guides)

Learn more about the various guides and how to use them in your catalog.

### [Best Practices](https://developers.facebook.com/docs/marketing-api/catalog/best-practices)

Tips for using catalog effectively.

### [Reference](/docs/marketing-api/catalog/reference)

Product specifications and endpoint references.

### [Support](/docs/marketing-api/catalog/support)

Solutions to common problems and troubleshooting tips.

## See Also

*   [Catalog Batch API](/docs/marketing-api/catalog-batch)