# Facebook Marketing API - Ad Creative Reference

## Summary
Complete reference for the Ad Creative object in Facebook Marketing API, which provides layout and content formatting for ads. Covers creation, reading, updating, and deletion of ad creatives with detailed field specifications and examples.

## Key Points
- Ad Creative objects define the layout and content for Facebook ads and are stored in the ad account's creative library
- Political ads require special authorization_category settings and compliance with Facebook's advertising policies
- Content has strict character limits and formatting rules, with different restrictions for link ads vs page post ads
- Inline page post creation allows creating unpublished posts directly within the ad creative using object_story_spec
- The API supports various ad formats including link ads, carousel ads, video ads, and dynamic ads with specific field requirements

## API Endpoints
- `GET /v23.0/<CREATIVE_ID>`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives`
- `POST /v23.0/<CREATIVE_ID>`
- `DELETE /v23.0/<CREATIVE_ID>`
- `GET /v23.0/<CREATIVE_ID>/previews`

## Parameters
- object_story_spec
- object_story_id
- authorization_category
- name
- image_hash
- image_url
- video_id
- call_to_action_type
- asset_feed_spec
- platform_customizations
- thumbnail_width
- thumbnail_height
- product_set_id
- url_tags
- status

## Content
# Facebook Marketing API - Ad Creative Reference

## Overview

The Ad Creative object provides layout and contains content for Facebook ads. It defines the creative field of one or more ads and is stored in your ad account's creative library for reuse.

### Important Deprecation Notice

The `instagram_actor_id` field for `act_<AD_ACCOUNT_ID>/adcreatives` has been deprecated for v22.0 and will be deprecated for all versions January 20, 2026. Migrate to use the `instagram_user_id` field instead.

## Special Requirements

### Political Ads

Advertisers running ads about social issues, elections, and politics must:
- Specify `special_ad_categories` while creating an ad campaign
- Set `authorization_category` to flag at the ad creative level
- Use `POLITICAL_WITH_DIGITALLY_CREATED_MEDIA` for digitally created/altered media (effective January 9, 2024)

## Limits and Restrictions

### General Limits
- Maximum 50,000 ad creatives returned (pagination unavailable beyond this)
- Ad title: 1-25 characters (recommended)
- Ad body: 1-90 characters (recommended)
- URL length: 1000 characters maximum
- Individual word length: 30 characters (recommended)

### Content Rules
- Cannot start with punctuation: `\ / ! . ? - * ( ) , ; :`
- No consecutive punctuation except three dots `...`
- Maximum three 1-character words allowed
- Prohibited characters: IPA symbols (with exceptions), standalone diacritical marks, superscript/subscript (except ™ and ℠), and `^~_={}[]|<>`

### Placement Restrictions
- Link Ads: Cannot use special characters
- Page Post Ads: Allow special characters like `★`

## API Operations

### Reading Ad Creatives

```bash
curl -G \
  -d 'fields=name,object_story_id' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

#### Reading Thumbnails

```bash
curl -G \
  -d 'thumbnail_width=150' \
  -d 'thumbnail_height=120' \
  -d 'fields=thumbnail_url' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

### Creating Ad Creatives

#### Basic Link Ad

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "link_data": { 
      "image_hash": "<IMAGE_HASH>", 
      "link": "<URL>", 
      "message": "try it out" 
    }, 
    "page_id": "<PAGE_ID>" 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Link Ad with Call to Action

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "link_data": { 
      "call_to_action": {"type":"SIGN_UP","value":{"link":"<URL>"}}, 
      "link": "<URL>", 
      "message": "try it out" 
    }, 
    "page_id": "<PAGE_ID>" 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Carousel Ad

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "link_data": { 
      "child_attachments": [ 
        { 
          "description": "$8.99", 
          "image_hash": "<IMAGE_HASH>", 
          "link": "https://www.link.com/product1", 
          "name": "Product 1", 
          "video_id": "<VIDEO_ID>" 
        }, 
        { 
          "description": "$9.99", 
          "image_hash": "<IMAGE_HASH>", 
          "link": "https://www.link.com/product2", 
          "name": "Product 2", 
          "video_id": "<VIDEO_ID>" 
        } 
      ], 
      "link": "<URL>" 
    }, 
    "page_id": "<PAGE_ID>" 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Political Ad Creative

```bash
curl \
  -F 'authorization_category=POLITICAL' \
  -F 'object_story_spec={...}' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

#### Video Page Like Ad

```bash
curl \
  -F 'name=Sample Creative' \
  -F 'object_story_spec={ 
    "page_id": "<PAGE_ID>", 
    "video_data": { 
      "call_to_action": {"type":"LIKE_PAGE","value":{"page":"<PAGE_ID>"}}, 
      "image_url": "<THUMBNAIL_URL>", 
      "video_id": "<VIDEO_ID>" 
    } 
  }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Updating Ad Creatives

```bash
curl \
  -F 'name=New creative name' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

### Deleting Ad Creatives

```bash
curl -X DELETE \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<CREATIVE_ID>/
```

## Key Fields

### Core Fields
- `id`: Unique numeric string identifier
- `account_id`: Ad account ID
- `name`: Creative name (100 character limit)
- `status`: ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED

### Content Fields
- `object_story_spec`: Specification for creating unpublished page posts
- `object_story_id`: ID of existing page post to use
- `body`: Ad body text (not supported for video posts)
- `title`: Title for link ads
- `call_to_action_type`: Type of CTA button

### Media Fields
- `image_hash`: Image from ad account's library
- `image_url`: URL for new image to save to library
- `video_id`: Facebook video object ID
- `image_crops`: JSON object defining crop dimensions

### Targeting and Placement
- `authorization_category`: Political ad labeling
- `platform_customizations`: Media for specific placements
- `asset_feed_spec`: Dynamic Creative asset feed

### Special Features
- `branded_content_sponsor_page_id`: For branded content ads
- `dynamic_ad_voice`: For store traffic in dynamic ads
- `product_set_id`: For dynamic product ads
- `url_tags`: Query parameters for clicked URLs

## Partnership Ads

For partnership ads, set sponsor information:
- `facebook_branded_content.sponsor_page_id`: Facebook sponsor page
- `instagram_branded_content.sponsor_id`: Instagram sponsor user ID

## Error Codes

- `100`: Invalid parameter
- `190`: Invalid OAuth 2.0 Access Token
- `200`: Permissions error
- `270`: Development access level restriction
- `2500`: Error parsing graph query
- `80004`: Too many calls to ad account (rate limiting)

## Examples
Basic link ad creation with image and message

Carousel ad with multiple products and descriptions

Political ad creative with authorization_category

Video page like ad with call to action

Reading ad creative with thumbnail specifications

Partnership ads with branded content sponsor settings

Adding URL tags to track clicked URLs

---
**Tags:** facebook-marketing-api, ad-creative, advertising, creative-management, api-reference, political-ads, carousel-ads, video-ads, branded-content
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-creative
**Processed:** 2025-06-25T15:51:28.699Z