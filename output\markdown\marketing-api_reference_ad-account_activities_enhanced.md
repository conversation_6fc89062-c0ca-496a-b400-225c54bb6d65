# Facebook Marketing API - Ad Account Activities Reference

## Summary
Reference documentation for the Ad Account Activities endpoint in Facebook's Marketing API. This endpoint allows reading activities related to an ad account but does not support create, update, or delete operations.

## Key Points
- This endpoint only supports READ operations - no create, update, or delete functionality
- Returns a list of AdActivity nodes with standard pagination
- No parameters are required for this endpoint
- Rate limiting applies with specific error code 80004 for too many calls
- Requires proper OAuth 2.0 access token for authentication

## API Endpoints
- `/ad-account/activities`

## Content
# Ad Account Activities

## Overview

The Ad Account Activities endpoint provides access to activities related to an Ad Account in the Facebook Marketing API.

## Reading

Activities related to an Ad Account can be retrieved through this endpoint.

### Parameters

This endpoint doesn't have any parameters.

### Response Format

Reading from this edge will return a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

#### `data`

A list of [AdActivity](/docs/marketing-api/reference/ad-activity/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

| Error | Description |
|-------|-------------|
| 200 | Permissions error |
| 80004 | There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management. |
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 368 | The action attempted has been deemed abusive or is otherwise disallowed |

## Creating

You can't perform this operation on this endpoint.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.

## API Version

Graph API Version: v23.0

## Examples
{
    "data": [],
    "paging": {}
}

---
**Tags:** Facebook Marketing API, Ad Account, Activities, Graph API, Read-only endpoint  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/activities/  
**Processed:** 2025-06-25T16:10:54.243Z