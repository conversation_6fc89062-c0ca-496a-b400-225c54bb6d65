const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('https://developers.facebook.com/docs/marketing-apis');
  await page.waitForLoadState('networkidle');
  
  console.log('Testing final extraction approach...');
  
  // Test the exact approach our scraper should use
  const content = await page.evaluate(() => {
    // Get all the main documentation sections
    const sections = document.querySelectorAll('._4-u2 ._4-u3');
    console.log('Found sections:', sections.length);
    
    if (sections.length > 0) {
      const combinedContent = Array.from(sections).map(section => {
        const clone = section.cloneNode(true);
        
        // Remove unwanted elements from each section
        const toRemove = [
          '.hidden_elem',
          '[data-click-area="to_top_nav"]',
          'script',
          'noscript', 
          'style',
          '._2k32',
          'fb\\:like',
          '.img',
          '[data-visualcompletion="css-img"]'
        ];
        
        toRemove.forEach(selector => {
          try {
            const elements = clone.querySelectorAll(selector);
            console.log(`Removing ${elements.length} elements with selector: ${selector}`);
            elements.forEach(el => el.remove());
          } catch (e) {
            console.log(`Error with selector ${selector}:`, e.message);
          }
        });
        
        return clone.innerHTML;
      }).join('\n\n');
      
      return combinedContent;
    }
    
    return 'No sections found';
  });
  
  console.log('Content length:', content.length);
  console.log('Content preview (first 300 chars):');
  console.log(content.substring(0, 300));
  console.log('\nContent preview (chars 300-600):');
  console.log(content.substring(300, 600));
  
  await browser.close();
})();
