# Facebook Marketing API Documentation Scraper

A comprehensive Playwright-based scraper for the Facebook Marketing API documentation that extracts content and saves it in multiple formats for AI agent consumption.

## Features

- 🚀 **Comprehensive Coverage**: Scrapes all Facebook Marketing API documentation pages
- 📄 **Multiple Formats**: Saves content as JSON, HTML, and Markdown
- 🔄 **Resume Capability**: Can resume interrupted scraping sessions
- 🔍 **URL Discovery**: Automatically discovers additional documentation URLs
- 📊 **Progress Tracking**: Real-time progress bar and detailed logging
- 🛡️ **Error Handling**: Robust retry logic and error recovery
- 📋 **Manifest Generation**: Creates an index of all scraped content
- 🧪 **Test Mode**: Test the scraper on a few pages before full run

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Install Playwright browsers:**
   ```bash
   npm run install-playwright
   ```

## Usage

### Full Scraping
```bash
npm run scrape
```

### Test Mode (scrape only a few pages)
```bash
npm run test
```

### Direct Node.js execution
```bash
# Full scraping
node scraper.js

# Test mode
node scraper.js --test
```

## Output Structure

The scraper creates the following directory structure:

```
output/
├── json/           # JSON files with full page data
├── html/           # Raw HTML content
├── markdown/       # Converted Markdown files
├── manifest.json   # Index of all scraped pages
└── progress.json   # Scraping progress (for resuming)
```

### JSON Format
Each page is saved as a JSON file containing:
```json
{
  "title": "Page Title",
  "breadcrumbs": [...],
  "content": "HTML content",
  "navigationLinks": [...],
  "url": "https://developers.facebook.com/docs/...",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Manifest Format
The manifest provides an overview of all scraped content:
```json
{
  "totalPages": 67,
  "pages": [
    {
      "filename": "marketing-apis.json",
      "title": "Marketing API",
      "url": "https://developers.facebook.com/docs/marketing-apis",
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  ],
  "generatedAt": "2024-01-01T00:00:00.000Z"
}
```

## Configuration

Edit `config.js` to customize:

- **URLs**: Add or remove documentation URLs to scrape
- **Delays**: Adjust request delays to be respectful to the server
- **Selectors**: Modify CSS selectors for content extraction
- **Output**: Change output directory and file formats
- **Browser**: Configure browser settings (headless mode, user agent, etc.)

## Key Configuration Options

```javascript
settings: {
  outputDir: './output',     // Output directory
  delay: 1000,              // Delay between requests (ms)
  timeout: 30000,           // Page timeout (ms)
  retries: 3,               // Number of retries for failed requests
  headless: true,           // Run browser in headless mode
}
```

## Resume Functionality

The scraper automatically saves progress and can resume from where it left off:

1. If scraping is interrupted, simply run the scraper again
2. It will skip already completed pages
3. Failed pages will be retried
4. Progress is saved every 10 completed pages

## Error Handling

- **Automatic Retries**: Failed pages are retried up to 3 times
- **Graceful Degradation**: Continues scraping even if some pages fail
- **Detailed Logging**: All errors are logged with context
- **Progress Preservation**: Progress is saved even if the scraper crashes

## Content Extraction

The scraper intelligently extracts:

- **Main Content**: Uses multiple CSS selectors to find the primary content
- **Page Titles**: Extracts meaningful page titles
- **Breadcrumbs**: Captures navigation breadcrumbs
- **Navigation Links**: Discovers additional URLs to scrape

## Rate Limiting

The scraper is configured to be respectful:

- 1-second delay between requests by default
- Waits for network idle before extracting content
- Uses realistic browser user agent
- Implements exponential backoff for retries

## Troubleshooting

### Common Issues

1. **Browser Installation**: Run `npm run install-playwright` if browsers are missing
2. **Network Timeouts**: Increase timeout in config.js for slow connections
3. **Memory Issues**: Run in headless mode and increase system memory
4. **Rate Limiting**: Increase delay between requests in config.js

### Debug Mode

To run with more verbose output:
```bash
DEBUG=* node scraper.js
```

## AI Agent Integration

The scraped content is optimized for AI agent consumption:

- **Structured JSON**: Easy to parse and process
- **Clean Markdown**: Human-readable format for training
- **Metadata**: Includes URLs, timestamps, and navigation context
- **Manifest**: Provides overview for content indexing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with `npm run test`
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Disclaimer

This scraper is for educational and research purposes. Please respect Facebook's robots.txt and terms of service. Use responsibly and consider the impact on their servers.
