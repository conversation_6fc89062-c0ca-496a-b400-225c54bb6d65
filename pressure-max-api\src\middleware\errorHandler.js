const logger = require('../config/logger');
const config = require('../config/config');

class AppError extends Error {
  constructor(message, statusCode, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';

    Error.captureStackTrace(this, this.constructor);
  }
}

// Custom error classes
class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400);
    this.errors = errors;
    this.type = 'validation';
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401);
    this.type = 'authentication';
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Insufficient permissions') {
    super(message, 403);
    this.type = 'authorization';
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404);
    this.type = 'not_found';
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409);
    this.type = 'conflict';
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429);
    this.type = 'rate_limit';
  }
}

class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(message, 502);
    this.service = service;
    this.type = 'external_service';
  }
}

// Error handler middleware
const errorHandler = (error, req, res, next) => {
  let err = { ...error };
  err.message = error.message;

  // Log error
  logger.logError(error, {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    body: req.body,
    params: req.params,
    query: req.query
  });

  // Mongoose bad ObjectId
  if (error.name === 'CastError') {
    const message = 'Invalid resource ID';
    err = new ValidationError(message);
  }

  // Mongoose duplicate key
  if (error.code === 11000) {
    const field = Object.keys(error.keyValue)[0];
    const message = `${field} already exists`;
    err = new ConflictError(message);
  }

  // Mongoose validation error
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors).map(val => ({
      field: val.path,
      message: val.message
    }));
    err = new ValidationError('Validation failed', errors);
  }

  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    err = new AuthenticationError('Invalid token');
  }

  if (error.name === 'TokenExpiredError') {
    err = new AuthenticationError('Token expired');
  }

  // PostgreSQL errors
  if (error.code === '23505') { // Unique violation
    const match = error.detail?.match(/Key \((.+)\)=\((.+)\) already exists/);
    const field = match ? match[1] : 'field';
    err = new ConflictError(`${field} already exists`);
  }

  if (error.code === '23503') { // Foreign key violation
    err = new ValidationError('Referenced resource does not exist');
  }

  if (error.code === '23502') { // Not null violation
    const field = error.column || 'field';
    err = new ValidationError(`${field} is required`);
  }

  // Stripe errors
  if (error.type && error.type.startsWith('Stripe')) {
    err = new ExternalServiceError('Stripe', error.message);
  }

  // Facebook API errors
  if (error.response?.data?.error) {
    const fbError = error.response.data.error;
    err = new ExternalServiceError('Facebook', fbError.message);
  }

  // VAPI errors
  if (error.response?.config?.baseURL?.includes('vapi.ai')) {
    err = new ExternalServiceError('VAPI', error.message);
  }

  // Default to 500 server error
  if (!err.statusCode) {
    err = new AppError('Something went wrong', 500, false);
  }

  // Send error response
  const response = {
    status: err.status || 'error',
    message: err.message
  };

  // Add additional error details in development
  if (config.server.env === 'development') {
    response.error = err;
    response.stack = err.stack;
  }

  // Add validation errors if present
  if (err.errors) {
    response.errors = err.errors;
  }

  // Add error type
  if (err.type) {
    response.type = err.type;
  }

  // Add service name for external service errors
  if (err.service) {
    response.service = err.service;
  }

  // Add request ID for tracking
  response.requestId = req.id || req.headers['x-request-id'];

  res.status(err.statusCode || 500).json(response);
};

// Async error wrapper
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

module.exports = {
  errorHandler,
  asyncHandler,
  notFoundHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError
};
