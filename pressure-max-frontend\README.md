# Pressure Max API Testing Interface

A comprehensive React-based frontend for testing and demonstrating the Pressure Max API backend functionality.

## 🚀 Features

### ✅ Authentication Section
- **User Registration** - Complete signup form with validation
- **User Login** - Secure login with JWT token management
- **Authentication Status** - Real-time display of login state
- **JWT Token Display** - Debug view of current access token
- **Logout Functionality** - Secure token cleanup

### ✅ Facebook Integration Section
- **OAuth Flow** - Initiate Facebook Business Manager connection
- **Connection Status** - Visual indicator of Facebook account status
- **Ad Accounts Display** - List connected ad accounts with details
- **Pages Display** - Show available Facebook pages
- **Error Handling** - Clear feedback for connection issues

### ✅ Campaign Management Section
- **Campaign List** - View existing campaigns with status
- **Create Campaign** - Form for new campaign creation with:
  - Campaign name input
  - Objective dropdown (10+ Facebook objectives)
  - Special ad categories selection
  - Status management
- **Account Selection** - Choose from available ad accounts
- **Real-time Updates** - Live campaign status monitoring

### ✅ API Testing & Monitoring
- **Server Health Check** - Monitor API backend status
- **User Profile Display** - Show current user information
- **Request/Response Logs** - Real-time API call monitoring
- **Quick API Tests** - One-click endpoint testing
- **Error Tracking** - Comprehensive error logging

## 🛠️ Technical Stack

- **React 18** - Modern React with hooks
- **Axios** - HTTP client with interceptors
- **React Hook Form** - Form validation and management
- **React Hot Toast** - User notifications
- **Lucide React** - Modern icon library
- **CSS3** - Custom responsive styling

## 📋 Prerequisites

- Node.js 16+ 
- Pressure Max API backend running on `localhost:3000`
- Modern web browser

## 🚀 Quick Start

1. **Install Dependencies:**
   ```bash
   cd pressure-max-frontend
   npm install
   ```

2. **Start Development Server:**
   ```bash
   npm start
   ```

3. **Open Browser:**
   Navigate to `http://localhost:3001`

## 🔧 Configuration

The frontend is configured to connect to the API backend at `http://localhost:3000`. This is set in:

- `package.json` - Proxy configuration
- `src/services/api.js` - API base URL

To change the backend URL, update the `API_BASE_URL` in `src/services/api.js`.

## 📱 Interface Sections

### 1. Authentication Section
```
┌─────────────────────────────────┐
│ 🔐 Authentication               │
├─────────────────────────────────┤
│ [Login] [Register]              │
│                                 │
│ Email: ________________         │
│ Password: _____________         │
│ [Login Button]                  │
│                                 │
│ ✅ Logged in as John Doe        │
│ 👁️ JWT Token: eyJ0eXAi...       │
│ [Logout]                        │
└─────────────────────────────────┘
```

### 2. Facebook Integration Section
```
┌─────────────────────────────────┐
│ 📘 Facebook Integration         │
├─────────────────────────────────┤
│ Status: ✅ Connected            │
│                                 │
│ 💳 Ad Accounts (2)              │
│ • Business Account 1            │
│ • Business Account 2            │
│                                 │
│ 👥 Pages (3)                    │
│ • Company Page                  │
│ • Product Page                  │
│ • Service Page                  │
└─────────────────────────────────┘
```

### 3. Campaign Management Section
```
┌─────────────────────────────────┐
│ 🎯 Campaign Management          │
├─────────────────────────────────┤
│ Account: [Business Account 1 ▼] │
│                                 │
│ Campaigns (5) [+ Create]        │
│                                 │
│ • Summer Sale Campaign          │
│   Status: Active | Reach        │
│   Created: 2024-01-15           │
│                                 │
│ • Lead Gen Campaign             │
│   Status: Paused | Lead Gen     │
│   Budget: $50/day               │
└─────────────────────────────────┘
```

### 4. API Testing Section
```
┌─────────────────────────────────┐
│ 🔍 API Testing & Monitoring     │
├─────────────────────────────────┤
│ 🖥️ Server Health: ✅ Healthy    │
│ Uptime: 45 minutes              │
│                                 │
│ 👤 User Profile                 │
│ ID: user_123                    │
│ Name: John Doe                  │
│ Role: user                      │
│                                 │
│ 📊 API Logs (10)                │
│ GET /health 200 ✅              │
│ GET /users/profile 200 ✅       │
│ POST /auth/login 200 ✅         │
└─────────────────────────────────┘
```

## 🔐 Authentication Flow

1. **Registration/Login** - User enters credentials
2. **JWT Storage** - Tokens stored in localStorage
3. **Auto-Refresh** - Automatic token refresh on expiry
4. **Request Interceptor** - Auto-attach Bearer token
5. **Logout** - Clean token removal and blacklisting

## 📡 API Integration

### Automatic Token Management
```javascript
// Request interceptor adds auth header
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor handles token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Auto-refresh token logic
    }
    return Promise.reject(error);
  }
);
```

### Error Handling
- **Network Errors** - Connection failure feedback
- **Authentication Errors** - Auto-redirect to login
- **Validation Errors** - Field-specific error messages
- **Server Errors** - User-friendly error notifications

## 🎨 Styling & UI

### Responsive Design
- **Mobile-first** approach
- **Flexbox/Grid** layouts
- **Breakpoints** for tablet and desktop
- **Touch-friendly** buttons and inputs

### Color Scheme
- **Primary**: Purple gradient (`#667eea` to `#764ba2`)
- **Success**: Green (`#10b981`)
- **Error**: Red (`#ef4444`)
- **Warning**: Orange (`#f59e0b`)
- **Neutral**: Gray shades

### Components
- **Cards** - Sectioned content areas
- **Forms** - Consistent input styling
- **Buttons** - Multiple variants with hover states
- **Status Indicators** - Visual connection states
- **Loading States** - Spinner and skeleton screens

## 🧪 Testing Features

### Manual Testing
- **Form Validation** - Test required fields and formats
- **API Endpoints** - Quick test buttons for each endpoint
- **Error Scenarios** - Simulate network failures
- **Authentication** - Test login/logout flows

### Debug Information
- **JWT Token Display** - View current token
- **API Request Logs** - See all HTTP requests/responses
- **Console Logging** - Detailed debug information
- **Network Tab** - Browser dev tools integration

## 🚀 Deployment

### Development
```bash
npm start
# Runs on http://localhost:3001
```

### Production Build
```bash
npm run build
# Creates optimized build in /build folder
```

### Environment Variables
Create `.env` file for custom configuration:
```env
REACT_APP_API_URL=http://localhost:3000
REACT_APP_ENVIRONMENT=development
```

## 🔧 Customization

### Adding New Sections
1. Create component in `src/components/`
2. Add to `src/App.js`
3. Update CSS in `src/App.css`
4. Add API calls in `src/services/api.js`

### Styling Changes
- Modify `src/App.css` for global styles
- Component-specific styles in component files
- CSS variables for consistent theming

## 📚 API Documentation

When the backend is running, visit:
- **Swagger UI**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Troubleshooting

### Common Issues

**API Connection Failed**
- Ensure backend is running on port 3000
- Check CORS configuration
- Verify network connectivity

**Authentication Issues**
- Clear localStorage and try again
- Check JWT token expiry
- Verify backend auth endpoints

**Facebook Integration**
- Ensure Facebook app is configured
- Check OAuth redirect URLs
- Verify app permissions

### Debug Steps
1. Open browser dev tools
2. Check Console for errors
3. Monitor Network tab for API calls
4. Verify localStorage for tokens
5. Check API logs section in interface
