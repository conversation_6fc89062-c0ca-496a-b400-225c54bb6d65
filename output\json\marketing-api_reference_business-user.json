{"title": "Business User", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_RQ\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_8R\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_Qt\"></div></span></div></div>\n\n<h1 id=\"overview\">Business User</h1>\n\n<p>In Graph API v9.0, <a href=\"/docs/graph-api/changelog/version9.0#business\">access to this endpoint was restricted</a>. In Graph API v10.0, <a href=\"/docs/graph-api/changelog/version10.0#business\">access has been restored to all apps</a>, but apps can now only target businesses (or child businesses of those businesses) that have claimed them.</p>\n\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Represents a business user. A business user can be an employee of the business or an admin of the business. An Employee can see all of information in business settings and be assigned roles by business admins. An Admin can control all aspects of the business including modifying or deleting the account and adding or removing people from the employee list</p>\n</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_fK\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_P8\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_Yo\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_0h\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_qW\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_v9\">iOS SDK</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%7Bbusiness-user-id%7D&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_b_Ce\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/{</span><span class=\"pln\">business</span><span class=\"pun\">-</span><span class=\"pln\">user</span><span class=\"pun\">-</span><span class=\"pln\">id</span><span class=\"pun\">}</span><span class=\"pln\"> HTTP</span><span class=\"pun\">/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The business user's ID.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>business</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/business/\">Business</a></div></td><td><p class=\"_yd\"></p><div><div><p>Business user is associated with this business.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>email</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>User's email as provided in Business Manager.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>finance_permission</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Financial permission role of the user in Business Manager, such as <code>EDITOR</code>, <code>ANALYST</code>, and so on.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>first_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>User's first name as provided in Business Manager.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ip_permission</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>This user's ads right permission role in Business Manager, such as Reviewer and so on.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>last_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>User's last name as provided in Business Manager.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Name of user as provided in Business Manager.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>pending_email</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Email for the business user that is still pending verification.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>role</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Role of the user in Business Manager, such as Admin, Employee, and so on.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>title</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The title of the user in this business.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>two_fac_status</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Two-factor authentication status of the business-scoped user.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"edges\">Edges</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/business-user/assigned_business_asset_groups/\"><code>assigned_business_asset_groups</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;BusinessAssetGroup&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Business asset groups that are assign to this business scoped user</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/business-user/assigned_pages/\"><code>assigned_pages</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Page&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Pages that are assigned to this business scoped user</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/business-user/assigned_product_catalogs/\"><code>assigned_product_catalogs</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalog&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Product catalogs that are assigned to this business scoped user</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>104</td><td>Incorrect signature</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Creating\">Creating</h2><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>business_users</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/business/business_users/\"><code>/{business_id}/business_users</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/business-user/\">BusinessUser</a> will be created.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_g_m5\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>email</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Email of user to be added to this business.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>invited_user_type</code></span></div><div class=\"_yb\">array&lt;enum {FB, MWA}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Not passing a value will default to 'FB'.</p>\n\n<p>Use 'MWA' for inviting a user with their Meta account managed by their organization.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>role</code></span></div><div class=\"_yb\">enum {FINANCE_EDITOR, FINANCE_ANALYST, ADS_RIGHTS_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER_CENTER_ADMIN, PARTNER_CENTER_ANALYST, PARTNER_CENTER_OPERATIONS, PARTNER_CENTER_MARKETING, PARTNER_CENTER_EDUCATION, MANAGE, DEFAULT, FINANCE_EDIT, FINANCE_VIEW}</div></td><td><p class=\"_yd\"></p><div><div><p>Role of user to add to this business.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>457</td><td>The session has an invalid origin</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Updating\">Updating</h2><div class=\"_844_\"><div class=\"_3-98\">You can update a&nbsp;<a href=\"/docs/marketing-api/reference/business-user/\">BusinessUser</a> by making a POST request to <a href=\"/docs/marketing-api/reference/business-user/\"><code>/{business_user_id}</code></a>.<div><h3 id=\"parameters-3\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_h_Y+\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>email</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The email of the user at this business.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>first_name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>First name for this business user.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>last_name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Last name for this business user.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>role</code></span></div><div class=\"_yb\">enum {FINANCE_EDITOR, FINANCE_ANALYST, ADS_RIGHTS_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER_CENTER_ADMIN, PARTNER_CENTER_ANALYST, PARTNER_CENTER_OPERATIONS, PARTNER_CENTER_MARKETING, PARTNER_CENTER_EDUCATION, MANAGE, DEFAULT, FINANCE_EDIT, FINANCE_VIEW}</div></td><td><p class=\"_yd\"></p><div><div><p>The role of the user at this business, such as <code>ADMIN</code> and so on.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>skip_verification_email</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>Whether to skip sending the verification email. The business persona email still requires verification - but just won't receive an email.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type-2\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-3\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr><tr><td>3914</td><td>It looks like you're trying to remove the last admin from this Business Manager. At least one admin is required in Business Manager.</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\"><div class=\"_3-98\">You can delete a&nbsp;<a href=\"/docs/marketing-api/reference/business-user/\">BusinessUser</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/business-user/\"><code>/{business_user_id}</code></a>.<div><h3 id=\"parameters-4\">Parameters</h3>This endpoint doesn't have any parameters.</div><h3 id=\"return-type-3\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-4\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3914</td><td>It looks like you're trying to remove the last admin from this Business Manager. At least one admin is required in Business Manager.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/business-user", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user/assigned_business_asset_groups/", "/docs/marketing-api/reference/business-user/assigned_pages/", "/docs/marketing-api/reference/business-user/assigned_product_catalogs/", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/business/business_users/", "/docs/marketing-api/reference/business-user/"], "url": "https://developers.facebook.com/docs/marketing-api/reference/business-user", "timestamp": "2025-06-25T15:44:47.162Z"}