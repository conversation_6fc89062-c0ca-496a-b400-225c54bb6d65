{"title": "Facebook Marketing API: Automating Ad Creation", "summary": "This guide covers the systematic approach to creating ads using the Facebook Marketing API, including the three primary endpoints for campaigns, ad sets, and ads. It provides detailed guidance and code samples for programmatically creating advertising components.", "content": "# Automating Ad Creation\n\nCreating ads using the Marketing API involves a systematic approach that includes setting up campaigns, ad sets, and ad creatives. This document provides detailed guidance on programmatically creating these components, along with code samples to illustrate the implementation process.\n\n## Ad Creation Endpoints\n\nThe Marketing API offers a variety of key endpoints that serve as essential tools for developers to create, manage, and analyze advertising campaigns. The primary creation endpoints include `campaigns`, `adsets`, and `ads`. Understanding these endpoints and their functionalities is crucial for both new and experienced developers looking to optimize their advertising strategies.\n\n### The `campaigns` endpoint\n\nThe [`campaigns` endpoint](/docs/marketing-api/reference/ad-account/campaigns) is used to create and manage advertising campaigns. This endpoint allows users to set the overall objectives for their marketing efforts, such as brand awareness or conversions.\n\n**Example API Request:**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/campaigns \\\n  -F 'name=My Campaign' \\\n  -F 'objective=LINK_CLICKS' \\\n  -F 'status=PAUSED' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\n### The `adsets` endpoint\n\nThe [`adsets` endpoint](/docs/marketing-api/reference/ad-account/adsets) organizes ads within campaigns based on specific targeting criteria and budget allocation. This allows for more granular control over audience targeting and spending.\n\n**Example API Request:**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets \\\n  -F 'name=My Ad Set' \\\n  -F 'campaign_id=<CAMPAIGN_ID>' \\\n  -F 'daily_budget=1000' \\\n  -F 'targeting={\"geo_locations\":{\"countries\":[\"US\"]}}' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\n### The `ads` endpoint\n\nThe [`ads` endpoint](/docs/marketing-api/reference/ad-account/ads) is where the actual advertisements are created, allowing you to define creative elements and link them to the appropriate ad set.\n\n**Example API Request:**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads \\\n  -F 'name=My Ad' \\\n  -F 'adset_id=<AD_SET_ID>' \\\n  -F 'creative={\"creative_id\": \"<CREATIVE_ID>\"}' \\\n  -F 'status=ACTIVE' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```", "keyPoints": ["Ad creation follows a hierarchical structure: campaigns → ad sets → ads", "The campaigns endpoint sets overall marketing objectives like brand awareness or conversions", "Ad sets provide granular control over targeting criteria and budget allocation", "The ads endpoint creates actual advertisements with creative elements", "All endpoints require proper authentication via access tokens"], "apiEndpoints": ["POST /v23.0/act_{ad-account-id}/campaigns", "POST /v23.0/act_{ad-account-id}/adsets", "POST /v23.0/act_{ad-account-id}/ads"], "parameters": ["name", "objective", "status", "campaign_id", "daily_budget", "targeting", "adset_id", "creative", "access_token"], "examples": ["Campaign creation with LINK_CLICKS objective", "Ad set creation with US geo-targeting and daily budget", "Ad creation linking to creative and ad set"], "tags": ["facebook-marketing-api", "ad-creation", "campaigns", "adsets", "automation", "api-endpoints"], "relatedTopics": ["Ad Campaign Creation", "Ad Account Management", "Creative Management", "Targeting Options", "Budget Management"], "difficulty": "beginner", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/get-started/basic-ad-creation", "processedAt": "2025-06-25T15:49:09.563Z", "processor": "openrouter-claude-sonnet-4"}