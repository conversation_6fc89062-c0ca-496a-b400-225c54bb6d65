{"title": "", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_o_yH\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\"></a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#fields\">Fields</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-2\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-2\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_np\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_lN\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_di\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_TK\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\"></h1><h1>Ad Account, Insights</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><p>The Insights API can return several metrics which are <i>estimated</i> or <i>in-development</i>. In some cases a metric may be <b>both </b><i>estimated and in-development</i>.</p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\"><p><b>Estimated</b> - Provide directional insights for outcomes that are hard to precisely quantify. They may evolve as we gather more data. See <a href=\"https://www.facebook.com/business/help/***************?helpref=faq_content#estimated\">Ads Help Center, Estimated metrics</a>.</p></div></li><li><div class=\"fcb\"><p><b>In Development</b> - Still being tested and may change as we improve our methodologies. We encourage you to use it for directional guidance, but please use caution when using it for historical comparisons or strategic planning. See <a href=\"https://www.facebook.com/business/help/***************?helpref=faq_content#indevelopment\">Ads Help Center, In development metrics</a>.</p></div></li></ul><p>For more information, see <a href=\"/docs/marketing-api/insights/estimated-in-development\">Insights API, Estimated and Deprecated Metrics</a></p><div class=\"_57yz _5s-k _3-8p\"><div class=\"_57y-\"><p>Facebook will no longer be able to aggregate non-inline conversion metric values across iOS 14.5 and non-iOS 14.5 campaigns due to differences in attribution logic. Querying across iOS 14.5 and non-iOS 14.5 campaigns will result in no data getting returned for non-inline conversion metrics such as app installs and purchases. Inline event metrics like impressions, link clicks, and video views, however, can still be aggregated. Please visit our <a href=\"https://developers.facebook.com/docs/graph-api/changelog/non-versioned-changes/jan-19-2021\">changelog</a> for more information.</p>\n</div></div><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>The <code>date_preset = lifetime</code> parameter is disabled in Graph API v10.0 and replaced with <code>date_preset = maximum</code>, which returns a maximum of 37 months of data. For v9.0 and below, <code>date_preset = maximum</code> will be enabled on May 25, 2021, and any <code>lifetime</code> calls will default to <code>maximum</code> and return only 37 months of data.</p>\n</div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Provides insights on your advertising performance. Allows for deduped metrics across child objects, such as <code>unique_clicks</code>, sorting of metrics, and async reporting.</p>\n</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_sn\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_6I\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_NF\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_so\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_7w\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_49\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_b_X4\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%3CAD_SET_ID%3E%2Finsights%3Ffields%3Dimpressions%26breakdown%3Dpublisher_platform&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_c_VN\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/insights?fields=impressions&amp;breakdown=publisher_platform HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_d_0E\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"kwd\">get</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/&lt;AD_SET_ID&gt;/insights?fields=impressions&amp;breakdown=publisher_platform'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_e_sA\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/&lt;AD_SET_ID&gt;/insights\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"impressions\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"breakdown\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"publisher_platform\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_f_eT\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"fields\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"impressions\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"breakdown\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"publisher_platform\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/&lt;AD_SET_ID&gt;/insights\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">GET</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_g_3X\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"impressions\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"breakdown\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"publisher_platform\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/&lt;AD_SET_ID&gt;/insights\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"GET\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_h_pF\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=\"impressions\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'breakdown=\"publisher_platform\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/&lt;AD_SET_ID&gt;/insights</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_i_/M\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>action_attribution_windows</code></span></div><div class=\"_yb\">list&lt;enum{1d_view, 7d_view, 28d_view, 1d_click, 7d_click, 28d_click, 1d_ev, dda, default, 7d_view_first_conversion, 28d_view_first_conversion, 7d_view_all_conversions, 28d_view_all_conversions, skan_view, skan_click, skan_click_second_postback, skan_view_second_postback, skan_click_third_postback, skan_view_third_postback}&gt;</div></td><td><div>Default value: <code>default</code></div><p class=\"_yd\"></p><div><div><p>The attribution window for the actions. <br>\nFor example, <code>28d_click</code> means the API returns all actions that happened 28 days after someone clicked on the ad. <code>1d_ev</code> refers to engaged-view conversions counted when a skippable video ad is played for at least 10 seconds, or for at least 97% of its total length if it’s shorter than 10 seconds, and a person takes an action within 1 day. <br>\nThe <code>default</code> option means <code>[\"7d_click\",\"1d_view\"]</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>action_breakdowns</code></span></div><div class=\"_yb\">list&lt;enum{action_device, conversion_destination, matched_persona_id, matched_persona_name, signal_source_bucket, standard_event_content_type, action_canvas_component_name, action_carousel_card_id, action_carousel_card_name, action_destination, action_reaction, action_target_id, action_type, action_video_sound, action_video_type}&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>How to break down action results. Supports more than one breakdowns. Default value is [\"action_type\"].</p>\n\n<p>Note: you must also include <code>actions</code> field whenever <code>action_breakdowns</code> is specified.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>action_report_time</code></span></div><div class=\"_yb\">enum{impression, conversion, mixed, lifetime}</div></td><td><p class=\"_yd\"></p><div><div><p>Determines the report time of action stats. For example, if a person\n            saw the ad on Jan 1st but converted on Jan 2nd, when you query the API\n            with <code>action_report_time=impression</code>, you see a conversion on Jan\n            1st. When you query the API with <code>action_report_time=conversion</code>, you see a conversion on Jan 2nd.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>breakdowns</code></span></div><div class=\"_yb\">list&lt;enum{ad_extension_domain, ad_extension_url, ad_format_asset, age, app_id, body_asset, breakdown_ad_objective, breakdown_reporting_ad_id, call_to_action_asset, coarse_conversion_value, comscore_market, comscore_market_code, country, creative_relaxation_asset_type, description_asset, fidelity_type, flexible_format_asset_type, gen_ai_asset_type, gender, hsid, image_asset, impression_device, is_auto_advance, is_conversion_id_modeled, is_rendered_as_delayed_skip_ad, landing_destination, link_url_asset, mdsa_landing_destination, media_asset_url, media_creator, media_destination_url, media_format, media_origin_url, media_text_content, media_type, postback_sequence_index, product_id, redownload, region, skan_campaign_id, skan_conversion_id, skan_version, sot_attribution_model_type, sot_attribution_window, sot_channel, sot_event_type, sot_source, title_asset, user_persona_id, user_persona_name, video_asset, dma, frequency_value, hourly_stats_aggregated_by_advertiser_time_zone, hourly_stats_aggregated_by_audience_time_zone, mmm, place_page_id, publisher_platform, platform_position, device_platform, standard_event_content_type, conversion_destination, signal_source_bucket, marketing_messages_btn_name, impression_view_time_advertiser_hour_v2}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>How to break down the result. For more than one breakdown, only certain combinations are available: See <a href=\"/docs/marketing-api/insights/breakdowns#combiningbreakdowns\">Combining Breakdowns</a> and the <a href=\"/docs/marketing-api/insights/breakdowns\">Breakdowns</a> page. The option <code>impression_device</code> cannot be used by itself.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>date_preset</code></span></div><div class=\"_yb\">enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year}</div></td><td><div>Default value: <code>last_30d</code></div><p class=\"_yd\"></p><div><div><p>Represents a relative time range. This field is ignored if <code>time_range</code> or <code>time_ranges</code> is specified.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>default_summary</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>Determine whether to return a summary. If <code>summary</code> is set, this param is be ignored; otherwise, a summary section with the same fields as specified by <code>fields</code> will be included in the summary section.</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>export_columns</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Select fields on the exporting report file. It is an optional param. Exporting columns are equal to the param fields, if you leave this param blank</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>export_format</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Set the format of exporting report file. If the export_format is set, Report file is asyncrhonizely generated. It expects [\"xls\", \"csv\"].</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>export_name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Set the file name of the exporting report.</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>fields</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Fields to be retrieved. Default behavior is to return impressions and spend.</p>\n</div></div><p></p></td></tr><tr class=\"row_10 _5m27\"><td><div class=\"_yc\"><span><code>filtering</code></span></div><div class=\"_yb\">list&lt;Filter Object&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>Filters on the report data. This parameter is an array of filter objects.</p>\n</div></div><p></p></td></tr><tr class=\"row_10-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>field</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_10-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>operator</code></span></div><div class=\"_yb\">enum {EQUAL, NOT_EQUAL, GREATER_THAN, GREATER_THAN_OR_EQUAL, LESS_THAN, LESS_THAN_OR_EQUAL, IN_RANGE, NOT_IN_RANGE, CONTAIN, NOT_CONTAIN, CONTAINS_ANY, NOT_CONTAINS_ANY, IN, NOT_IN, STARTS_WITH, ENDS_WITH, ANY, ALL, AFTER, BEFORE, ON_OR_AFTER, ON_OR_BEFORE, NONE, TOP}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_10-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>value</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>level</code></span></div><div class=\"_yb\">enum {ad, adset, campaign, account}</div></td><td><p class=\"_yd\"></p><div><div><p>Represents the level of result.</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>limit</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>limit</p>\n</div></div><p></p></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>product_id_limit</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>Maximum number of product ids to be returned for each ad when breakdown by <code>product_id</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>sort</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>Field to sort the result, and direction of sorting. You can specify sorting direction by appending \"_ascending\" or \"_descending\" to the sort field. For example, \"reach_descending\". For actions, you can sort by action type in form of \"actions:&lt;action_type&gt;\". For example, [\"actions:link_click_ascending\"]. This array supports no more than one element. By default, the sorting direction is ascending.</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29\"><td><div class=\"_yc\"><span><code>summary</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>If this param is used, a summary section will be included, with the fields listed in this param.</p>\n</div></div><p></p></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>summary_action_breakdowns</code></span></div><div class=\"_yb\">list&lt;enum{action_device, conversion_destination, matched_persona_id, matched_persona_name, signal_source_bucket, standard_event_content_type, action_canvas_component_name, action_carousel_card_id, action_carousel_card_name, action_destination, action_reaction, action_target_id, action_type, action_video_sound, action_video_type}&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>Similar to <code>action_breakdowns</code>, but applies to summary. Default value is [\"action_type\"].</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29\"><td><div class=\"_yc\"><span><code>time_increment</code></span></div><div class=\"_yb\">enum{monthly, all_days} or integer</div></td><td><div>Default value: <code>all_days</code></div><p class=\"_yd\"></p><div><div><p>If it is an integer, it is the number of days from 1 to 90. After you pick a reporting period by using <code>time_range</code> or <code>date_preset</code>, you may choose to have the results for the whole period, or have results for smaller time slices. If \"all_days\" is used, it means one result set for the whole period. If \"monthly\" is used, you will get one result set for each calendar month in the given period. Or you can have one result set for each N-day period specified by this param. This param is ignored if <code>time_ranges</code> is specified.</p>\n</div></div><p></p></td></tr><tr class=\"row_18 _5m27\"><td><div class=\"_yc\"><span><code>time_range</code></span></div><div class=\"_yb\">{'since':YYYY-MM-DD,'until':YYYY-MM-DD}</div></td><td><p class=\"_yd\"></p><div><div><p>A single time range object. UNIX timestamp not supported. This param is ignored if <code>time_ranges</code> is provided.</p>\n</div></div><p></p></td></tr><tr class=\"row_18-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>since</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means from the beginning midnight of that day.</p>\n</div></div><p></p></td></tr><tr class=\"row_18-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>until</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means to the beginning midnight of the following day.</p>\n</div></div><p></p></td></tr><tr class=\"row_19 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>time_ranges</code></span></div><div class=\"_yb\">list&lt;{'since':YYYY-MM-DD,'until':YYYY-MM-DD}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Array of time range objects. Time ranges can overlap, for example to return cumulative insights. Each time range will have one result set. You cannot have more granular results with <code>time_increment</code> setting in this case.If <code>time_ranges</code> is specified, <code>date_preset</code>, <code>time_range</code> and <code>time_increment</code> are ignored.</p>\n</div></div><p></p></td></tr><tr class=\"row_19-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>since</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means from the beginning midnight of that day.</p>\n</div></div><p></p></td></tr><tr class=\"row_19-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>until</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means to the beginning midnight of the following day.</p>\n</div></div><p></p></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>use_account_attribution_setting</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>When this parameter is set to <code>true</code>, your ads results will be shown using the attribution settings defined for the ad account.</p>\n</div></div><p></p></td></tr><tr class=\"row_21 _5m29\"><td><div class=\"_yc\"><span><code>use_unified_attribution_setting</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>When this parameter is set to <code>true</code>, your ads results will be shown using unified attribution settings defined at ad set level and parameter <code>use_account_attribution_setting</code> will be ignored.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class=\"_3hux\"><p>{\n    \"<code>data</code>\": [],\n    \"<code>paging</code>\": {},\n    \"<code>summary</code>\": {}\n}</p>\n</pre><div class=\"_3-8o\"><h4><code>data</code></h4>A list of AdsInsights nodes.</div><div class=\"_3-8o\"><h4><code>paging</code></h4>For more details about pagination, see the <a href=\"/docs/graph-api/using-graph-api/#paging\">Graph API guide</a>.</div><div class=\"_3-8o\"><h4><code>summary</code></h4><p>Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like <code>summary=account_currency</code>).</p><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>account_currency</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>Currency that is used by your ad account.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The ID number of your ad account, which groups your advertising activity. Your ad account includes your campaigns, ads and billing.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>account_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The name of your ad account, which groups your advertising activity. Your ad account includes your campaigns, ads and billing.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>action_values</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total value of all conversions attributed to your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total number of actions people took that are attributed to your ads. Actions may include engagement, clicks or conversions.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>activity_recency</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>activity_recency</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ad_click_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>ad_click_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ad_format_asset</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>ad_format_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ad_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The unique ID of the ad you're viewing in reporting.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>ad_impression_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>ad_impression_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ad_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The name of the ad you're viewing in reporting.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>adset_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The unique ID of the ad set you're viewing in reporting. An ad set is a group of ads that share the same budget, schedule, delivery optimization and targeting.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>adset_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The name of the ad set you're viewing in reporting. An ad set is a group of ads that share the same budget, schedule, delivery optimization and targeting.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>attribution_setting</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The default attribution window to be used when attribution result is calculated. Each ad set has its own attribution setting value. The attribution setting for campaign or account is calculated based on existing ad sets.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>auction_bid</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>auction_bid</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>auction_competitiveness</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>auction_competitiveness</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>auction_max_competitor_bid</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>auction_max_competitor_bid</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>body_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetBody</span></div></td><td><p class=\"_yd\"></p><div><p>body_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>buying_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The method by which you pay for and target ads in your campaigns: through dynamic auction bidding, fixed-price bidding, or reach and frequency buying. This field is currently only visible at the campaign level.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>campaign_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The unique ID number of the ad campaign you're viewing in reporting. Your campaign contains ad sets and ads.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>campaign_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The name of the ad campaign you're viewing in reporting. Your campaign contains ad sets and ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>canvas_avg_view_percent</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average percentage of the Instant Experience that people saw. An Instant Experience is a screen that opens after someone interacts with your ad on a mobile device. It may include a series of interactive or multimedia components, including video, images product catalog and more.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>canvas_avg_view_time</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average total time, in seconds, that people spent viewing an Instant Experience. An Instant Experience is a screen that opens after someone interacts with your ad on a mobile device. It may include a series of interactive or multimedia components, including video, images product catalog and more.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>catalog_segment_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of actions performed attributed to your ads promoting your catalog segment, broken down by action type.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>catalog_segment_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total value of all conversions from your catalog segment attributed to your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>catalog_segment_value_mobile_purchase_roas</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total return on ad spend (ROAS) from mobile app purchases for your catalog segment.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>catalog_segment_value_omni_purchase_roas</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total return on ad spend (ROAS) from all purchases for your catalog segment.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>catalog_segment_value_website_purchase_roas</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total return on ad spend (ROAS) from website purchases for your catalog segment.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>clicks</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of clicks on your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>coarse_conversion_value</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>Allows advertisers and ad networks to receive directional post-install quality insights when the volume of campaign conversions isn't high enough to meet the privacy threshold needed to unlock the standard conversion value. Possible values of this breakdown are <code>low</code>, <code>medium</code> and <code>high</code>.\n<br>\n<strong>Note:</strong> This breakdown is only supported by the <code>total_postbacks_detailed_v4</code> field.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>comparison_node</code></span></div><div class=\"_yb _yc\"><span>AdsInsightsComparison</span></div></td><td><p class=\"_yd\"></p><div><p>Parent node that encapsulates fields to be compared (current time range Vs comparison time range)</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>comscore_market</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>comscore market</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>conversion_values</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>conversion_values</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>conversions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>conversions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_app_custom_event_fb_mobile_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product app custom event fb mobile purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_app_custom_event_fb_mobile_purchase_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product app custom event fb mobile purchase value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_offline_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product offline purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_offline_purchase_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product offline purchase value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_omni_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product omni purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_omni_purchase_values</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product omni purchase values</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_quantity</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of products purchased which are recorded by your merchant partner's pixel or app SDK for a given product ID and driven by your ads. Has to be used together with converted product ID breakdown.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The value of purchases recorded by your merchant partner's pixel or app SDK for a given product ID and driven by your ads. Has to be used together with converted product ID breakdown.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_website_pixel_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product website pixel purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_product_website_pixel_purchase_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted product website pixel purchase value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_app_custom_event_fb_mobile_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product app custom event fb mobile purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_app_custom_event_fb_mobile_purchase_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product app custom event fb mobile purchase value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_offline_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product offline purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_offline_purchase_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product offline purchase value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_omni_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product omni purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_omni_purchase_values</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product omni purchase values</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_quantity</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted_promoted_product_quantity</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted_promoted_product_value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_website_pixel_purchase</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product website pixel purchase</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>converted_promoted_product_website_pixel_purchase_value</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>converted promoted product website pixel purchase value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_15_sec_video_view</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>cost_per_15_sec_video_view</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_2_sec_continuous_video_view</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>cost_per_2_sec_continuous_video_view</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_action_type</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The average cost of a relevant action.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_ad_click</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>cost_per_ad_click</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_conversion</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>cost_per_conversion</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_dda_countby_convs</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>cost_per_dda_countby_convs</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_inline_link_click</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost of each inline link click.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_inline_post_engagement</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost of each inline post engagement.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_objective_result</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost per objective result from your ads. Objective results are what you're trying to get the most of in your ad campaign, based on the objective you selected.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_one_thousand_ad_impression</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>cost_per_one_thousand_ad_impression</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_outbound_click</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The average cost for each outbound click.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_result</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost per result from your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_thruplay</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The average cost for each ThruPlay. This metric is in development.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_unique_action_type</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The average cost of each unique action. This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_unique_click</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost for each unique click (all). This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_unique_conversion</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>cost_per_unique_conversion</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_unique_inline_link_click</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost of each unique inline link click. This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cost_per_unique_outbound_click</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The average cost for each unique outbound click. This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>country</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>country</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cpc</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost for each click (all).</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cpm</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost for 1,000 impressions.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>cpp</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average cost to reach 1,000 people. This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>created_time</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>created_time</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>creative_relaxation_asset_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>creative relaxation asset type</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ctr</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The percentage of times people saw your ad and performed a click (all).</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>date_start</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The start date for your data. This is controlled by the date range you've selected for your reporting view.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>date_stop</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The end date for your data. This is controlled by the date range you've selected for your reporting view.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>dda_countby_convs</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>dda_countby_convs</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>dda_results</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsDdaResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>dda_results</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>description_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetDescription</span></div></td><td><p class=\"_yd\"></p><div><p>description_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>device_platform</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>device_platform</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>dma</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>dma</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>fidelity_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>To differentiate StoreKit-rendered ads from view-through ads, SKAdNetwork defines a fidelity-type parameter, which you include in the ad signature and receive in the install-validation postback. Use a fidelity-type value of <code>1</code> for StoreKit-rendered ads and attributable web ads, and <code>0</code> for view-through ads.\n<br>\n<strong>Note:</strong> This breakdown is only supported by the <code>total_postbacks_detailed_v4</code> field.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>flexible_format_asset_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>flexible format asset type</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>frequency</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The average number of times each person saw your ad. This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>frequency_value</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>frequency_value</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>full_view_impressions</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of Full Views on your Page's posts as a result of your ad.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>full_view_reach</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of people who performed a Full View on your Page's post as a result of your ad.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>gen_ai_asset_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>gen ai asset type</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>hourly_stats_aggregated_by_advertiser_time_zone</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>hourly_stats_aggregated_by_advertiser_time_zone</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>hourly_stats_aggregated_by_audience_time_zone</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>hourly_stats_aggregated_by_audience_time_zone</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>hsid</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The <code>hsid</code> key is available for ad impressions that use SKAdNetwork 4 and later. This integer can have up to four digits. You can encode information about your advertisement in each set of digits; you may receive two, three, or all four digits of the sourceIdentifier in the first winning postback, depending on the ad impression's postback data tier.\n<br>\n<strong>Note:</strong> This breakdown is only supported by the <code>total_postbacks_detailed_v4</code> field.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>image_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetImage</span></div></td><td><p class=\"_yd\"></p><div><p>image_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>impression_device</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>impression_device</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>impressions</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of times your ads were on screen.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>inline_link_click_ctr</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The percentage of time people saw your ads and performed an inline link click.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>inline_link_clicks</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of clicks on links to select destinations or experiences, on or off Facebook-owned properties. Inline link clicks use a fixed 1-day-click attribution window.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>inline_post_engagement</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The total number of actions that people take involving your ads. Inline post engagements use a fixed 1-day-click attribution window.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instagram_upcoming_event_reminders_set</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>instagram_upcoming_event_reminders_set</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instant_experience_clicks_to_open</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>instant_experience_clicks_to_open</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instant_experience_clicks_to_start</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>instant_experience_clicks_to_start</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instant_experience_outbound_clicks</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>instant_experience_outbound_clicks</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>interactive_component_tap</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>interactive_component_tap</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_auto_advance</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>is auto advance</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>landing_page_view_per_link_click</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>landing_page_view_per_link_click</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>marketing_messages_delivery_rate</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of messages delivered divided by the number of messages sent. Some messages may not be delivered, such as when a customer's device is out of service. This metric doesn't include messages sent to Europe and Japan.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>media_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetMedia</span></div></td><td><p class=\"_yd\"></p><div><p>media_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>mobile_app_purchase_roas</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total return on ad spend (ROAS) from mobile app purchases. This is based on the value that you assigned when you set up the app event.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>objective</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The objective reflecting the goal you want to achieve with your advertising. It may be different from the selected objective of the campaign in some cases.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>objective_result_rate</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>The number of objective results you received divided by the number of impressions.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>objective_results</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>The number of responses you wanted to achieve from your ad campaign, based on your selected objective. For example, if you selected promote your Page as your campaign objective, this metric shows the number of Page likes that happened as a result of your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>optimization_goal</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>The optimization goal you selected for your ad or ad set. Your optimization goal reflects what you want to optimize for the ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>outbound_clicks</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of clicks on links that take people off Facebook-owned properties.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>outbound_clicks_ctr</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The percentage of times people saw your ad and performed an outbound click.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>platform_position</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>platform_position</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>postback_sequence_index</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>Sequence of postbacks received from SkAdNetwork API version 4.0. Possible values of this breakdown are <code>0</code> (first postback), <code>1</code> (second postback) and <code>2</code> (third postback).\n<br>\n<strong>Note:</strong> This breakdown is only supported by the <code>total_postbacks_detailed_v4</code> field.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_brand</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_brand</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_category</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_category</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_content_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product content id</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_custom_label_0</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_custom_label_0</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_custom_label_1</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_custom_label_1</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_custom_label_2</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_custom_label_2</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_custom_label_3</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_custom_label_3</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_custom_label_4</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_custom_label_4</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_group_content_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product group content id</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_group_retailer_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_group_retailer_id</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_id</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_name</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_retailer_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product_retailer_id</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_views</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>product views</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>publisher_platform</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>publisher_platform</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>purchase_per_landing_page_view</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>purchase_per_landing_page_view</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>purchase_roas</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total return on ad spend (ROAS) from purchases. This is based on information received from one or more of your connected Facebook Business Tools and attributed to your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>qualifying_question_qualify_answer_rate</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>qualifying_question_qualify_answer_rate</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>reach</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The number of people who saw your ads at least once. Reach is different from impressions, which may include multiple views of your ads by the same people. This metric is estimated.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>redownload</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>Boolean flag that indicates the customer redownloaded and reinstalled the app when the value is true. A <code>1</code> indicates customer has reinstalled the app and <code>0</code> indicates that customer hasn’t reinstalled the app\n<br>\n<strong>Note:</strong> This breakdown is only supported by the <code>total_postbacks_detailed_v4</code> field.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>result_rate</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>The percentage of results you received out of all the views of your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>result_values_performance_indicator</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>result_values_performance_indicator</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>results</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsInsightsResult&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>The number of times your ad achieved an outcome, based on the objective and settings you selected.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>rule_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetRule</span></div></td><td><p class=\"_yd\"></p><div><p>rule_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>shops_assisted_purchases</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>shops_assisted_purchases</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>skan_version</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>skan_version</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>social_spend</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The total amount you've spent so far for your ads showed with social information. (ex: Jane Doe likes this).</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>spend</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>The estimated total amount of money you've spent on your campaign, ad set or ad during its schedule. This metric is estimated.</p>\n</div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>title_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetTitle</span></div></td><td><p class=\"_yd\"></p><div><p>title_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>total_card_view</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>total card view</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>updated_time</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>updated_time</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>user_segment_key</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><p>user_segment_key</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_30_sec_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video played for at least 30 seconds, or for nearly its total length if it's shorter than 30 seconds. For each impression of a video, we'll count video views separately and exclude any time spent replaying the video.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_asset</code></span></div><div class=\"_yb _yc\"><span>AdAssetVideo</span></div></td><td><p class=\"_yd\"></p><div><p>video_asset</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_avg_time_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The average time a video was played, including any time spent replaying the video for a single impression.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_continuous_2_sec_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>video_continuous_2_sec_watched_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_p100_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video was played at 100% of its length, including plays that skipped to this point.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_p25_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video was played at 25% of its length, including plays that skipped to this point.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_p50_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video was played at 50% of its length, including plays that skipped to this point.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_p75_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video was played at 75% of its length, including plays that skipped to this point.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_p95_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video was played at 95% of its length, including plays that skipped to this point.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_play_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The number of times your video starts to play. This is counted for each impression of a video, and excludes replays. This metric is in development.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_play_curve_actions</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsHistogramStats&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>A video-play based curve graph that illustrates the percentage of video plays that reached a given second. Entries 0 to 14 represent seconds 0 thru 14. Entries 15 to 17 represent second ranges [15 to 20), [20 to 25), and [25 to 30). Entries 18 to 20 represent second ranges [30 to 40), [40 to 50), and [50 to 60). Entry 21 represents plays over 60 seconds.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_play_retention_0_to_15s_actions</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsHistogramStats&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>video_play_retention_0_to_15s_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_play_retention_20_to_60s_actions</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsHistogramStats&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>video_play_retention_20_to_60s_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_play_retention_graph_actions</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdsHistogramStats&gt;</span></div></td><td><p class=\"_yd\"></p><div><p>video_play_retention_graph_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_time_watched_actions</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>video_time_watched_actions</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>website_ctr</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The percentage of times people saw your ad and performed a link click.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>website_purchase_roas</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-action-stats/\">list&lt;AdsActionStats&gt;</a></div></td><td><p class=\"_yd\"></p><div><p>The total return on ad spend (ROAS) from website purchases. This is based on the value of all conversions recorded by the Facebook pixel on your website and attributed to your ads.</p>\n</div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>wish_bid</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><p>wish_bid</p>\n</div><p></p></td></tr></tbody></table></div></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>3018</td><td>The start date of the time range cannot be beyond 37 months from the current date</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>2642</td><td>Invalid cursors values</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr><tr><td>3001</td><td>Invalid query</td></tr><tr><td>105</td><td>The number of parameters exceeded the maximum for this operation</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>insights</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/insights/\"><code>/act_{ad_account_id}/insights</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-report-run/\">AdReportRun</a> will be created.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_j_KZ\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>action_attribution_windows</code></span></div><div class=\"_yb\">list&lt;enum{1d_view, 7d_view, 28d_view, 1d_click, 7d_click, 28d_click, 1d_ev, dda, default, 7d_view_first_conversion, 28d_view_first_conversion, 7d_view_all_conversions, 28d_view_all_conversions, skan_view, skan_click, skan_click_second_postback, skan_view_second_postback, skan_click_third_postback, skan_view_third_postback}&gt;</div></td><td><div>Default value: <code>default</code></div><p class=\"_yd\"></p><div><div><p>The attribution window for the actions. For example, <code>28d_click</code> means the API returns all actions that happened 28 days after someone clicked on the ad. The <code>default</code> option means <code>[\"7d_view\",\"1d_click\"]</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>action_breakdowns</code></span></div><div class=\"_yb\">list&lt;enum{action_device, conversion_destination, matched_persona_id, matched_persona_name, signal_source_bucket, standard_event_content_type, action_canvas_component_name, action_carousel_card_id, action_carousel_card_name, action_destination, action_reaction, action_target_id, action_type, action_video_sound, action_video_type}&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>How to break down action results. Supports more than one breakdowns. Default value is [\"action_type\"]</p>\n\n<p>Note: you must also include <code>actions</code> field whenever <code>action_breakdowns</code> is specified.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>action_report_time</code></span></div><div class=\"_yb\">enum{impression, conversion, mixed, lifetime}</div></td><td><p class=\"_yd\"></p><div><div><p>Determines the report time of action stats. For example, if a person            saw the ad on Jan 1st but converted on Jan 2nd, when you query the API            with <code>action_report_time=impression</code>, you see a conversion on Jan            1st. When you query the API with <code>action_report_time=conversion</code>, you see a conversion on Jan 2nd</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>breakdowns</code></span></div><div class=\"_yb\">list&lt;enum{ad_extension_domain, ad_extension_url, ad_format_asset, age, app_id, body_asset, breakdown_ad_objective, breakdown_reporting_ad_id, call_to_action_asset, coarse_conversion_value, comscore_market, comscore_market_code, country, creative_relaxation_asset_type, description_asset, fidelity_type, flexible_format_asset_type, gen_ai_asset_type, gender, hsid, image_asset, impression_device, is_auto_advance, is_conversion_id_modeled, is_rendered_as_delayed_skip_ad, landing_destination, link_url_asset, mdsa_landing_destination, media_asset_url, media_creator, media_destination_url, media_format, media_origin_url, media_text_content, media_type, postback_sequence_index, product_id, redownload, region, skan_campaign_id, skan_conversion_id, skan_version, sot_attribution_model_type, sot_attribution_window, sot_channel, sot_event_type, sot_source, title_asset, user_persona_id, user_persona_name, video_asset, dma, frequency_value, hourly_stats_aggregated_by_advertiser_time_zone, hourly_stats_aggregated_by_audience_time_zone, mmm, place_page_id, publisher_platform, platform_position, device_platform, standard_event_content_type, conversion_destination, signal_source_bucket, marketing_messages_btn_name, impression_view_time_advertiser_hour_v2}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>How to break down the result. For more than one breakdown, only certain combinations are available: See \"Combining Breakdowns\" in the Breakdowns page. The option <code>impression_device</code> cannot be used by itself</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>date_preset</code></span></div><div class=\"_yb\">enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year}</div></td><td><div>Default value: <code>last_30d</code></div><p class=\"_yd\"></p><div><div><p>Represents a relative time range. This field is ignored if <code>time_range</code> or <code>time_ranges</code> is specified</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>default_summary</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>Determine whether to return a summary. If <code>summary</code> is set, this param is ignored; otherwise, a summary section with the same fields as specified by <code>fields</code> is included in the summary section</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>export_columns</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Select fields on the exporting report file. It is an optional param. Exporting columns are equal to the param fields if you leave this param blank</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>export_format</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Set the format of exporting report file. If the export_format is set, Report file is asyncrhonizely generated. It expects [\"xls\", \"csv\"].</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>export_name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Set the file name of the exporting report.</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>fields</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Fields to be retrieved. Default behavior is to return a list of most used fields</p>\n</div></div><p></p></td></tr><tr class=\"row_10 _5m27\"><td><div class=\"_yc\"><span><code>filtering</code></span></div><div class=\"_yb\">list&lt;Filter Object&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>Filters on the report data. This parameter is an array of filter objects</p>\n</div></div><p></p></td></tr><tr class=\"row_10-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>field</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_10-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>operator</code></span></div><div class=\"_yb\">enum {EQUAL, NOT_EQUAL, GREATER_THAN, GREATER_THAN_OR_EQUAL, LESS_THAN, LESS_THAN_OR_EQUAL, IN_RANGE, NOT_IN_RANGE, CONTAIN, NOT_CONTAIN, CONTAINS_ANY, NOT_CONTAINS_ANY, IN, NOT_IN, STARTS_WITH, ENDS_WITH, ANY, ALL, AFTER, BEFORE, ON_OR_AFTER, ON_OR_BEFORE, NONE, TOP}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_10-2 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>value</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>level</code></span></div><div class=\"_yb\">enum {ad, adset, campaign, account}</div></td><td><p class=\"_yd\"></p><div><div><p>Represents the level of result</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>product_id_limit</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>Maximum number of product ids to be returned for each ad when breakdown by <code>product_id</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>sort</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>Field to sort the result, and direction of sorting. You can specify sorting direction by appending \"_ascending\" or \"_descending\" to the sort field. For example, \"reach_descending\". For actions, you can sort by action type in form of \"actions:&lt;action_type&gt;\". For example, [\"actions:link_click_ascending\"]. This array supports no more than one element. By default, the sorting direction is ascending</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>summary</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>If this param is used, a summary section is included, with the fields listed in this param</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29\"><td><div class=\"_yc\"><span><code>summary_action_breakdowns</code></span></div><div class=\"_yb\">list&lt;enum{action_device, conversion_destination, matched_persona_id, matched_persona_name, signal_source_bucket, standard_event_content_type, action_canvas_component_name, action_carousel_card_id, action_carousel_card_name, action_destination, action_reaction, action_target_id, action_type, action_video_sound, action_video_type}&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class=\"_yd\"></p><div><div><p>Similar to <code>action_breakdowns</code>, but applies to summary. Default value is [\"action_type\"]</p>\n</div></div><p></p></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>time_increment</code></span></div><div class=\"_yb\">enum{monthly, all_days} or integer</div></td><td><div>Default value: <code>all_days</code></div><p class=\"_yd\"></p><div><div><p>If it is an integer, it is the number of days from 1 to 90. After you pick a reporting period by using <code>time_range</code> or <code>date_preset</code>, you may choose to have the results for the whole period, or have results for smaller time slices. If \"all_days\" is used, it means one result set for the whole period. If \"monthly\" is used, you get one result set for each calendar month in the given period. Or you can have one result set for each N-day period specified by this param. This param is ignored if <code>time_ranges</code> is specified</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>time_range</code></span></div><div class=\"_yb\">{'since':YYYY-MM-DD,'until':YYYY-MM-DD}</div></td><td><p class=\"_yd\"></p><div><div><p>A single time range object. UNIX timestamp not supported. This param is ignored if <code>time_ranges</code> is provided</p>\n</div></div><p></p></td></tr><tr class=\"row_17-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>since</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means from the beginning midnight of that day.</p>\n</div></div><p></p></td></tr><tr class=\"row_17-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>until</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means to the beginning midnight of the following day.</p>\n</div></div><p></p></td></tr><tr class=\"row_18 _5m27\"><td><div class=\"_yc\"><span><code>time_ranges</code></span></div><div class=\"_yb\">list&lt;{'since':YYYY-MM-DD,'until':YYYY-MM-DD}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Array of time range objects. Time ranges can overlap, for example to return cumulative insights. Each time range has one result set. You cannot have more granular results with <code>time_increment</code> setting in this case.If <code>time_ranges</code> is specified, <code>date_preset</code>, <code>time_range</code> and <code>time_increment</code> are ignored</p>\n</div></div><p></p></td></tr><tr class=\"row_18-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>since</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means from the beginning midnight of that day.</p>\n</div></div><p></p></td></tr><tr class=\"row_18-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>until</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>A date in the format of \"YYYY-MM-DD\", which means to the beginning midnight of the following day.</p>\n</div></div><p></p></td></tr><tr class=\"row_19 _5m29\"><td><div class=\"_yc\"><span><code>use_account_attribution_setting</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>When this parameter is set to true, your ads results are shown using the attribution settings defined for the ad account</p>\n</div></div><p></p></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>use_unified_attribution_setting</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>When this parameter is set to <code>true</code>, your ads results will be shown using unified attribution settings defined at ad set level and parameter <code>use_account_attribution_setting</code> will be ignored.\n<br>\n<strong>Note:</strong> Please set this to <code>true</code> to get the same behavior as in the Ads Manager.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>report_run_id</code>: numeric string, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>3018</td><td>The start date of the time range cannot be beyond 37 months from the current date</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/ad-account/insights", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/insights/estimated-in-development", "/docs/marketing-api/insights/breakdowns#combiningbreakdowns", "/docs/marketing-api/insights/breakdowns", "/docs/marketing-api/reference/ad-report-run/"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/", "timestamp": "2025-06-25T15:29:41.748Z"}