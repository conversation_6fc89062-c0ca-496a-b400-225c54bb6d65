{"title": "Ad Creative", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_h8\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_5p\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_yR\"></div></span></div></div>\n\n<h1 id=\"overview\">Ad Creative</h1>\n\n<div><div class=\"_57yz _57y_ _3-8p\"><div class=\"_57y-\"><p>\n      The <code>instagram_actor_id</code> field for the <code>act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</code> has been deprecated for v22.0 and will be deprecated for all versions January 20, 2026. Please migrate your API calls to use the <code>instagram_user_id</code> field. \n      Learn more in the \n      <a href=\"https://developers.facebook.com/docs/graph-api/changelog/version22.0\" target=\"_blank\">\n        v22.0 API Changelog\n      </a>\n      and \n      <a href=\"https://developers.facebook.com/blog/post/2025/01/21/making-it-easier-to-build-integrations-across-the-instagram-api-and-marketing-api/\" target=\"_blank\">\n      our News for Developers blog post.\n      </a></p></div></div></div><p>Format which provides layout and contains content for the ad. To see available ad creatives, visit <a href=\"https://www.facebook.com/business/ads-guide\">Ads Guide</a>. The guide also contains information on size requirements for each ad unit. See also <a href=\"https://www.facebook.com/business/overview\">Facebook for Business</a> and <a href=\"/ads/blog/post/2014/08/28/creative-page-post-api\">Inline page post creation blog post</a>.</p><span><h3>Ads About Social Issues, Elections, and Politics</h3>\n\n<p>Advertisers running ads about social issues, elections, and politics need to specify <a href=\"/docs/marketing-api/audiences/special-ad-category\"><code>special_ad_categories</code></a> while creating an ad campaign. In addition, businesses still have to set <code>authorization_category</code> to flag at the ad creative level. <a href=\"/docs/marketing-api/audiences/special-ad-category/#issues-elections-politics\">Learn more about the requirements.</a></p>\n</span><h3>Examples</h3><p>For example, get information about an ad creative, such as the ID of the newly created unpublished page post:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=name,object_story_id'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CREATIVE_ID&gt;</span></pre><p></p><p>Create a link ad:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Sample Creative'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={ \n    \"link_data\": { \n      \"image_hash\": \"&lt;IMAGE_HASH&gt;\", \n      \"link\": \"&lt;URL&gt;\", \n      \"message\": \"try it out\" \n    }, \n    \"page_id\": \"&lt;PAGE_ID&gt;\" \n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>You can replace <code>picture</code> with <code>image_hash</code> to specify an image from your ad account's image library. You can also specify image cropping with <code>image_crops</code> in <code>link_data</code>. See <a href=\"/docs/reference/ads-api/image-crops/\">Image Crop, Reference</a>.</p><p>To create a political ad creative, use the field <code>authorization_category</code> with value <code>POLITICAL</code>. For example:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'authorization_category=POLITICAL'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={\n    ...\n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>\n      Beginning January 9, 2024, to create an issue, electoral, or political ad creative that uses media that is digitally created or altered, use the <code>authorization_category</code> field with the <code>POLITICAL_WITH_DIGITALLY_CREATED_MEDIA</code> value. For example:\n    </p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'authorization_category=POLITICAL_WITH_DIGITALLY_CREATED_MEDIA'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={\n    ...\n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>For guidelines on Facebook ads see <a href=\"https://www.facebook.com/ad_guidelines.php\">Ad Guidelines</a>.</p><h2 id=\"resources\">Related Resources</h2><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\"><a href=\"/docs/marketing-api/mobile-app-ads\">App Ads</a></div></li><li><div class=\"fcb\"><a href=\"/docs/marketing-api/guides/videoads\">Video &amp; Carousel Ads</a></div></li><li><div class=\"fcb\"><a href=\"/docs/marketing-api/advantage-catalog-ads\">Advantage+ Catalog Ads</a></div></li><li><div class=\"fcb\"><a href=\"/docs/marketing-api/guides/instagramads\">Instagram Ads</a></div></li><li><div class=\"fcb\"><a href=\"/docs/marketing-api/ad-creative/messaging-ads/click-to-whatsapp\">Ads that Click to WhatsApp</a></div></li><li><div class=\"fcb\"><a href=\"/docs/marketing-api/guides/lead-ads\">Lead Ads</a></div></li></ul><h2 id=\"limits\">Limits</h2><p>Only returns 50,000 ad creatives, pagination past this is unavailable.</p><h3>Fields-Level Limits</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nLimit\n</th><th>\nValue\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_5_68\"><tr class=\"row_0\"><td><p>Maximum ad title length</p>\n</td><td><p>25 characters, recommended</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p>Minimum ad title length</p>\n</td><td><p>1 character</p>\n</td></tr><tr class=\"row_2\"><td><p>Maximum ad body length</p>\n</td><td><p>90 characters, recommended</p>\n</td></tr><tr class=\"row_3 _5m29\"><td><p>Minimum ad body length</p>\n</td><td><p>1 character</p>\n</td></tr><tr class=\"row_4\"><td><p>Maximum length of a URL</p>\n</td><td><p>1000 characters</p>\n</td></tr><tr class=\"row_5 _5m29\"><td><p>Maximum length of an individual word in title or body</p>\n</td><td><p>30 characters, recommended</p>\n</td></tr></tbody></table></div><h3>Title and Body Limits</h3><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\">Should be between minimum and maximum title and body lengths.</div></li><li><div class=\"fcb\">Cannot start with punctuation <code>\\ / ! . ? - * ( ) , ; :</code></div></li><li><div class=\"fcb\">Cannot have consecutive punctuation except of three full-stops <code>...</code></div></li><li><div class=\"fcb\">Words no longer than 30 characters</div></li><li><div class=\"fcb\">Only three 1-character words allowed</div></li></ul><p>The following characters are not allowed:</p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\">IPA Symbols. Except: &amp;#601;, &amp;#602;, &amp;#603;, &amp;#604;, &amp;#605;, &amp;#606;, &amp;#607;</div></li><li><div class=\"fcb\">Diacritical Marks. Precomposed version of a character + diacritical mark are allowed. Standalone diacritical marks are not allowed.</div></li><li><div class=\"fcb\">Superscript and subscript characters except &amp;#8482; and &amp;#8480;</div></li><li><div class=\"fcb\">These characters <code>^~_={}[]|&lt;&gt;</code></div></li></ul><h3>Exceptions</h3><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\"><b>Link Ads</b> cannot use special characters</div></li><li><div class=\"fcb\"><b>Page Posts Ads</b> allow special characters such as <code>★</code></div></li></ul><h3 id=\"placement\">Placement</h3><p>See <a href=\"/docs/marketing-api/creative/#placements\">Placement</a> for restrictions on placement of your ad based on creative.</p>\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>An ad creative object is an instance of a specific creative which is being used to define the <code>creative</code> field of one or more <a href=\"/docs/marketing-api/adgroup/\">ads</a></p>\n</div><div><h3 id=\"read_examples\">Read Thumbnail</h3><p>Request the thumbnail URL and dimensions:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'thumbnail_width=150'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'thumbnail_height=120'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=thumbnail_url'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CREATIVE_ID&gt;</span></pre><p></p></div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_6_Oq\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_7_RV\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_rY\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_iq\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_FB\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_b_Fp\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_c_Ra\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%3CCREATIVE_ID%3E%2F%3Ffields%3Dasset_feed_spec&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_d_AQ\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">CREATIVE_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/?fields=asset_feed_spec HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_j_il\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>thumbnail_height</code></span></div><div class=\"_yb\">int64</div></td><td><div>Default value: <code>64</code></div><p class=\"_yd\"></p><div><div><p>Rendered height of thumbnails provided in thumbnail_url, in pixels</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>thumbnail_width</code></span></div><div class=\"_yb\">int64</div></td><td><div>Default value: <code>64</code></div><p class=\"_yd\"></p><div><div><p>Rendered width of thumbnails accessible in thumbnail_url, in pixels</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Unique ID for an ad creative, numeric string.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Ad account ID for the account this ad creative belongs to.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>actor_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The actor ID (Page ID or User ID) of this creative</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ad_disclaimer_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/graph-api/reference/ad-creative-ad-disclaimer/\">AdCreativeAdDisclaimer</a></div></td><td><p class=\"_yd\"></p><div><div><p>Ad disclaimer data on creative for additional information on ads.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>adlabels</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-label/\">list&lt;AdLabel&gt;</a></div></td><td><p class=\"_yd\"></p><div><div><p><a href=\"/docs/marketing-api/reference/ad-label\">Ad Labels</a> associated with this creative. Used to group it with related ad objects.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>applink_treatment</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/dynamic-product-ads/ads-management\">Dynamic Ads</a>. Specify what action should occur if a person clicks a link in the ad, but the business' app is not installed on their device. For example, open a webpage displaying the product, or open the app in an app store on the person's mobile device.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>asset_feed_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-asset-feed-spec/\">AdAssetFeedSpec</a></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/dynamic-creative/dynamic-creative-optimization\">Dynamic Creative</a> to automatically experiment and deliver different variations of an ad's creative. Specifies an asset feed with multiple images, text and other assets used to generate variations of an ad. Formatted as a JSON string.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>authorization_category</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>Specifies whether ad was configured to be labeled as a political ad or not.\nSee <a href=\"https://www.facebook.com/policies/ads\">Facebook Advertising Policies</a>. This field cannot be used for <a href=\"https://developers.facebook.com/docs/marketing-api/dynamic-ad\">Dynamic Ads</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>body</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The body of the ad. Not supported for video post creatives</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>branded_content</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-branded-content-ads/\">AdCreativeBrandedContentAds</a></div></td><td><p class=\"_yd\"></p><div><div><p>branded_content</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>branded_content_sponsor_page_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID for page representing business which runs Branded Content ads. See <a href=\"/docs/marketing-api/guides/branded-content\">Creating Branded Content Ads</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>bundle_folder_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Ad's</a> bundle folder ID</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>call_to_action</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-link-data-call-to-action/\">AdCreativeLinkDataCallToAction</a></div></td><td><p class=\"_yd\"></p><div><div><p>Call to action for an ad created from existing Instagram post</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>call_to_action_type</code></span></div><div class=\"_yb _yc\"><span>enum {OPEN_LINK, LIKE_PAGE, SHOP_NOW, PLAY_GAME, INSTALL_APP, USE_APP, CALL, CALL_ME, VIDEO_CALL, INSTALL_MOBILE_APP, USE_MOBILE_APP, MOBILE_DOWNLOAD, BOOK_TRAVEL, LISTEN_MUSIC, WATCH_VIDEO, LEARN_MORE, SIGN_UP, DOWNLOAD, WATCH_MORE, NO_BUTTON, VISIT_PAGES_FEED, CALL_NOW, APPLY_NOW, CONTACT, BUY_NOW, GET_OFFER, GET_OFFER_VIEW, BUY_TICKETS, UPDATE_APP, GET_DIRECTIONS, BUY, SEND_UPDATES, MESSAGE_PAGE, DONATE, SUBSCRIBE, SAY_THANKS, SELL_NOW, SHARE, DONATE_NOW, GET_QUOTE, CONTACT_US, ORDER_NOW, START_ORDER, ADD_TO_CART, VIEW_CART, VIEW_IN_CART, VIDEO_ANNOTATION, RECORD_NOW, INQUIRE_NOW, CONFIRM, REFER_FRIENDS, REQUEST_TIME, GET_SHOWTIMES, LISTEN_NOW, WOODHENGE_SUPPORT, SOTTO_SUBSCRIBE, FOLLOW_USER, RAISE_MONEY, EVENT_RSVP, WHATSAPP_MESSAGE, FOLLOW_NEWS_STORYLINE, SEE_MORE, BOOK_NOW, FIND_A_GROUP, FIND_YOUR_GROUPS, PAY_TO_ACCESS, PURCHASE_GIFT_CARDS, FOLLOW_PAGE, SEND_A_GIFT, SWIPE_UP_SHOP, SWIPE_UP_PRODUCT, SEND_GIFT_MONEY, PLAY_GAME_ON_FACEBOOK, GET_STARTED, OPEN_INSTANT_APP, AUDIO_CALL, GET_PROMOTIONS, JOIN_CHANNEL, MAKE_AN_APPOINTMENT, ASK_ABOUT_SERVICES, BOOK_A_CONSULTATION, GET_A_QUOTE, BUY_VIA_MESSAGE, ASK_FOR_MORE_INFO, CHAT_WITH_US, VIEW_PRODUCT, VIEW_CHANNEL, GET_IN_TOUCH, WATCH_LIVE_VIDEO}</span></div></td><td><p class=\"_yd\"></p><div><div><p>Type of call to action button in your ad. This determines the button text and header text for your ad. See <a href=\"https://www.facebook.com/business/ads-guide/\">Ads Guide</a> for <a href=\"/docs/marketing-api/adcampaign/\">campaign objectives</a> and permitted call to action types.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>categorization_criteria</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>The <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Category Ad's</a> categorization field, e.g. brand</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>category_media_source</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>The <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Ad's</a> rendering mode for category ads</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>collaborative_ads_lsb_image_bank_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for CPAS local delivery image bank</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>contextual_multi_ads</code></span></div><div class=\"_yb _yc\"><span>AdCreativeContextualMultiAds</span></div></td><td><p class=\"_yd\"></p><div><div><p>contextual_multi_ads</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>creative_sourcing_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-sourcing-spec/\">AdCreativeSourcingSpec</a></div></td><td><p class=\"_yd\"></p><div><div><p>creative_sourcing_spec</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>degrees_of_freedom_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-degrees-of-freedom-spec/\">AdCreativeDegreesOfFreedomSpec</a></div></td><td><p class=\"_yd\"></p><div><div><p>Specifies the types of transformations that are enabled for the given creative</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>destination_set_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the Product Set for a Destination Catalog that will be used to link with Travel Catalogs</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>dynamic_ad_voice</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/guides/dynamic-ad/store-visits\">Store Traffic Objective inside Dynamic Ads</a>. Allows you to control the voice of your ad. If set to <code>DYNAMIC</code>, page name and profile picture in your ad post come from the nearest page location. If set to <code>STORY_OWNER</code>, page name and profile picture in your ad post come from the main page location.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>effective_authorization_category</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>Specifies whether ad is a political ad or not.\nSee <a href=\"https://www.facebook.com/policies/ads\">Facebook Advertising Policies</a>. This field cannot be used for <a href=\"https://developers.facebook.com/docs/marketing-api/dynamic-ad\">Dynamic Ads</a>.</p>\n\n<p>This value can be different than the authorization_category value in case our systems have identified the ad as political even though it was not configured to be labeled as such.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>effective_instagram_media_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of an Instagram post to use in an ad</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>effective_object_story_id</code></span></div><div class=\"_yb _yc\"><span>token with structure: Post ID</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a page post to use in an ad, regardless of whether it's an organic or unpublished page post</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>enable_direct_install</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether Direct Install should be enabled on supported devices</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>enable_launch_instant_app</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether Instant App should be enabled on supported devices</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>facebook_branded_content</code></span></div><div class=\"_yb _yc\"><span>AdCreativeFacebookBrandedContent</span></div></td><td><p class=\"_yd\"></p><div><div><p>Stores fields for Facebook Branded Content</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>image_crops</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ads-image-crops/\">AdsImageCrops</a></div></td><td><p class=\"_yd\"></p><div><div><p>A JSON object defining crop dimensions for the image specified. See <a href=\"/docs/marketing-api/image-crops/\">image crop reference</a> for more details</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>image_hash</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Image hash for ad creative. If provided, do not add <code>image_url</code>. See <a href=\"/docs/marketing-api/adimage/\">image library</a> for more details.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>image_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>A URL for the image for this creative. We save the image at this URL to the ad account's image library. If provided, do not include <code>image_hash</code>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instagram_permalink_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>URL for a post on Instagram you want to run as an ad. Also known as Instagram media.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instagram_user_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Instagram actor ID</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>interactive_components_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/graph-api/reference/ad-creative-interactive-components-spec/\">AdCreativeInteractiveComponentsSpec</a></div></td><td><p class=\"_yd\"></p><div><div><p>Specification for all the interactive components that would show up on the ad</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>link_destination_display_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Overwrites the display URL for link ads when <code>object_url</code> is set to a click tag</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>link_og_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The Open Graph (OG) ID for the link in this creative if the landing page has OG tags</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>link_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Identify a specific landing tab on your Facebook page by the Page tab's URL. See <a href=\"/docs/reference/ads-api/connectionobjects/\">connection objects</a> for retrieving Page tab URLs. You can add <a href=\"/docs/facebook-login/manually-build-a-login-flow\">app_data</a> parameters to the URL to pass data to a Page's tab.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>messenger_sponsored_message</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for Messenger sponsored message. JSON string with message for this ad creative. See <a href=\"docs/messenger-platform/reference/send-api\">Messenger Platform, Send API Reference</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Name of this ad creative as seen in the ad account's library. This field has a limit of 100 characters.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>object_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID for Facebook object being promoted with ads or relevant to the ad or ad type. For example a page ID if you are running ads to generate Page Likes. See <a href=\"/docs/marketing-api/reference/ad-campaign/promoted-object\">promoted_object</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>object_store_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>iTunes or Google Play of the destination of an app ad</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>object_story_id</code></span></div><div class=\"_yb _yc\"><span>token with structure: Post ID</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID of a Facebook Page post to use in an ad. You can get this ID by <a href=\"/docs/graph-api/reference/page/feed/\">querying the posts of the page</a>. If this post includes an image, it should not exceed 8 MB. Facebook will upload the image from the post to your ad account's <a href=\"/docs/marketing-api/adimage\">image library</a>. If you create an unpublished page post via <code>object_story_spec</code> at the same time as creating the ad, this ID will be null. However, the <code>effective_object_story_id</code> will be the ID of the page post regardless of whether it's an organic or unpublished page post.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>object_story_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-object-story-spec/\">AdCreativeObjectStorySpec</a></div></td><td><p class=\"_yd\"></p><div><div><p>Use if you want to create a new unpublished page post and turn the post into an ad. The Page ID and the content to create a new unpublished page post. Specify <code>link_data</code>, <code>photo_data</code>, <code>video_data</code>, <code>text_data</code> or <code>template_data</code> with the content.</p>\n\n<p><strong>Note:</strong> The <code>object_story_spec</code> field may be null if the ad creative was generated through boosting an Instagram post.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>object_type</code></span></div><div class=\"_yb _yc\"><span>enum {APPLICATION, DOMAIN, EVENT, OFFER, PAGE, PHOTO, SHARE, STATUS, STORE_ITEM, VIDEO, INVALID, PRIVACY_CHECK_FAIL, POST_DELETED}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The type of Facebook object you want to advertise. Allowed values are:<br><code>PAGE</code><br><code>DOMAIN</code><br><code>EVENT</code><br><code>STORE_ITEM</code>: refers to an iTunes or Google Play store destination<br><code>SHARE</code>: from a page<br><code>PHOTO</code><br><code>STATUS</code>: of a page<br><code>VIDEO</code><br><code>APPLICATION</code>: app on Facebook<br><code>INVALID</code>: when an invalid object_id was specified such as a deleted object or if you do not have permission to see the object. In very few cases, this field may be empty if Facebook is unable to identify the type of advertised object<br><code>PRIVACY_CHECK_FAIL</code>: you are missing the permission to load this object type<br><code>POST_DELETED</code>: this object_type has been deleted</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>object_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>URL that opens if someone clicks your link on a link ad. This URL is not connected to a Facebook page.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>page_welcome_message</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Page welcome message for CTM ads</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>photo_album_source_object_story_id</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>photo_album_source_object_story_id</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>place_page_set_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the page set for this creative. See the<a href=\"/docs/marketing-api/guides/local-awareness\">Local Awareness guide</a></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>platform_customizations</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-platform-customization/\">AdCreativePlatformCustomization</a></div></td><td><p class=\"_yd\"></p><div><div><p>Use this field to specify the exact media to use on different Facebook <a href=\"/docs/marketing-api/targeting-specs/#placement\">placements</a>. You can currently use this setting for images and videos. Facebook replaces the media originally defined in ad creative with this media when the ad displays in a specific placements. For example, if you define a media here for <code>instagram</code>, Facebook uses that media instead of the media defined in the ad creative when the ad appears on Instagram.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>playable_asset_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the playable asset in this creative</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>portrait_customizations</code></span></div><div class=\"_yb _yc\"><span>AdCreativePortraitCustomizations</span></div></td><td><p class=\"_yd\"></p><div><div><p>This field describes the rendering customizations selected for portrait mode ads like IG Stories, FB Stories, IGTV, etc</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_data</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdCreativeProductData&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>product_data</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>product_set_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Ad</a>. An ID for a product set, which groups related products or other items being advertised.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>recommender_settings</code></span></div><div class=\"_yb _yc\"><span>AdCreativeRecommenderSettings</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Ads</a>. Settings to display Dynamic ads based on product recommendations.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>referral_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of Referral Ad Configuration in this creative</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>source_facebook_post_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook post for creating ads</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>source_instagram_media_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of an Instagram post for creating ads</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb _yc\"><span>enum {ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The status of the creative. <code>WITH_ISSUES</code> and <code>IN_PROCESS</code> are available for 4.0 or higher</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>template_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Ads</a> when you want to use third-party click tracking. See <a href=\"/docs/marketing-api/dynamic-product-ads/ads-management#adtemplate\">Dynamic Ads, Click Tracking and Templates</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>template_url_spec</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-creative-template-url-spec/\">AdCreativeTemplateURLSpec</a></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Ads</a> when you want to use third-party click tracking. See <a href=\"/docs/marketing-api/dynamic-product-ads/ads-management#adtemplate\">Dynamic Ads, Click Tracking and Templates</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>threads_user_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>threads_user_id</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>thumbnail_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>thumbnail_id</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>thumbnail_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>URL for a thumbnail image for this ad creative. You can provide dimensions for this with <code>thumbnail_width</code> and <code>thumbnail_height</code>. <a href=\"/docs/marketing-api/reference/ad-creative#thumbnail-example\">See example</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>title</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Title for link ad, which does not belong to a page.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>url_tags</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>A set of query string parameters which will replace or be appended to urls clicked from page post ads, message of the post, and canvas app install creatives only</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>use_page_actor_override</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Used for <a href=\"/docs/app-ads\">App Ads</a>. If <code>true</code>, we display the Facebook page associated with the app ads.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>video_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Facebook object ID for video in this ad creative.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"edges\">Edges</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-creative/previews/\"><code>previews</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdPreview&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The HTML Snippets for previewing this creative</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>270</td><td>This Ads API request is not allowed for apps with development access level (Development access is by default for all apps, please request for upgrade). Make sure that the access token belongs to a user that is both admin of the app and admin of the ad account</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Creating\">Creating</h2><div><p>Define creative as part of an ad set or standalone. In either case, we store your ad creative in your ad account's creative library to use in ads. If you try to add an creative that isn't unique, we do not generate it and return the creative ID of the existing ad creative. For example, create a Link Ad with a call to action:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Sample Creative'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={ \n    \"link_data\": { \n      \"call_to_action\": {\"type\":\"SIGN_UP\",\"value\":{\"link\":\"&lt;URL&gt;\"}}, \n      \"link\": \"&lt;URL&gt;\", \n      \"message\": \"try it out\" \n    }, \n    \"page_id\": \"&lt;PAGE_ID&gt;\" \n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>You use <code>link_caption</code> to pass the call to action object. By doing this, you can customize the call to action caption. To customize the call to action description, pass <code>link_description</code> in the call to action object.</p><p>Create a <a href=\"/docs/reference/ads-api/multi-product-ads\">carousel ad</a></p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Sample Creative'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={ \n    \"link_data\": { \n      \"child_attachments\": [ \n        { \n          \"description\": \"$8.99\", \n          \"image_hash\": \"&lt;IMAGE_HASH&gt;\", \n          \"link\": \"https:\\/\\/www.link.com\\/product1\", \n          \"name\": \"Product 1\", \n          \"video_id\": \"&lt;VIDEO_ID&gt;\" \n        }, \n        { \n          \"description\": \"$9.99\", \n          \"image_hash\": \"&lt;IMAGE_HASH&gt;\", \n          \"link\": \"https:\\/\\/www.link.com\\/product2\", \n          \"name\": \"Product 2\", \n          \"video_id\": \"&lt;VIDEO_ID&gt;\" \n        }, \n        { \n          \"description\": \"$10.99\", \n          \"image_hash\": \"&lt;IMAGE_HASH&gt;\", \n          \"link\": \"https:\\/\\/www.link.com\\/product3\", \n          \"name\": \"Product 3\" \n        } \n      ], \n      \"link\": \"&lt;URL&gt;\" \n    }, \n    \"page_id\": \"&lt;PAGE_ID&gt;\" \n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><h3 id=\"partnership-ads-posts\">Partnership Ads Posts</h3><p>As a partnership ads sponsor, you can create ads with posts where your brand is tagged. Create a campaign, ad set, as ads as your normally do. The only difference is in the ad creative. </p><p>Set the <code>sponsor_page_id</code> field for <code>facebook_branded_content</code> and/or the <code>sponsor_id</code> field for <code>instagram_branded_content</code> in the ad creative.  For example:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;TOKEN&gt;'</span><span class=\"pln\"> \\\n </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'facebook_branded_content'</span><span class=\"pun\">:{</span><span class=\"str\">'sponsor_page_id=&lt;PAGE_ID&gt;'</span><span class=\"pun\">}</span><span class=\"pln\">\\\n </span><span class=\"com\">// OR</span><span class=\"pln\">\n </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'instagram_branded_content'</span><span class=\"pun\">:{</span><span class=\"str\">'sponsor_id=&lt;Instagram_user_ID&gt;'</span><span class=\"pun\">}</span><span class=\"pln\">\\\n </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_id=&lt;OBJECT_STORY_ID&gt;'</span><span class=\"pln\"> \\\nhttps</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;VERSION&gt;/&lt;ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>Where <code>object_story_id</code> is the post id in the format of: <code>postOwnerID_postID</code>.</p><h3 id=\"inline_post\">Inline Page Post Creation</h3><p>Most ad creatives rely on page posts for creative content. While you may create page posts separately then reference them by ID, it is easier to create them in the same call you use to provide ad creative. Specify the page post content with <code>object_story_spec</code> which creates an unpublished page post. See <a href=\"/ads/blog/post/2014/08/28/creative-page-post-api\">Inline Page Post, Blog</a>.</p><p>You can get the new ID by retrieving <code>object_story_id</code> from the ad creative. To get post ids created with <code>object_story_spec</code> through <a href=\"/docs/graph-api/reference/page/feed/\"><code>/promotable_posts</code></a>, pass <code>include_inline=true</code> in your <code>HTTP GET</code>. If <code>include_inline</code> value is <code>false</code>, we don't return any ids.</p><h3 id=\"obtaining_objects\">Get Related Objects</h3><p>Many ad creatives require <code>object_id</code> for a relevant Facebook object, app ID, or page tab's URL. See <a href=\"/docs/reference/ads-api/connectionobjects/\">Connection Objects</a> for more information.</p><h3 id=\"create_example\">Examples</h3><p>Create a Video Page Like ad:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Sample Creative'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={ \n    \"page_id\": \"&lt;PAGE_ID&gt;\", \n    \"video_data\": { \n      \"call_to_action\": {\"type\":\"LIKE_PAGE\",\"value\":{\"page\":\"&lt;PAGE_ID&gt;\"}}, \n      \"image_url\": \"&lt;THUMBNAIL_URL&gt;\", \n      \"video_id\": \"&lt;VIDEO_ID&gt;\" \n    } \n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>Create an ad from an existing page post</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Sample Promoted Post'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_id=&lt;POST_ID&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>Create a Photo Ad with <a href=\"https://www.facebook.com/business/news/branded-content-update\">Branded Content</a> from another page. This is available for photo, video, and link ads.</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Sample Creative'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_spec={ \n    \"page_id\": \"&lt;PAGE_ID&gt;\", \n    \"photo_data\": { \n      \"branded_content_sponsor_page_id\": \"&lt;SPONSOR_PAGE_ID&gt;\", \n      \"image_hash\": \"&lt;IMAGE_HASH&gt;\" \n    } \n  }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p><p>Adding <code>url_tags</code> to an ad</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'object_story_id=&lt;POST_ID&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'url_tags=key1=val1&amp;key2=val2'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></pre><p></p></div><div class=\"_844_\">You can't perform this operation on this endpoint.</div>\n\n<h2 id=\"Updating\">Updating</h2><div><h3 id=\"update_example\">Examples</h3><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=New creative name **********'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CREATIVE_ID&gt;</span></pre><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can update an&nbsp;<a href=\"/docs/marketing-api/reference/ad-creative/\">AdCreative</a> by making a POST request to <a href=\"/docs/marketing-api/reference/ad-creative/\"><code>/{ad_creative_id}</code></a>.<div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_k_sN\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb\">numeric string</div></td><td><p class=\"_yd\"></p><div><div><p>Ad account ID for the account this ad creative belongs to.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>adlabels</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p><a href=\"/docs/marketing-api/reference/ad-label\">Ad Labels</a> associated with this creative. Used to group it with related ad objects.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The name of the creative in the creative library. This field takes a string of up to 100 characters.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb\">enum {ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED}</div></td><td><p class=\"_yd\"></p><div><div><p>The status of this ad creative. See <a href=\"/docs/marketing-api/best-practices/storing_adobjects\">Storing and Retrieving Ad Objects</a>.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Deleting\">Deleting</h2><div><h3 id=\"delete_examples\">Examples</h3><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X DELETE \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CREATIVE_ID&gt;/</span></pre><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can delete an&nbsp;<a href=\"/docs/marketing-api/reference/ad-creative/\">AdCreative</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/ad-creative/\"><code>/{ad_creative_id}</code></a>.<div><h3 id=\"parameters-3\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_l_7X\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb\">numeric string</div></td><td><p class=\"_yd\"></p><div><div><p>Ad account ID for the account this ad creative belongs to.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>adlabels</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p><a href=\"/docs/marketing-api/reference/ad-label\">Ad Labels</a> associated with this creative. Used to group it with related ad objects.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Name of this ad creative as seen in the ad account's library.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb\">enum {ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED}</div></td><td><p class=\"_yd\"></p><div><div><p>The status of this ad creative. See <a href=\"/docs/marketing-api/best-practices/storing_adobjects\">Storing and Retrieving Ad Objects</a>.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type-2\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-3\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-creative", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/audiences/special-ad-category", "/docs/marketing-api/audiences/special-ad-category/#issues-elections-politics", "/docs/marketing-api/mobile-app-ads", "/docs/marketing-api/guides/videoads", "/docs/marketing-api/advantage-catalog-ads", "/docs/marketing-api/guides/instagramads", "/docs/marketing-api/ad-creative/messaging-ads/click-to-whatsapp", "/docs/marketing-api/guides/lead-ads", "/docs/marketing-api/creative/#placements", "/docs/marketing-api/adgroup/", "/docs/marketing-api/reference/ad-label", "/docs/marketing-api/dynamic-product-ads/ads-management", "/docs/marketing-api/dynamic-creative/dynamic-creative-optimization", "/docs/marketing-api/guides/branded-content", "/docs/marketing-api/dynamic-product-ads", "/docs/marketing-api/adcampaign/", "/docs/marketing-api/guides/dynamic-ad/store-visits", "/docs/marketing-api/image-crops/", "/docs/marketing-api/adimage/", "/docs/marketing-api/reference/ad-campaign/promoted-object", "/docs/marketing-api/adimage", "/docs/marketing-api/guides/local-awareness", "/docs/marketing-api/targeting-specs/#placement", "/docs/marketing-api/dynamic-product-ads/ads-management#adtemplate", "/docs/marketing-api/reference/ad-creative#thumbnail-example", "/docs/marketing-api/reference/ad-creative/previews/", "/docs/marketing-api/reference/ad-creative/", "/docs/marketing-api/best-practices/storing_adobjects"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-creative", "timestamp": "2025-06-25T15:50:44.286Z"}