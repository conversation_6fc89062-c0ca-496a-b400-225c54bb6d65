# Ad Account Targetingbrowse

Graph API Version

[v23.0](#)

# Ad Account Targetingbrowse

## Reading

Unified browse tree as a flat list. Use parent key to recreate the tree.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Ftargetingbrowse&version=v23.0)

```
`GET /v23.0/{ad-account-id}/targetingbrowse HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`include_nodes`

boolean

Include searchable nodes, for example, work/edu entries. This parameter is set as false by default. Also it is for internal use only

`limit_type`

enum {interests, education\_schools, education\_majors, work\_positions, work\_employers, relationship\_statuses, interested\_in, user\_adclusters, college\_years, education\_statuses, family\_statuses, industries, life\_events, politics, behaviors, income, net\_worth, home\_type, home\_ownership, home\_value, ethnic\_affinity, generation, household\_composition, moms, office\_type, location\_categories}

Limit the type of audience to retrieve

`regulated_categories`

array<enum {NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES\_ELECTIONS\_POLITICS, ONLINE\_GAMBLING\_AND\_GAMING, FINANCIAL\_PRODUCTS\_SERVICES}>

The regulated categories of the campaign

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of AdAccountTargetingUnified nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

100

Invalid parameter

190

Invalid OAuth 2.0 Access Token

## Creating

You can't perform this operation on this endpoint.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.