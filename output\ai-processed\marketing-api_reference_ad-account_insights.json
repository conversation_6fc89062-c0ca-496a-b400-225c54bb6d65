{"title": "Facebook Marketing API - Ad Account Insights Reference", "summary": "Complete reference documentation for the Facebook Marketing API Insights endpoint, which provides advertising performance metrics and analytics. Covers reading insights data, creating async reports, and all available parameters and fields for ad account analytics.", "content": "# Facebook Marketing API - Ad Account Insights\n\n## Overview\n\nThe Insights API provides comprehensive advertising performance metrics and analytics for Facebook ad accounts. It supports both synchronous and asynchronous reporting with advanced features like deduplication, breakdowns, and custom time ranges.\n\n### Important Notes\n\n- **Estimated Metrics**: Some metrics are estimated and provide directional insights for outcomes that are hard to precisely quantify\n- **In Development Metrics**: Certain metrics are still being tested and may change as methodologies improve\n- **iOS 14.5 Impact**: Non-inline conversion metrics cannot be aggregated across iOS 14.5 and non-iOS 14.5 campaigns due to attribution logic differences\n- **Data Retention**: Maximum of 37 months of historical data available\n\n## Reading Insights\n\nRetrieves advertising performance insights with support for deduped metrics, sorting, and async reporting.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/insights\n```\n\n### Key Parameters\n\n#### Time Range Parameters\n- `date_preset`: Predefined time ranges (today, yesterday, last_30d, etc.)\n- `time_range`: Custom date range with since/until dates\n- `time_ranges`: Array of multiple time ranges\n- `time_increment`: Granularity (all_days, monthly, or 1-90 days)\n\n#### Breakdown Parameters\n- `breakdowns`: How to segment results (age, gender, country, platform, etc.)\n- `action_breakdowns`: How to break down action results\n- `action_attribution_windows`: Attribution windows for actions\n\n#### Data Selection\n- `fields`: Specific metrics to retrieve\n- `level`: Reporting level (ad, adset, campaign, account)\n- `filtering`: Array of filter objects\n- `sort`: Field and direction for sorting results\n\n### Response Structure\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n## Key Metrics Available\n\n### Core Metrics\n- `impressions`: Number of times ads were shown\n- `clicks`: Total clicks on ads\n- `spend`: Total amount spent\n- `reach`: Estimated unique people reached\n- `frequency`: Average times each person saw ads\n\n### Performance Metrics\n- `cpc`: Cost per click\n- `cpm`: Cost per 1,000 impressions\n- `ctr`: Click-through rate\n- `cost_per_action_type`: Cost per specific action\n\n### Conversion Metrics\n- `actions`: Actions attributed to ads\n- `action_values`: Value of conversions\n- `conversions`: Conversion events\n- `purchase_roas`: Return on ad spend\n\n### Video Metrics\n- `video_play_actions`: Video play starts\n- `video_30_sec_watched_actions`: 30-second video views\n- `video_p25_watched_actions`: 25% video completion\n- `video_avg_time_watched_actions`: Average watch time\n\n## Creating Async Reports\n\nFor large data requests, create asynchronous reports that can be retrieved later.\n\n### Endpoint\n```\nPOST /v23.0/{ad-account-id}/insights\n```\n\n### Export Options\n- `export_format`: File format (\"xls\", \"csv\")\n- `export_name`: Custom filename\n- `export_columns`: Specific fields to export\n\n### Response\n```json\n{\n  \"report_run_id\": \"string\"\n}\n```\n\n## Advanced Features\n\n### Attribution Settings\n- `use_account_attribution_setting`: Use account-level attribution\n- `use_unified_attribution_setting`: Use ad set-level unified attribution\n\n### Filtering\nSupports complex filtering with operators:\n- EQUAL, NOT_EQUAL\n- GREATER_THAN, LESS_THAN\n- IN_RANGE, NOT_IN_RANGE\n- CONTAIN, NOT_CONTAIN\n- IN, NOT_IN\n\n### Breakdowns\nCombine multiple breakdowns for detailed analysis:\n- Demographics: age, gender\n- Geographic: country, region, dma\n- Platform: publisher_platform, device_platform\n- Time: hourly_stats_aggregated_by_advertiser_time_zone\n\n## Error Handling\n\nCommon error codes:\n- `100`: Invalid parameter\n- `190`: Invalid OAuth 2.0 Access Token\n- `200`: Permissions error\n- `3018`: Start date beyond 37 months limit\n- `2635`: Deprecated API version\n\n## Best Practices\n\n1. Use appropriate attribution windows for your business model\n2. Leverage breakdowns for detailed analysis\n3. Consider async reporting for large datasets\n4. Monitor estimated and in-development metrics carefully\n5. Use unified attribution settings for consistency with Ads Manager", "keyPoints": ["Provides comprehensive advertising performance metrics with support for deduplication and advanced breakdowns", "Supports both synchronous and asynchronous reporting with export capabilities", "Offers extensive filtering, sorting, and time range options for flexible data analysis", "Includes estimated and in-development metrics that require careful interpretation", "Maximum 37 months of historical data available with iOS 14.5 attribution limitations"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/insights", "POST /v23.0/{ad-account-id}/insights"], "parameters": ["date_preset", "time_range", "time_ranges", "time_increment", "breakdowns", "action_breakdowns", "action_attribution_windows", "fields", "level", "filtering", "sort", "export_format", "export_name", "use_account_attribution_setting", "use_unified_attribution_setting"], "examples": ["GET /v23.0/<AD_SET_ID>/insights?fields=impressions&breakdown=publisher_platform", "PHP SDK example with Facebook\\FacebookResponse", "JavaScript SDK example with FB.api", "cURL example with access token"], "tags": ["facebook-marketing-api", "insights", "analytics", "advertising-metrics", "performance-reporting", "ad-account", "breakdowns", "attribution"], "relatedTopics": ["AdsActionStats", "AdReportRun", "Attribution Windows", "Breakdowns", "Estimated Metrics", "iOS 14.5 Changes", "Graph API", "Async Reporting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/", "processedAt": "2025-06-25T15:30:12.030Z", "processor": "openrouter-claude-sonnet-4"}