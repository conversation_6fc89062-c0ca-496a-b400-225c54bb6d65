{"title": "Facebook Marketing API - Ad Account Insights Reference", "summary": "Complete reference documentation for the Facebook Marketing API Insights endpoint, which provides advertising performance metrics and analytics. Covers reading insights data, creating async reports, and all available parameters and fields for ad account analytics.", "content": "# Facebook Marketing API - Ad Account Insights\n\n## Overview\n\nThe Insights API provides comprehensive advertising performance metrics and analytics for Facebook ad accounts. It supports both synchronous and asynchronous reporting with advanced features like deduplication, breakdowns, and custom time ranges.\n\n### Important Notes\n\n- **Estimated Metrics**: Some metrics are estimated and provide directional insights for outcomes that are hard to precisely quantify\n- **In Development Metrics**: Certain metrics are still being tested and may change as methodologies improve\n- **iOS 14.5 Impact**: Non-inline conversion metrics cannot be aggregated across iOS 14.5 and non-iOS 14.5 campaigns due to attribution logic differences\n- **Data Retention**: Maximum of 37 months of historical data available (replaces previous `lifetime` preset)\n\n## Reading Insights\n\nRetrieves advertising performance insights with support for deduped metrics, sorting, and async reporting.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/insights\n```\n\n### Example Request\n```bash\ncurl -X GET -G \\\n  -d 'fields=\"impressions\"' \\\n  -d 'breakdown=\"publisher_platform\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_ACCOUNT_ID>/insights\n```\n\n### Key Parameters\n\n#### Time Range Parameters\n- `date_preset`: Predefined time ranges (today, yesterday, last_30d, etc.)\n- `time_range`: Custom date range with since/until dates\n- `time_ranges`: Array of multiple time ranges\n- `time_increment`: Granularity (daily, monthly, or N-day periods)\n\n#### Attribution Parameters\n- `action_attribution_windows`: Attribution windows for conversions (1d_view, 7d_click, 28d_click, etc.)\n- `use_account_attribution_setting`: Use account-level attribution settings\n- `use_unified_attribution_setting`: Use ad set-level unified attribution\n\n#### Breakdown Parameters\n- `breakdowns`: Dimension breakdowns (age, gender, country, device_platform, etc.)\n- `action_breakdowns`: Action-specific breakdowns (action_type, action_device, etc.)\n\n#### Filtering and Sorting\n- `filtering`: Array of filter objects with field, operator, and value\n- `sort`: Field and direction for sorting results\n- `level`: Reporting level (ad, adset, campaign, account)\n\n### Response Structure\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Core Metrics Fields\n\n**Performance Metrics**\n- `impressions`: Number of times ads were shown\n- `clicks`: Total clicks on ads\n- `spend`: Total amount spent\n- `reach`: Estimated unique people reached\n- `frequency`: Average times each person saw the ad\n\n**Cost Metrics**\n- `cpc`: Cost per click\n- `cpm`: Cost per 1,000 impressions\n- `cpp`: Cost per 1,000 people reached\n- `cost_per_action_type`: Cost per specific action\n\n**Conversion Metrics**\n- `actions`: Actions attributed to ads\n- `action_values`: Value of conversions\n- `conversions`: Conversion events\n- `conversion_values`: Conversion values\n\n**Video Metrics**\n- `video_play_actions`: Video play starts\n- `video_30_sec_watched_actions`: 30-second video views\n- `video_p25_watched_actions`: 25% video completion\n- `video_avg_time_watched_actions`: Average watch time\n\n## Creating Async Reports\n\nFor large data sets or complex queries, create asynchronous reports.\n\n### Endpoint\n```\nPOST /v23.0/{ad-account-id}/insights\n```\n\n### Parameters\nSame parameters as reading insights, plus:\n- `export_format`: File format (\"xls\", \"csv\")\n- `export_name`: Custom filename\n- `export_columns`: Specific fields to export\n\n### Response\n```json\n{\n  \"report_run_id\": \"<report_id>\"\n}\n```\n\n## Common Breakdowns\n\n### Demographic Breakdowns\n- `age`: Age ranges\n- `gender`: Male/female/unknown\n- `country`: Country codes\n- `region`: Geographic regions\n\n### Platform Breakdowns\n- `publisher_platform`: Facebook, Instagram, Messenger, etc.\n- `platform_position`: Feed, stories, right column, etc.\n- `device_platform`: Mobile, desktop, etc.\n- `impression_device`: Specific device types\n\n### Creative Breakdowns\n- `ad_format_asset`: Ad format types\n- `body_asset`: Ad body text variations\n- `image_asset`: Image creative variations\n- `video_asset`: Video creative variations\n\n## Error Handling\n\n### Common Error Codes\n- `100`: Invalid parameter\n- `190`: Invalid OAuth 2.0 Access Token\n- `200`: Permissions error\n- `2635`: Deprecated API version\n- `3018`: Date range exceeds 37-month limit\n\n## Best Practices\n\n1. **Use appropriate attribution windows** for your business model\n2. **Limit breakdowns** to avoid data fragmentation\n3. **Use async reporting** for large data sets\n4. **Cache results** when possible to reduce API calls\n5. **Handle estimated metrics** appropriately in analysis\n6. **Monitor for deprecated fields** and update accordingly", "keyPoints": ["Provides comprehensive advertising performance metrics with support for breakdowns and custom time ranges", "Supports both synchronous reading and asynchronous report generation for large datasets", "Includes estimated and in-development metrics that should be used with appropriate caution", "iOS 14.5 attribution changes affect non-inline conversion metric aggregation", "Maximum 37 months of historical data available with various attribution window options"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/insights", "POST /v23.0/{ad-account-id}/insights"], "parameters": ["date_preset", "time_range", "time_ranges", "time_increment", "action_attribution_windows", "breakdowns", "action_breakdowns", "fields", "filtering", "level", "sort", "use_account_attribution_setting", "use_unified_attribution_setting"], "examples": ["GET request with impressions field and publisher_platform breakdown", "PHP SDK implementation with error handling", "JavaScript SDK API call", "cURL command with access token"], "tags": ["facebook-marketing-api", "insights", "analytics", "advertising-metrics", "performance-data", "breakdowns", "attribution", "reporting"], "relatedTopics": ["Ad Account Management", "Campaign Analytics", "Attribution Windows", "Async Reporting", "Data Breakdowns", "iOS 14.5 Changes", "Estimated Metrics", "Graph API Pagination"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/", "processedAt": "2025-06-25T16:14:56.935Z", "processor": "openrouter-claude-sonnet-4"}