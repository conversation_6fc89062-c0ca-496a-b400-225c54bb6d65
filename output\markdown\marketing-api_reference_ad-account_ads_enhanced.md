# Facebook Marketing API - Ad Account Ads Reference

## Summary
Complete reference documentation for managing ads within a Facebook ad account, including reading, creating, and managing ad operations through the Marketing API. Covers endpoints, parameters, error handling, and code examples across multiple programming languages.

## Key Points
- Supports reading ads from an ad account with filtering and pagination options
- Creating ads requires name, adset_id (or adset_spec), creative, and status parameters
- Provides comprehensive error handling with specific error codes for different scenarios
- Supports multiple programming languages with SDK examples (PHP, JavaScript, Android, iOS)
- Includes advanced features like ad scheduling, audience targeting, and tracking specifications

## API Endpoints
- `GET /v23.0/act_{ad-account-id}/ads`
- `POST /v23.0/act_{ad-account-id}/ads`

## Parameters
- date_preset
- effective_status
- time_range
- since
- until
- updated_since
- name
- adset_id
- creative
- status
- ad_schedule_start_time
- ad_schedule_end_time
- adlabels
- adset_spec
- audience_id
- conversion_domain
- execution_options
- tracking_specs

## Content
# Ad Account Ads

Ads belonging to this ad account.

## Reading

Retrieve ads belonging to this ad account.

### Endpoint
```
GET /v23.0/act_{ad-account-id}/ads
```

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `date_preset` | enum | Predefined date range for aggregating insights metrics. Options: today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year |
| `effective_status` | list<string> | Filter ads by effective status |
| `time_range` | object | Date range object with 'since' and 'until' fields |
| `since` | datetime | Start date in "YYYY-MM-DD" format |
| `until` | datetime | End date in "YYYY-MM-DD" format |
| `updated_since` | integer | Time since the Ad has been updated |

### Response Format

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Response Fields

- **data**: A list of Ad nodes
- **paging**: Pagination information
- **summary**: Aggregated information including:
  - `insights`: Analytics summary for all objects
  - `total_count`: Total number of Ads returned

### Code Examples

#### PHP SDK
```php
try {
  $response = $fb->get(
    '/act_{ad-account-id}/ads',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/act_{ad-account-id}/ads",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

## Creating

Create a new ad within the ad account.

### Endpoint
```
POST /v23.0/act_{ad-account-id}/ads
```

### Required Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `name` | string | Name of the ad (supports emoji) |
| `adset_id` | int64 | The ID of the ad set (required unless adset_spec is provided) |
| `creative` | object | Creative ID or spec: `{"creative_id": "<CREATIVE_ID>"}` |
| `status` | enum | Ad status: ACTIVE, PAUSED, DELETED, ARCHIVED |

### Optional Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `ad_schedule_start_time` | datetime | Start time for individual ad scheduling |
| `ad_schedule_end_time` | datetime | End time for individual ad scheduling |
| `adlabels` | list<Object> | Ad labels associated with this ad |
| `adset_spec` | object | Ad set specification (alternative to adset_id) |
| `audience_id` | string | The ID of the audience |
| `conversion_domain` | string | Domain where conversions happen |
| `display_sequence` | int64 | Sequence of the ad within the campaign |
| `engagement_audience` | boolean | Create audience based on ad engagement |
| `execution_options` | list<enum> | Options: validate_only, synchronous_ad_review, include_recommendations |
| `priority` | int64 | Ad priority |
| `source_ad_id` | string | ID of the source ad |
| `tracking_specs` | object | Tracking and conversion specifications |

### Example Request

```bash
curl -X POST \
  -F 'name="My Ad"' \
  -F 'adset_id="<AD_SET_ID>"' \
  -F 'creative={
       "creative_id": "<CREATIVE_ID>"
     }' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads
```

### Return Type

```json
{
  "id": "numeric_string",
  "success": true
}
```

## Error Codes

### Reading Errors

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 2500 | Error parsing graph query |
| 2635 | Deprecated API version |
| 3018 | Start date beyond 37 months |
| 80004 | Rate limit exceeded |

### Creating Errors

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 194 | Missing required parameter |
| 200 | Permissions error |
| 368 | Action deemed abusive |
| 500 | Message contains banned content |
| 613 | Rate limit exceeded |
| 1500 | Invalid URL supplied |
| 2635 | Deprecated API version |
| 80004 | Rate limit exceeded |

## Updating and Deleting

Updating and deleting operations are not supported on this endpoint.

## Examples
PHP SDK GET request with error handling

JavaScript SDK API call

cURL POST request for creating ads

Android SDK GraphRequest implementation

iOS SDK FBSDKGraphRequest usage

---
**Tags:** Facebook Marketing API, Ads Management, Graph API, Ad Account, REST API, Social Media Marketing  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/  
**Processed:** 2025-06-25T16:21:06.230Z