# Facebook Marketing API - Ad Account Ads Reference

## Summary
Complete reference documentation for managing ads within a Facebook ad account, including reading, creating, and managing ad operations through the Marketing API. Covers endpoints, parameters, error codes, and code examples across multiple programming languages.

## Key Points
- Supports reading ads from ad accounts with filtering by status, date ranges, and update times
- Creating ads requires name, adset_id (or adset_spec), creative specification, and status
- New ads undergo review process with PENDING_REVIEW status before becoming active
- Direct updating and deleting operations are not supported through this endpoint
- Comprehensive error handling with specific codes for rate limiting, permissions, and validation issues

## API Endpoints
- `GET /v23.0/act_{ad-account-id}/ads`
- `POST /v23.0/act_{ad-account-id}/ads`

## Parameters
- date_preset
- effective_status
- time_range
- updated_since
- name
- adset_id
- creative
- status
- ad_schedule_start_time
- ad_schedule_end_time
- conversion_domain
- tracking_specs

## Content
# Ad Account Ads

Ads belonging to this ad account.

## Reading

Retrieve ads belonging to a specific ad account.

### Endpoint
```
GET /v23.0/act_{ad-account-id}/ads
```

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `date_preset` | enum | Predefined date range for aggregating insights metrics. Options: today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year |
| `effective_status` | list<string> | Filter ads by effective status |
| `time_range` | object | Date range object with 'since' and 'until' fields (YYYY-MM-DD format) |
| `updated_since` | integer | Time since the Ad has been updated |

### Response Format

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Response Fields
- **data**: Array of Ad nodes
- **paging**: Pagination information
- **summary**: Aggregated information including insights and total_count

### Code Examples

#### PHP SDK
```php
try {
  $response = $fb->get(
    '/act_{ad-account-id}/ads',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
}
```

#### JavaScript SDK
```javascript
FB.api(
    "/act_{ad-account-id}/ads",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

## Creating

Create a new ad within an ad account.

### Endpoint
```
POST /v23.0/act_{ad-account-id}/ads
```

### Required Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `name` | string | Name of the ad (supports emoji) |
| `adset_id` | int64 | The ID of the ad set (required unless adset_spec is provided) |
| `creative` | object | Creative ID or creative spec object |
| `status` | enum | Ad status: ACTIVE or PAUSED (for creation) |

### Optional Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `ad_schedule_start_time` | datetime | Start time for individual ad scheduling |
| `ad_schedule_end_time` | datetime | End time for individual ad scheduling |
| `adlabels` | list<Object> | Ad labels associated with this ad |
| `adset_spec` | object | Ad set specification (alternative to adset_id) |
| `audience_id` | string | The ID of the audience |
| `conversion_domain` | string | Domain where conversions happen |
| `display_sequence` | int64 | Sequence of the ad within the campaign |
| `execution_options` | list<enum> | Validation and execution options |
| `priority` | int64 | Ad priority |
| `tracking_specs` | object | Tracking and conversion specifications |

### Creative Parameter Format

```json
// Using creative ID
{"creative_id": "<CREATIVE_ID>"}

// Using creative spec
{"creative": {"name": "<NAME>", "object_story_spec": "<SPEC>"}}
```

### Example Request

```bash
curl -X POST \
  -F 'name="My Ad"' \
  -F 'adset_id="<AD_SET_ID>"' \
  -F 'creative={"creative_id": "<CREATIVE_ID>"}' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads
```

### Return Type

```json
{
  "id": "numeric_string",
  "success": true
}
```

## Error Codes

### Common Errors

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | Action deemed abusive or disallowed |
| 500 | Message contains banned content |
| 613 | API rate limit exceeded |
| 1500 | Invalid URL supplied |
| 2500 | Error parsing graph query |
| 2635 | Deprecated API version |
| 3018 | Start date beyond 37 months from current date |
| 80004 | Too many calls to ad account (rate limiting) |

## Operations Not Supported

- **Updating**: Direct updates to ads through this endpoint are not supported
- **Deleting**: Direct deletion of ads through this endpoint is not supported

## Notes

- Newly created ads go through ad review with status `PENDING_REVIEW` before reverting to selected status
- For testing, use `PAUSED` status to avoid accidental spend
- The `bid_amount` parameter is deprecated and should be set at the ad set level
- Ad scheduling parameters are only available for sales and app promotion campaigns

## Examples
PHP SDK GET request with error handling

JavaScript SDK API call

cURL POST request for creating ads

Creative specification formats

Android and iOS SDK implementations

---
**Tags:** Facebook Marketing API, Ads Management, Graph API, Ad Creation, API Reference, Social Media Marketing
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/
**Processed:** 2025-06-25T15:17:04.995Z