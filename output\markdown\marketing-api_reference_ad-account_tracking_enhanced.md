# Facebook Marketing API - Ad Account Tracking Reference

## Summary
Complete reference documentation for the Ad Account Tracking endpoint in Facebook's Marketing API. Covers reading tracking data from ad accounts and creating new tracking specifications at the account level.

## Key Points
- Supports reading existing tracking data and creating new tracking specifications
- Reading endpoint requires no parameters and returns AdAccountTrackingData nodes
- Creating requires tracking_specs parameter and supports read-after-write
- Update and delete operations are not supported on this endpoint
- Subject to rate limiting with specific error code 80004 for too many calls

## API Endpoints
- `GET /v23.0/{ad-account-id}/tracking`
- `POST /act_{ad_account_id}/tracking`

## Parameters
- tracking_specs (Object, required for POST)
- ad-account-id (path parameter)
- data (response field)
- paging (response field)
- success (return field)

## Content
# Ad Account Tracking

## Overview

The Ad Account Tracking endpoint allows you to manage tracking specifications for Facebook ad accounts. This endpoint supports reading existing tracking data and creating new tracking specifications.

**Graph API Version:** v23.0

## Reading Tracking Data

### Endpoint
```
GET /v23.0/{ad-account-id}/tracking
```

### Parameters
This endpoint doesn't require any parameters.

### Response Format
Reading from this edge returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {}
}
```

#### Response Fields
- **`data`**: A list of AdAccountTrackingData nodes
- **`paging`**: Pagination information (see [Graph API guide](https://developers.facebook.com/docs/graph-api/using-graph-api/#paging) for details)

### Example Request
```http
GET /v23.0/{ad-account-id}/tracking HTTP/1.1
Host: graph.facebook.com
```

## Creating Tracking Specifications

### Endpoint
```
POST /act_{ad_account_id}/tracking
```

When posting to this edge, no Graph object will be created.

### Parameters
| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `tracking_specs` | Object | Tracking specs to add to the account level | Yes |

### Return Type
This endpoint supports [read-after-write](https://developers.facebook.com/docs/graph-api/advanced/#read-after-write) and will read the node to which you POSTed.

```json
{
  "success": bool
}
```

## Limitations
- **Updating**: Not supported on this endpoint
- **Deleting**: Not supported on this endpoint

## Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 80004 | Too many calls to this ad-account. Wait and try again. See [rate limiting documentation](https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management) |

## Additional Resources
- [Using Graph API guide](https://developers.facebook.com/docs/graph-api/using-graph-api/)
- [Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Ftracking&version=v23.0)

## Examples
GET /v23.0/{ad-account-id}/tracking HTTP/1.1

Response format: {"data": [], "paging": {}}

Return type: {"success": bool}

---
**Tags:** Facebook Marketing API, Ad Account, Tracking, Graph API, API Reference, v23.0
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/tracking/
**Processed:** 2025-06-25T15:40:59.137Z