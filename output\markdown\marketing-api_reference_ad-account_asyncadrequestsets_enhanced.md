# Facebook Marketing API - Ad Account Asyncadrequestsets Reference

## Summary
Reference documentation for the Ad Account Asyncadrequestsets endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous ad request sets but does not support reading, updating, or deleting operations.

## Key Points
- Only POST (create) operations are supported on this endpoint
- No Graph object is created when posting to this edge
- Supports read-after-write functionality
- Requires ad_specs and name parameters for creation
- Optional notification system with completion callbacks

## API Endpoints
- `/act_{ad_account_id}/asyncadrequestsets`

## Parameters
- ad_specs
- name
- notification_mode
- notification_uri

## Content
# Ad Account Asyncadrequestsets

This endpoint manages asynchronous ad request sets for Facebook Marketing API ad accounts.

## Supported Operations

### Reading
Reading operations are **not supported** on this endpoint.

### Creating
You can make a POST request to the `asyncadrequestsets` edge from:
- `/act_{ad_account_id}/asyncadrequestsets`

When posting to this edge, no Graph object will be created.

#### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `ad_specs` | `list<dictionary { non-empty string : <string> }>` | Specs for ads in the request set | Yes |
| `name` | `UTF-8 encoded string` | Name of the request set | Yes |
| `notification_mode` | `enum{OFF, ON_COMPLETE}` | Specify `0` for no notifications and `1` for notification on completion | No |
| `notification_uri` | `URL` | If notifications are enabled, specify the URL to send them | No |

#### Return Type
This endpoint supports read-after-write and will read the node represented by `id` in the return type.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |

### Updating
Updating operations are **not supported** on this endpoint.

### Deleting
Deleting operations are **not supported** on this endpoint.

---
**Tags:** Facebook Marketing API, Ad Account, Async Requests, POST API, v23.0  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/asyncadrequestsets/  
**Processed:** 2025-06-25T16:25:12.206Z