{"title": "Facebook Marketing API - Ad Preview Plugin", "summary": "The Ad Preview Plugin enables advertisers to generate previews of ads on their websites using Creative Specs, Adgroup IDs, or Creative IDs. Previews can be generated through Social Plugins or the Graph API for various ad formats including Right Hand Column, Feed, and Mobile.", "content": "# Ad Preview Plugin\n\nThe Ad Preview plugin is the easiest way for advertisers to preview ads on their own websites.\n\nThe plugin enables you to generate Right Hand Column, Feed, or Mobile previews of an ad by specifying a Creative Spec, Adgroup ID or Creative ID. Previews can either be generated using a Social Plugin or through the Graph API.\n\n## Parameters\n\n### Required Parameters\n- One of: `creative`, `creative_id`, or `adgroup_id`\n- `ad_format` (replaces the deprecated `page_type` parameter)\n\n### Optional Parameters\n- `ad_account_id`\n- `targeting`\n- `post`\n\n### Authentication Requirements\nThe preview plugin requires you to be logged in with Facebook Login. If `creative_id`, `adgroup_id`, or `ad_account_id` is used, you must also have the permissions to access the Creative, Ad Group, or Ad Account respectively.\n\n## Parameter Reference\n\n| Setting | HTML5 Attribute | Description |\n|---------|------------------|-------------|\n| `ad_account_id` | `data-ad-account-id` | Required when specifying a creative that uses `image_hash` |\n| `adgroup_id` | `data-adgroup-id` | Adgroup ID returned from a Graph API call |\n| `creative` | `data-creative` | JSON-encoded creative spec |\n| `creative_id` | `data-creative-id` | Creative ID returned from a Graph API call |\n| `ad_format` | `data-ad-format` | One of: `RIGHT_COLUMN_STANDARD`, `DESKTOP_FEED_STANDARD`, `MOBILE_FEED_STANDARD`, or `FACEBOOK_STORY_MOBILE` |\n| `page_type` | `data-page-type` | **Deprecated**: One of: `rightcolumn`, `desktopfeed`, or `mobile` |\n| `targeting` | `data-targeting` | JSON-encoded targeting spec |\n| `post` | `data-post` | JSON-encoded post specification according to the Pages API documentation |\n\n## Graph API Integration\n\nPreviews can also be generated using the Graph API. To generate a plugin-style preview, simply specify the additional parameter `ad_format` as described in the table above.", "keyPoints": ["Ad Preview Plugin allows advertisers to preview ads directly on their websites", "Supports multiple ad formats: Right Hand Column, Desktop Feed, Mobile Feed, and Facebook Story Mobile", "Requires Facebook Login authentication and appropriate permissions for accessing Creative, Ad Group, or Ad Account", "Can be implemented using either Social Plugins or Graph API calls", "The ad_format parameter replaces the deprecated page_type parameter"], "apiEndpoints": ["/docs/reference/ads-api/generatepreview/"], "parameters": ["creative", "creative_id", "adgroup_id", "ad_format", "ad_account_id", "targeting", "post", "page_type"], "examples": [], "tags": ["Facebook Marketing API", "Ad Preview", "Social Plugin", "Graph API", "Ad Creative", "Ad Format", "Authentication"], "relatedTopics": ["Creative Spec", "Adgroup ID", "Creative ID", "Facebook Login", "Targeting Spec", "Pages API", "Graph API"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/ad-preview-plugin/v23.0", "processedAt": "2025-06-25T15:43:33.908Z", "processor": "openrouter-claude-sonnet-4"}