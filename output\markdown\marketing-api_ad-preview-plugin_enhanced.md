# Facebook Marketing API - Ad Preview Plugin

## Summary
The Ad Preview Plugin enables advertisers to generate previews of ads on their websites using Creative Specs, Adgroup IDs, or Creative IDs. Previews can be generated through Social Plugins or the Graph API for various ad formats including Right Hand Column, Feed, and Mobile.

## Key Points
- Ad Preview Plugin allows advertisers to preview ads directly on their websites
- Supports multiple ad formats: Right Hand Column, Desktop Feed, Mobile Feed, and Facebook Story Mobile
- Requires Facebook Login authentication and appropriate permissions for accessing Creative, Ad Group, or Ad Account
- Can be implemented using either Social Plugins or Graph API calls
- The ad_format parameter replaces the deprecated page_type parameter

## API Endpoints
- `/docs/reference/ads-api/generatepreview/`

## Parameters
- creative
- creative_id
- adgroup_id
- ad_format
- ad_account_id
- targeting
- post
- page_type

## Content
# Ad Preview Plugin

The Ad Preview plugin is the easiest way for advertisers to preview ads on their own websites.

The plugin enables you to generate Right Hand Column, Feed, or Mobile previews of an ad by specifying a Creative Spec, Adgroup ID or Creative ID. Previews can either be generated using a Social Plugin or through the Graph API.

## Parameters

### Required Parameters
- One of: `creative`, `creative_id`, or `adgroup_id`
- `ad_format` (replaces the deprecated `page_type` parameter)

### Optional Parameters
- `ad_account_id`
- `targeting`
- `post`

### Authentication Requirements
The preview plugin requires you to be logged in with Facebook Login. If `creative_id`, `adgroup_id`, or `ad_account_id` is used, you must also have the permissions to access the Creative, Ad Group, or Ad Account respectively.

## Parameter Reference

| Setting | HTML5 Attribute | Description |
|---------|------------------|-------------|
| `ad_account_id` | `data-ad-account-id` | Required when specifying a creative that uses `image_hash` |
| `adgroup_id` | `data-adgroup-id` | Adgroup ID returned from a Graph API call |
| `creative` | `data-creative` | JSON-encoded creative spec |
| `creative_id` | `data-creative-id` | Creative ID returned from a Graph API call |
| `ad_format` | `data-ad-format` | One of: `RIGHT_COLUMN_STANDARD`, `DESKTOP_FEED_STANDARD`, `MOBILE_FEED_STANDARD`, or `FACEBOOK_STORY_MOBILE` |
| `page_type` | `data-page-type` | **Deprecated**: One of: `rightcolumn`, `desktopfeed`, or `mobile` |
| `targeting` | `data-targeting` | JSON-encoded targeting spec |
| `post` | `data-post` | JSON-encoded post specification according to the Pages API documentation |

## Graph API Integration

Previews can also be generated using the Graph API. To generate a plugin-style preview, simply specify the additional parameter `ad_format` as described in the table above.

---
**Tags:** Facebook Marketing API, Ad Preview, Social Plugin, Graph API, Ad Creative, Ad Format, Authentication
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/ad-preview-plugin/v23.0
**Processed:** 2025-06-25T15:43:33.908Z