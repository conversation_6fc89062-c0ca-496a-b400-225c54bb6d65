{"title": "Facebook Marketing API Catalog", "summary": "Facebook catalogs are containers for product information and inventory that enable various commerce and advertising features. They support Collection Ads, Commerce distribution, Advantage+ Catalog Ads, Instagram Shopping, and WhatsApp conversational commerce.", "content": "# Catalog\n\nA Facebook catalog is an object (or container) of information about your products and where you can upload your inventory. Learn more about [product catalog](/docs/marketing-api/catalog/overview).\n\n## Common Uses\n\n- **[Collection Ads](https://developers.facebook.com/docs/marketing-api/guides/collection)** — Use them in immersive formats.\n- **[Commerce](https://developers.facebook.com/docs/commerce-platform/catalog/)** — Distribute products in Marketplace.\n- **[Advantage+ Catalog Ads](https://developers.facebook.com/docs/marketing-api/dynamic-ads)** — Feature products in different formats to be served dynamically as personalized ads.\n- **Instagram Shopping** — Feature in Instagram Shopping experiences, such as product tags on Instagram and soon on Instagram Shops.\n- **WhatsApp** — Feature in conversational commerce in WhatsApp.\n\n## Documentation Contents\n\n### [Overview](/docs/marketing-api/catalog/overview)\nLearn more about catalog and its components.\n\n### [Get Started](/docs/marketing-api/catalog/getting-started)\nLearn how to successfully set up a catalog for commerce or Advantage+ catalog ads, and more.\n\n### [Guides](/docs/marketing-api/catalog/guides)\nLearn more about the various guides and how to use them in your catalog.\n\n### [Best Practices](https://developers.facebook.com/docs/marketing-api/catalog/best-practices)\nTips for using catalog effectively.\n\n### [Reference](/docs/marketing-api/catalog/reference)\nProduct specifications and endpoint references.\n\n### [Support](/docs/marketing-api/catalog/support)\nSolutions to common problems and troubleshooting tips.\n\n## See Also\n\n- [Catalog Batch API](/docs/marketing-api/catalog-batch)", "keyPoints": ["Facebook catalogs are containers for product information and inventory management", "Catalogs enable Collection Ads, Commerce distribution, Advantage+ Catalog Ads, Instagram Shopping, and WhatsApp commerce", "Comprehensive documentation includes overview, setup guides, best practices, API reference, and support resources", "Catalogs support dynamic personalized advertising and immersive shopping experiences", "Integration available across Facebook, Instagram, Marketplace, and WhatsApp platforms"], "apiEndpoints": [], "parameters": [], "examples": [], "tags": ["catalog", "product-catalog", "commerce", "advertising", "collection-ads", "advantage-plus", "instagram-shopping", "whatsapp-commerce", "marketplace", "dynamic-ads"], "relatedTopics": ["Collection Ads", "Commerce Platform", "Advantage+ Catalog Ads", "Dynamic Ads", "Instagram Shopping", "WhatsApp Commerce", "Marketplace", "Catalog Batch API", "Product Catalog Overview"], "difficulty": "beginner", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-api/catalog", "processedAt": "2025-06-25T15:50:18.334Z", "processor": "openrouter-claude-sonnet-4"}