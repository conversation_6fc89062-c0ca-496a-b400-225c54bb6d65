{"title": "Facebook Marketing API - Ad Account DSA Recommendations", "summary": "This endpoint provides DSA (Digital Services Act) recommendations for ad accounts targeting EU regions. It returns predicted beneficiary and payor strings based on recent account activity to help comply with EU DSA requirements.", "content": "# Ad Account DSA Recommendations\n\nAs part of the requirements set forth by the European Union (EU) Digital Services Act (DSA), Facebook requires ads targeting any part of the EU to provide string values defining the beneficiary and payor of the ad being created. This API endpoint outputs a list of strings that Facebook has identified as likely beneficiary/payer values based on recent activity of the ad account.\n\n**Important Note:** While the predicted values often match what advertisers manually input for their DSA Beneficiary/Payor, Facebook doesn't guarantee accuracy. Users should review recommendations before publishing campaigns.\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/dsa_recommendations\n```\n\n### Example Request\n\n**HTTP:**\n```http\nGET /v23.0/{ad-account-id}/dsa_recommendations HTTP/1.1\nHost: graph.facebook.com\n```\n\n**PHP SDK:**\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/dsa_recommendations',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n**JavaScript SDK:**\n```javascript\nFB.api(\n    \"/{ad-account-id}/dsa_recommendations\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Format\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Fields\n- **data**: A list of AdAccountDsaRecommendations nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Error Codes\n| Error | Description |\n|-------|-------------|\n| 200   | Permissions error |\n\n## Operations\n\n- **Creating**: Not supported on this endpoint\n- **Updating**: Not supported on this endpoint  \n- **Deleting**: Not supported on this endpoint", "keyPoints": ["Required for ads targeting EU regions to comply with Digital Services Act (DSA)", "Returns predicted beneficiary and payor strings based on account activity", "Predictions are suggestions only - manual review required before campaign publishing", "Read-only endpoint - no create, update, or delete operations supported", "Returns paginated list of AdAccountDsaRecommendations nodes"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/dsa_recommendations"], "parameters": [], "examples": ["GET /v23.0/{ad-account-id}/dsa_recommendations HTTP/1.1", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest implementation", "iOS SDK FBSDKGraphRequest implementation"], "tags": ["DSA", "Digital Services Act", "EU compliance", "Ad Account", "Marketing API", "Recommendations", "Beneficiary", "Payor"], "relatedTopics": ["Ad Account DSA Recommendations field reference", "Graph API pagination", "EU Digital Services Act compliance", "Facebook Marketing API", "Ad targeting regulations"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/dsa_recommendations/", "processedAt": "2025-06-25T16:13:59.384Z", "processor": "openrouter-claude-sonnet-4"}