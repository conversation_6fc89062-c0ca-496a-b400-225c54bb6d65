# Facebook Marketing API - Ad Account Campaigns Reference

## Summary
Complete reference documentation for managing ad campaigns within Facebook ad accounts, including reading, creating, updating, and deleting campaigns through the Marketing API v23.0.

## Key Points
- Campaigns are the top-level structure for organizing Facebook ads within an ad account
- Budget can be set at either campaign level (shared) or ad set level, but not both
- Special ad categories must be declared for certain types of campaigns (employment, housing, etc.)
- Bid strategy determines how Facebook optimizes for your campaign objectives
- Campaign objectives define the primary goal and affect available optimization options

## API Endpoints
- `GET /v23.0/act_<AD_ACCOUNT_ID>/campaigns`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/campaigns`
- `DELETE /v23.0/act_<AD_ACCOUNT_ID>/campaigns`

## Parameters
- name
- objective
- status
- special_ad_categories
- bid_strategy
- daily_budget
- lifetime_budget
- effective_status
- promoted_object
- buying_type
- spend_cap

## Content
# Ad Account, Ad Campaigns

The ad campaigns associated with a given ad account.

## Important Updates

- **May 1, 2018**: Marketing API 3.0 removed `kpi_custom_conversion_id`, `kpi_type`, and `kpi_results`
- **September 15, 2022**: Marketing API v15.0 no longer allows creation of incremental conversion optimization campaigns
- **Marketing API v15.0**: Advertisers can no longer create Special Ad Audiences

## Reading Campaigns

Returns the campaigns under this ad account. A request with no filters returns only campaigns that were not archived or deleted.

### Endpoint
```
GET /v23.0/act_<AD_ACCOUNT_ID>/campaigns
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `date_preset` | enum | Predefined date range for aggregating insights metrics |
| `effective_status` | list<enum> | Filter campaigns by status (ACTIVE, PAUSED, DELETED, etc.) |
| `is_completed` | boolean | Return completed campaigns if true |
| `time_range` | object | Custom date range with 'since' and 'until' fields |

### Response Structure

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

- **data**: Array of Campaign objects
- **paging**: Pagination information
- **summary**: Aggregated information (insights, total_count)

## Creating Campaigns

Create a new campaign by making a POST request to the campaigns edge.

### Endpoint
```
POST /v23.0/act_<AD_ACCOUNT_ID>/campaigns
```

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `name` | string | Campaign name (supports emoji) |
| `objective` | enum | Campaign objective (OUTCOME_TRAFFIC, CONVERSIONS, etc.) |
| `status` | enum | Campaign status (ACTIVE, PAUSED) |
| `special_ad_categories` | array | Special ad category declarations |

### Key Optional Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `bid_strategy` | enum | Bidding strategy (LOWEST_COST_WITHOUT_CAP, etc.) |
| `daily_budget` | int64 | Daily budget in currency subunits |
| `lifetime_budget` | int64 | Lifetime budget in currency subunits |
| `buying_type` | string | AUCTION (default) or RESERVED |
| `promoted_object` | object | Object being promoted across ads |
| `spend_cap` | int64 | Maximum spend limit |
| `start_time` | datetime | Campaign start time |
| `stop_time` | datetime | Campaign end time |

### Bid Strategies

- **LOWEST_COST_WITHOUT_CAP**: Automatic bidding for maximum results
- **LOWEST_COST_WITH_BID_CAP**: Manual maximum-cost bidding with bid limit
- **COST_CAP**: Cost control bidding
- **LOWEST_COST_WITH_MIN_ROAS**: Minimum ROAS bidding

### Campaign Objectives

Supported objectives include:
- OUTCOME_TRAFFIC
- OUTCOME_SALES
- OUTCOME_LEADS
- CONVERSIONS
- BRAND_AWARENESS
- And many more...

## Updating Campaigns

Campaign updates are not supported through this endpoint.

## Deleting Campaigns

Delete campaigns from an ad account using DELETE request.

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `delete_strategy` | enum | DELETE_ANY, DELETE_OLDEST, DELETE_ARCHIVED_BEFORE |
| `object_count` | integer | Number of objects to delete |
| `before_date` | datetime | Delete campaigns before this date |

## Error Codes

Common error codes:
- **100**: Invalid parameter
- **190**: Invalid OAuth 2.0 Access Token
- **200**: Permissions error
- **2635**: Deprecated API version
- **80004**: Rate limiting - too many calls

## Code Examples

### Reading Campaigns (cURL)
```bash
curl -X GET -G \
  -d 'effective_status=["ACTIVE","PAUSED"]' \
  -d 'fields="name,objective"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/campaigns
```

### Creating a Campaign (cURL)
```bash
curl -X POST \
  -F 'name="My campaign"' \
  -F 'objective="OUTCOME_TRAFFIC"' \
  -F 'status="PAUSED"' \
  -F 'special_ad_categories=[]' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/campaigns
```

## Examples
Reading campaigns with status filter

Creating a traffic campaign with OUTCOME_TRAFFIC objective

Setting bid strategy and budget parameters

Using special ad categories for compliance

---
**Tags:** Facebook Marketing API, Campaigns, Ad Account, Advertising, API Reference, CRUD Operations  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/campaigns/  
**Processed:** 2025-06-25T16:13:37.321Z