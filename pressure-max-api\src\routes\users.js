const express = require('express');
const { body, validationResult } = require('express-validator');

const { asyncHandler, ValidationError } = require('../middleware/errorHandler');
const authMiddleware = require('../middleware/auth');
const User = require('../models/User');
const logger = require('../config/logger');

const router = express.Router();

/**
 * @swagger
 * /users/profile:
 *   get:
 *     summary: Get current user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 */
router.get('/profile', asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id);
  
  if (!user) {
    throw new NotFoundError('User not found');
  }

  res.json(user.toJSON());
}));

/**
 * @swagger
 * /users/profile:
 *   put:
 *     summary: Update current user profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               timezone:
 *                 type: string
 *     responses:
 *       200:
 *         description: Profile updated successfully
 */
router.put('/profile', [
  body('firstName').optional().trim().isLength({ min: 1 }).withMessage('First name cannot be empty'),
  body('lastName').optional().trim().isLength({ min: 1 }).withMessage('Last name cannot be empty'),
  body('phone').optional().isMobilePhone().withMessage('Valid phone number required'),
  body('timezone').optional().isString().withMessage('Valid timezone required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const user = await User.findById(req.user.id);
  
  if (!user) {
    throw new NotFoundError('User not found');
  }

  const allowedUpdates = ['firstName', 'lastName', 'phone', 'timezone'];
  const updates = {};
  
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  if (Object.keys(updates).length === 0) {
    throw new ValidationError('No valid fields to update');
  }

  await user.update(updates);

  logger.audit('profile_updated', req.user.id, { updates: Object.keys(updates) });

  res.json({
    message: 'Profile updated successfully',
    user: user.toJSON()
  });
}));

/**
 * @swagger
 * /users/change-password:
 *   post:
 *     summary: Change user password
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Password changed successfully
 */
router.post('/change-password', [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword').isLength({ min: 8 }).withMessage('New password must be at least 8 characters')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { currentPassword, newPassword } = req.body;

  const user = await User.findById(req.user.id);
  
  if (!user) {
    throw new NotFoundError('User not found');
  }

  // Verify current password
  const isValidPassword = await authMiddleware.comparePassword(currentPassword, user.password);
  if (!isValidPassword) {
    throw new ValidationError('Current password is incorrect');
  }

  // Hash new password
  const hashedNewPassword = await authMiddleware.hashPassword(newPassword);
  
  // Update password
  await user.updatePassword(hashedNewPassword);

  logger.audit('password_changed', req.user.id);

  res.json({
    message: 'Password changed successfully'
  });
}));

/**
 * @swagger
 * /users/team:
 *   get:
 *     summary: Get team members (tenant users)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Team members retrieved successfully
 */
router.get('/team', authMiddleware.requireTenant, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, search, role } = req.query;

  const options = {
    page: parseInt(page),
    limit: Math.min(parseInt(limit), 100), // Max 100 per page
    search,
    role
  };

  const result = await User.findByTenant(req.user.tenantId, options);

  res.json({
    users: result.users.map(user => user.toJSON()),
    pagination: result.pagination
  });
}));

module.exports = router;
