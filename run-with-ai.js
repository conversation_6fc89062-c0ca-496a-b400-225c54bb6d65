#!/usr/bin/env node

// Load environment variables
require('dotenv').config();

const FacebookMarketingAPIScraper = require('./scraper');

async function main() {
  console.log('🚀 Starting Facebook Marketing API Scraper with AI Processing');
  console.log('🤖 Using Claude Sonnet 4 via OpenRouter for content enhancement');
  console.log('=' .repeat(60));
  
  // Check if API key is configured
  if (!process.env.OPENROUTER_KEY) {
    console.error('❌ OPENROUTER_KEY not found in environment variables');
    console.log('\n🔧 Setup instructions:');
    console.log('1. Copy .env.example to .env');
    console.log('2. Add your OpenRouter API key to .env');
    console.log('3. Get your API key from: https://openrouter.ai/keys');
    process.exit(1);
  }
  
  const scraper = new FacebookMarketingAPIScraper();
  
  try {
    // Check for test mode
    if (process.argv.includes('--test')) {
      console.log('🧪 Running in test mode with AI processing...\n');
      await scraper.test();
    } else {
      console.log('📄 Starting full scraping with AI processing...\n');
      await scraper.run();
    }
    
    console.log('\n🎉 Scraping completed successfully!');
    console.log('📁 Check the ./output/ directory for results');
    console.log('🤖 AI-enhanced content is in ./output/ai-processed/');
    
  } catch (error) {
    console.error('\n💥 Scraping failed:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received interrupt signal. Cleaning up...');
  process.exit(0);
});

// Run the scraper
main();
