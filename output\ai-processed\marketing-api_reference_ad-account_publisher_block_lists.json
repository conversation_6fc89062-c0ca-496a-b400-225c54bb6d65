{"title": "Facebook Marketing API - Ad Account Publisher Block Lists", "summary": "Documentation for managing publisher block lists within Facebook ad accounts. This endpoint allows creating publisher block lists to control where ads are displayed, but does not support reading, updating, or deleting operations.", "content": "# Ad Account Publisher Block Lists\n\n## Overview\n\nThe Publisher Block Lists endpoint allows you to manage lists of publishers where you don't want your ads to appear within a Facebook ad account.\n\n## Supported Operations\n\n### Reading\nReading operations are **not supported** on this endpoint.\n\n### Creating\nYou can create a new publisher block list by making a POST request to the `publisher_block_lists` edge.\n\n**Endpoint:**\n```\nPOST /act_{ad_account_id}/publisher_block_lists\n```\n\nWhen posting to this edge, a [PublisherBlockList](/docs/marketing-api/reference/publisher-block-list/) object will be created.\n\n#### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `name` | string | Name of the block list |\n\n#### Return Type\n\nThis endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will return the node represented by `id`.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n\n### Updating\nUpdating operations are **not supported** on this endpoint.\n\n### Deleting\nDeleting operations are **not supported** on this endpoint.\n\n## API Version\nThis documentation is for Graph API version **v23.0**.", "keyPoints": ["Only CREATE operations are supported - reading, updating, and deleting are not available", "Creates PublisherBlockList objects to control ad placement exclusions", "Requires only a 'name' parameter to create a new block list", "Returns the created object ID and supports read-after-write functionality", "Part of the ad account edge structure in Facebook Marketing API"], "apiEndpoints": ["POST /act_{ad_account_id}/publisher_block_lists"], "parameters": ["name (string) - Name of the block list"], "examples": ["POST request to create publisher block list with name parameter", "Return structure with numeric string ID"], "tags": ["Facebook Marketing API", "Publisher Block Lists", "Ad Account", "Graph API", "Ad Placement Control"], "relatedTopics": ["PublisherBlockList reference", "Ad Account management", "Graph API read-after-write", "Ad placement controls", "Marketing API permissions"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/publisher_block_lists/", "processedAt": "2025-06-25T15:33:10.774Z", "processor": "openrouter-claude-sonnet-4"}