# Ad Account Tracking

Graph API Version

[v23.0](#)

# Ad Account Tracking

## Reading

AdAccountTracking

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Ftracking&version=v23.0)

```
`GET /v23.0/{ad-account-id}/tracking HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of AdAccountTrackingData nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

190

Invalid OAuth 2.0 Access Token

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

## Creating

You can make a POST request to `tracking` edge from the following paths:

*   [`/act_{ad_account_id}/tracking`](/docs/marketing-api/reference/ad-account/tracking/)

When posting to this edge, no Graph object will be created.

### Parameters

Parameter

Description

`tracking_specs`

Object

Tracking specs to add to the account level

Required

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node to which you POSTed.

Struct {

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.