{"title": "Facebook Marketing API - Ad Account Ads Reference", "summary": "Complete reference documentation for managing ads within a Facebook ad account, including reading, creating, and managing ad operations through the Marketing API. Covers endpoints, parameters, error codes, and code examples across multiple programming languages.", "content": "# Ad Account Ads\n\nAds belonging to this ad account.\n\n## Reading\n\nRetrieve ads belonging to a specific ad account.\n\n### Endpoint\n```\nGET /v23.0/act_{ad-account-id}/ads\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `date_preset` | enum | Predefined date range for aggregating insights metrics. Options: today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year |\n| `effective_status` | list<string> | Filter ads by effective status |\n| `time_range` | object | Date range object with 'since' and 'until' fields (YYYY-MM-DD format) |\n| `updated_since` | integer | Time since the Ad has been updated |\n\n### Response Format\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Response Fields\n- **data**: Array of Ad nodes\n- **paging**: Pagination information\n- **summary**: Aggregated information including insights and total_count\n\n### Code Examples\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->get(\n    '/act_{ad-account-id}/ads',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n}\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/act_{ad-account-id}/ads\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n## Creating\n\nCreate a new ad within an ad account.\n\n### Endpoint\n```\nPOST /v23.0/act_{ad-account-id}/ads\n```\n\n### Required Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `name` | string | Name of the ad (supports emoji) |\n| `adset_id` | int64 | The ID of the ad set (required unless adset_spec is provided) |\n| `creative` | object | Creative ID or creative spec object |\n| `status` | enum | Ad status: ACTIVE or PAUSED (for creation) |\n\n### Optional Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `ad_schedule_start_time` | datetime | Start time for individual ad scheduling |\n| `ad_schedule_end_time` | datetime | End time for individual ad scheduling |\n| `adlabels` | list<Object> | Ad labels associated with this ad |\n| `adset_spec` | object | Ad set specification (alternative to adset_id) |\n| `audience_id` | string | The ID of the audience |\n| `conversion_domain` | string | Domain where conversions happen |\n| `display_sequence` | int64 | Sequence of the ad within the campaign |\n| `execution_options` | list<enum> | Validation and execution options |\n| `priority` | int64 | Ad priority |\n| `tracking_specs` | object | Tracking and conversion specifications |\n\n### Creative Parameter Format\n\n```json\n// Using creative ID\n{\"creative_id\": \"<CREATIVE_ID>\"}\n\n// Using creative spec\n{\"creative\": {\"name\": \"<NAME>\", \"object_story_spec\": \"<SPEC>\"}}\n```\n\n### Example Request\n\n```bash\ncurl -X POST \\\n  -F 'name=\"My Ad\"' \\\n  -F 'adset_id=\"<AD_SET_ID>\"' \\\n  -F 'creative={\"creative_id\": \"<CREATIVE_ID>\"}' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads\n```\n\n### Return Type\n\n```json\n{\n  \"id\": \"numeric_string\",\n  \"success\": true\n}\n```\n\n## Error Codes\n\n### Common Errors\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | Action deemed abusive or disallowed |\n| 500 | Message contains banned content |\n| 613 | API rate limit exceeded |\n| 1500 | Invalid URL supplied |\n| 2500 | Error parsing graph query |\n| 2635 | Deprecated API version |\n| 3018 | Start date beyond 37 months from current date |\n| 80004 | Too many calls to ad account (rate limiting) |\n\n## Operations Not Supported\n\n- **Updating**: Direct updates to ads through this endpoint are not supported\n- **Deleting**: Direct deletion of ads through this endpoint is not supported\n\n## Notes\n\n- Newly created ads go through ad review with status `PENDING_REVIEW` before reverting to selected status\n- For testing, use `PAUSED` status to avoid accidental spend\n- The `bid_amount` parameter is deprecated and should be set at the ad set level\n- Ad scheduling parameters are only available for sales and app promotion campaigns", "keyPoints": ["Supports reading ads from ad accounts with filtering by status, date ranges, and update times", "Creating ads requires name, adset_id (or adset_spec), creative specification, and status", "New ads undergo review process with PENDING_REVIEW status before becoming active", "Direct updating and deleting operations are not supported through this endpoint", "Comprehensive error handling with specific codes for rate limiting, permissions, and validation issues"], "apiEndpoints": ["GET /v23.0/act_{ad-account-id}/ads", "POST /v23.0/act_{ad-account-id}/ads"], "parameters": ["date_preset", "effective_status", "time_range", "updated_since", "name", "adset_id", "creative", "status", "ad_schedule_start_time", "ad_schedule_end_time", "conversion_domain", "tracking_specs"], "examples": ["PHP SDK GET request with error handling", "JavaScript SDK API call", "cURL POST request for creating ads", "Creative specification formats", "Android and iOS SDK implementations"], "tags": ["Facebook Marketing API", "Ads Management", "Graph API", "Ad Creation", "API Reference", "Social Media Marketing"], "relatedTopics": ["Ad Sets", "Ad Creatives", "Ad Account Management", "Campaign Management", "Tracking Specs", "Ad Insights", "Rate Limiting", "O<PERSON>uth <PERSON>"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/", "processedAt": "2025-06-25T15:17:04.995Z", "processor": "openrouter-claude-sonnet-4"}