{"title": "Facebook Marketing API - Ad Account Ads Reference", "summary": "Complete reference documentation for managing ads within a Facebook ad account, including reading, creating, and managing ad operations through the Marketing API. Covers endpoints, parameters, error handling, and code examples across multiple programming languages.", "content": "# Ad Account Ads\n\nAds belonging to this ad account.\n\n## Reading\n\nRetrieve ads belonging to this ad account.\n\n### Endpoint\n```\nGET /v23.0/act_{ad-account-id}/ads\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `date_preset` | enum | Predefined date range for aggregating insights metrics. Options: today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year |\n| `effective_status` | list<string> | Filter ads by effective status |\n| `time_range` | object | Date range object with 'since' and 'until' fields |\n| `since` | datetime | Start date in \"YYYY-MM-DD\" format |\n| `until` | datetime | End date in \"YYYY-MM-DD\" format |\n| `updated_since` | integer | Time since the Ad has been updated |\n\n### Response Format\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Response Fields\n\n- **data**: A list of Ad nodes\n- **paging**: Pagination information\n- **summary**: Aggregated information including:\n  - `insights`: Analytics summary for all objects\n  - `total_count`: Total number of Ads returned\n\n### Code Examples\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->get(\n    '/act_{ad-account-id}/ads',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/act_{ad-account-id}/ads\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n## Creating\n\nCreate a new ad within the ad account.\n\n### Endpoint\n```\nPOST /v23.0/act_{ad-account-id}/ads\n```\n\n### Required Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `name` | string | Name of the ad (supports emoji) |\n| `adset_id` | int64 | The ID of the ad set (required unless adset_spec is provided) |\n| `creative` | object | Creative ID or spec: `{\"creative_id\": \"<CREATIVE_ID>\"}` |\n| `status` | enum | Ad status: ACTIVE, PAUSED, DELETED, ARCHIVED |\n\n### Optional Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `ad_schedule_start_time` | datetime | Start time for individual ad scheduling |\n| `ad_schedule_end_time` | datetime | End time for individual ad scheduling |\n| `adlabels` | list<Object> | Ad labels associated with this ad |\n| `adset_spec` | object | Ad set specification (alternative to adset_id) |\n| `audience_id` | string | The ID of the audience |\n| `conversion_domain` | string | Domain where conversions happen |\n| `display_sequence` | int64 | Sequence of the ad within the campaign |\n| `engagement_audience` | boolean | Create audience based on ad engagement |\n| `execution_options` | list<enum> | Options: validate_only, synchronous_ad_review, include_recommendations |\n| `priority` | int64 | Ad priority |\n| `source_ad_id` | string | ID of the source ad |\n| `tracking_specs` | object | Tracking and conversion specifications |\n\n### Example Request\n\n```bash\ncurl -X POST \\\n  -F 'name=\"My Ad\"' \\\n  -F 'adset_id=\"<AD_SET_ID>\"' \\\n  -F 'creative={\n       \"creative_id\": \"<CREATIVE_ID>\"\n     }' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads\n```\n\n### Return Type\n\n```json\n{\n  \"id\": \"numeric_string\",\n  \"success\": true\n}\n```\n\n## Error Codes\n\n### Reading Errors\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 2500 | Error parsing graph query |\n| 2635 | Deprecated API version |\n| 3018 | Start date beyond 37 months |\n| 80004 | Rate limit exceeded |\n\n### Creating Errors\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 194 | Missing required parameter |\n| 200 | Permissions error |\n| 368 | Action deemed abusive |\n| 500 | Message contains banned content |\n| 613 | Rate limit exceeded |\n| 1500 | Invalid URL supplied |\n| 2635 | Deprecated API version |\n| 80004 | Rate limit exceeded |\n\n## Updating and Deleting\n\nUpdating and deleting operations are not supported on this endpoint.", "keyPoints": ["Supports reading ads from an ad account with filtering and pagination options", "Creating ads requires name, adset_id (or adset_spec), creative, and status parameters", "Provides comprehensive error handling with specific error codes for different scenarios", "Supports multiple programming languages with SDK examples (PHP, JavaScript, Android, iOS)", "Includes advanced features like ad scheduling, audience targeting, and tracking specifications"], "apiEndpoints": ["GET /v23.0/act_{ad-account-id}/ads", "POST /v23.0/act_{ad-account-id}/ads"], "parameters": ["date_preset", "effective_status", "time_range", "since", "until", "updated_since", "name", "adset_id", "creative", "status", "ad_schedule_start_time", "ad_schedule_end_time", "adlabels", "adset_spec", "audience_id", "conversion_domain", "execution_options", "tracking_specs"], "examples": ["PHP SDK GET request with error handling", "JavaScript SDK API call", "cURL POST request for creating ads", "Android SDK GraphRequest implementation", "iOS SDK FBSDKGraphRequest usage"], "tags": ["Facebook Marketing API", "Ads Management", "Graph API", "Ad Account", "REST API", "Social Media Marketing"], "relatedTopics": ["Ad Sets", "Ad Creatives", "Ad Insights", "Campaign Management", "Audience Targeting", "Conversion Tracking", "Rate Limiting", "O<PERSON>uth <PERSON>"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/", "processedAt": "2025-06-25T16:21:06.230Z", "processor": "openrouter-claude-sonnet-4"}