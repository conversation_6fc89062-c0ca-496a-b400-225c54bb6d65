{"title": "Facebook Marketing API - Ad Account Ad Creatives Reference", "summary": "Complete reference for managing ad creatives within Facebook ad accounts, including reading existing creatives, creating new ones with various media types and configurations, and understanding the available parameters and error handling.", "content": "# Ad Account Ad Creatives\n\nThe Ad Creatives endpoint manages creative content for an ad account that can be used in ads, including images, videos, and other media. Using an ad account creative for a particular ad is subject to validation rules based on the ad type and other requirements.\n\n## Reading Ad Creatives\n\nTo retrieve an account's ad creatives, make an HTTP GET call to `/act_{ad_account_id}/adcreatives`.\n\n### Example Request\n\n```bash\ncurl -G \\\n  -d 'fields=name' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Response Format\n\nReading from this edge returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Response Fields\n\n- **data**: A list of AdCreative nodes\n- **paging**: Pagination information for navigating through results\n- **summary**: Aggregated information about the edge, such as counts\n\n### Summary Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `total_count` | unsigned int32 | Total number of creatives in the ad account |\n\n### Reading Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 200 | Permissions error |\n| 100 | Invalid parameter |\n| 80004 | Too many calls to this ad-account. Rate limiting applied |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 2500 | Error parsing graph query |\n\n## Creating Ad Creatives\n\nYou can create new ad creatives by making a POST request to `/act_{ad_account_id}/adcreatives`.\n\n### Limitations\n\n- When creating ad creatives, if the `object_story_id` being used is already in use by an existing creative, the API will return the existing creative_id instead of creating a new one\n- Using `radius` can cause errors when targeting multiple locations\n\n### Example Creation Request\n\n```bash\ncurl -X POST \\\n  -F 'name=\"Sample Promoted Post\"' \\\n  -F 'object_story_id=\"<PAGE_ID>_<POST_ID>\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Key Parameters\n\n#### Basic Creative Information\n\n- **name** (string): Name of the ad creative as seen in the ad account's library\n- **actor_id** (int64): The actor ID (Page ID) of this creative\n- **body** (string): The body text of the ad (supports emoji)\n- **title** (string): Title for the creative\n\n#### Media Specifications\n\n- **image_file** (string): Reference to a local image file (max 8MB)\n- **image_hash** (string): Hash for an uploaded image\n- **image_url** (URL): URL for the image in the creative\n- **image_crops** (object): Crop dimensions for the image\n\n#### Asset Feed Specification\n\n```json\n{\n  \"images\": [\n    {\n      \"hash\": \"string\",\n      \"url\": \"URL\",\n      \"image_crops\": {},\n      \"url_tags\": \"string\"\n    }\n  ],\n  \"videos\": [\n    {\n      \"video_id\": \"int64\",\n      \"thumbnail_id\": \"int64\",\n      \"thumbnail_url\": \"URL\"\n    }\n  ],\n  \"bodies\": [\n    {\n      \"text\": \"string\",\n      \"url_tags\": \"string\"\n    }\n  ],\n  \"titles\": [\n    {\n      \"text\": \"string\",\n      \"url_tags\": \"string\"\n    }\n  ]\n}\n```\n\n#### Call to Action Configuration\n\n```json\n{\n  \"type\": \"LEARN_MORE\",\n  \"value\": {\n    \"link\": \"https://example.com\",\n    \"link_title\": \"Learn More\",\n    \"link_description\": \"Click to learn more\"\n  }\n}\n```\n\n#### Platform Customizations\n\nSpecify different media for different Facebook placements:\n\n```json\n{\n  \"instagram\": {\n    \"image_url\": \"URL\",\n    \"image_hash\": \"string\",\n    \"image_crops\": {}\n  }\n}\n```\n\n### Advanced Features\n\n#### Dynamic Creative Optimization\n\n- **degrees_of_freedom_spec**: Specifies transformation types enabled for the creative\n- **creative_features_spec**: Advanced creative features like product metadata automation\n\n#### Branded Content\n\n```json\n{\n  \"partners\": [\n    {\n      \"fb_page_id\": \"numeric_string\",\n      \"ig_user_id\": \"numeric_string\"\n    }\n  ]\n}\n```\n\n#### Interactive Components\n\n```json\n{\n  \"child_attachments\": [\n    {\n      \"components\": [\n        {\n          \"poll_spec\": {\n            \"option_a_text\": \"Option A\",\n            \"option_b_text\": \"Option B\",\n            \"question_text\": \"Your question?\"\n          }\n        }\n      ]\n    }\n  ]\n}\n```\n\n### Return Type\n\nSuccessful creation returns:\n\n```json\n{\n  \"id\": \"numeric_string\",\n  \"success\": true\n}\n```\n\n### Creation Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 100 | Invalid parameter |\n| 200 | Permissions error |\n| 500 | Message contains banned content |\n| 1500 | Invalid URL supplied |\n| 80004 | Rate limiting applied |\n| 105 | Too many parameters |\n| 368 | Action deemed abusive or disallowed |\n| 194 | Missing required parameter |\n| 2635 | Deprecated API version |\n\n## Updating and Deleting\n\nBoth updating and deleting operations are not supported on this endpoint. Ad creatives are typically immutable once created.\n\n## Best Practices\n\n1. **Image Requirements**: Ensure images don't exceed 8MB and use appropriate dimensions\n2. **Content Validation**: Follow Facebook's advertising policies to avoid content rejection\n3. **Rate Limiting**: Be mindful of API rate limits, especially for bulk operations\n4. **Asset Management**: Use the asset feed specification for Dynamic Creative campaigns\n5. **Platform Optimization**: Use platform customizations for placement-specific media", "keyPoints": ["Ad creatives contain all media and content elements used in Facebook ads", "Reading creatives returns paginated results with data, paging, and summary information", "Creating creatives supports various media types including images, videos, and interactive components", "Asset feed specifications enable Dynamic Creative optimization with multiple variations", "Platform customizations allow different media for different Facebook placements"], "apiEndpoints": ["GET /act_{ad_account_id}/adcreatives", "POST /act_{ad_account_id}/adcreatives"], "parameters": ["name", "actor_id", "body", "title", "image_file", "image_hash", "image_url", "image_crops", "asset_feed_spec", "call_to_action", "platform_customizations", "object_story_id", "object_story_spec", "degrees_of_freedom_spec", "branded_content", "interactive_components_spec"], "examples": ["curl -G -d 'fields=name' -d 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives", "curl -X POST -F 'name=\"Sample Promoted Post\"' -F 'object_story_id=\"<PAGE_ID>_<POST_ID>\"' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives", "Asset feed specification with images, videos, bodies, and titles arrays", "Call to action configuration with type and value objects", "Platform customizations for Instagram-specific media"], "tags": ["Facebook Marketing API", "Ad Creatives", "Ad Account", "Media Management", "Dynamic Creative", "<PERSON><PERSON>", "Platform Customization"], "relatedTopics": ["AdCreative object reference", "Dynamic Product Ads", "Facebook Ads Guide", "Image Crops reference", "Validation and Objectives", "Branded Content Ads", "Interactive Components", "Rate Limiting", "OAuth Access Tokens"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/", "processedAt": "2025-06-25T15:13:55.119Z", "processor": "openrouter-claude-sonnet-4"}