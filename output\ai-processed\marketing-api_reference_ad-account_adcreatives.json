{"title": "Facebook Marketing API - Ad Account Ad Creatives Reference", "summary": "Complete reference for managing ad creatives within Facebook ad accounts, including reading existing creatives, creating new ones with various specifications, and understanding the available parameters and error codes.", "content": "# Ad Account Ad Creatives\n\nThe Ad Creatives endpoint manages creative content for an ad account that can be used in ads, including images, videos, and other media assets. Using an ad account creative for a particular ad is subject to validation rules based on the ad type and other requirements.\n\n## Reading Ad Creatives\n\nTo retrieve an account's ad creatives, make an HTTP GET call to:\n\n```\nGET /v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Example Request\n\n```bash\ncurl -X GET -G \\\n  -d 'fields=\"name\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Response Format\n\nReading from this edge returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Response Fields\n\n- **data**: A list of AdCreative nodes\n- **paging**: Pagination information for navigating through results\n- **summary**: Aggregated information about the edge, such as counts\n\n### Summary Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `total_count` | unsigned int32 | Total number of creatives in the ad account |\n\n## Creating Ad Creatives\n\nYou can create new ad creatives by making a POST request to the `adcreatives` edge:\n\n```\nPOST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Example Creation\n\n```bash\ncurl -X POST \\\n  -F 'name=\"Sample Promoted Post\"' \\\n  -F 'object_story_id=\"<PAGE_ID>_<POST_ID>\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Key Parameters\n\n#### Basic Creative Information\n\n- **name** (string): Name of the ad creative as seen in the ad account's library\n- **actor_id** (int64): The actor ID (Page ID) of this creative\n- **body** (string): The body text of the ad (supports emoji)\n- **title** (string): Title for the creative\n\n#### Media Specifications\n\n- **image_file** (string): Reference to a local image file (max 8MB)\n- **image_hash** (string): Hash for an uploaded image\n- **image_url** (URL): URL for the image in the creative\n- **image_crops** (object): Crop dimensions for the image\n\n#### Call to Action\n\n```json\n{\n  \"type\": \"LEARN_MORE\",\n  \"value\": {\n    \"link\": \"https://example.com\",\n    \"link_title\": \"Learn More\",\n    \"link_description\": \"Click to learn more\"\n  }\n}\n```\n\n#### Asset Feed Specification (Dynamic Creative)\n\nFor Dynamic Creative optimization:\n\n```json\n{\n  \"images\": [\n    {\n      \"hash\": \"image_hash_here\",\n      \"url\": \"https://example.com/image.jpg\"\n    }\n  ],\n  \"bodies\": [\n    {\n      \"text\": \"Primary ad text here\"\n    }\n  ],\n  \"titles\": [\n    {\n      \"text\": \"Ad headline here\"\n    }\n  ]\n}\n```\n\n#### Object Story Specification\n\nFor creating new unpublished page posts:\n\n```json\n{\n  \"page_id\": \"<PAGE_ID>\",\n  \"link_data\": {\n    \"link\": \"https://example.com\",\n    \"message\": \"Check this out!\",\n    \"name\": \"Link Title\",\n    \"description\": \"Link description\"\n  }\n}\n```\n\n### Advanced Features\n\n#### Branded Content\n\n```json\n{\n  \"partners\": [\n    {\n      \"fb_page_id\": \"<PARTNER_PAGE_ID>\",\n      \"identity_type\": \"PARTNER_CREATOR\"\n    }\n  ]\n}\n```\n\n#### Platform Customizations\n\nSpecify different media for different placements:\n\n```json\n{\n  \"instagram\": {\n    \"image_url\": \"https://example.com/instagram-image.jpg\",\n    \"image_crops\": {\n      \"100x100\": [[0, 0, 100, 100]]\n    }\n  }\n}\n```\n\n#### Interactive Components\n\nAdd polls and other interactive elements:\n\n```json\n{\n  \"components\": [\n    {\n      \"poll_spec\": {\n        \"question_text\": \"What do you think?\",\n        \"option_a_text\": \"Yes\",\n        \"option_b_text\": \"No\"\n      }\n    }\n  ]\n}\n```\n\n### Limitations\n\n1. When creating ad creatives, if the `object_story_id` is already in use by an existing creative, the API returns the existing creative_id instead of creating a new one\n2. Using `radius` can cause errors when targeting multiple locations\n3. Images cannot exceed 8MB in size\n\n### Return Type\n\nSuccessful creation returns:\n\n```json\n{\n  \"id\": \"creative_id\",\n  \"success\": true\n}\n```\n\n## Error Codes\n\n### Reading Errors\n\n| Code | Description |\n|------|-------------|\n| 200 | Permissions error |\n| 100 | Invalid parameter |\n| 80004 | Too many calls to ad account (rate limiting) |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 2500 | Error parsing graph query |\n\n### Creating Errors\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 200 | Permissions error |\n| 500 | Message contains banned content |\n| 1500 | Invalid URL supplied |\n| 80004 | Rate limiting - too many calls |\n| 105 | Too many parameters |\n| 368 | Action deemed abusive or disallowed |\n| 194 | Missing required parameter |\n| 2635 | Deprecated API version |\n\n## Updating and Deleting\n\nUpdating and deleting operations are not supported on this endpoint. Ad creatives are typically managed through creation of new versions rather than modification of existing ones.", "keyPoints": ["Ad creatives contain all media and text content used in Facebook ads", "Supports various media types including images, videos, and interactive components", "Dynamic Creative allows automatic testing of different creative variations", "Platform customizations enable different media for different placements", "Rate limiting applies - monitor API call frequency to avoid errors"], "apiEndpoints": ["GET /v23.0/act_<AD_ACCOUNT_ID>/adcreatives", "POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives"], "parameters": ["name", "actor_id", "body", "title", "image_file", "image_hash", "image_url", "call_to_action", "object_story_spec", "object_story_id", "asset_feed_spec", "platform_customizations", "branded_content", "interactive_components_spec"], "examples": ["curl -X GET -G -d 'fields=\"name\"' -d 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives", "curl -X POST -F 'name=\"Sample Promoted Post\"' -F 'object_story_id=\"<PAGE_ID>_<POST_ID>\"' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives"], "tags": ["Facebook Marketing API", "Ad Creatives", "Advertising", "Media Management", "Dynamic Creative", "Branded Content"], "relatedTopics": ["Ad Account Management", "Dynamic Product Ads", "Facebook Ads Guide", "Image Crops Reference", "Object Story Specification", "Platform Placements", "Rate Limiting", "Validation Rules"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/", "processedAt": "2025-06-25T16:11:35.485Z", "processor": "openrouter-claude-sonnet-4"}