const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  await page.goto('https://developers.facebook.com/docs/marketing-apis');
  await page.waitForLoadState('networkidle');
  
  console.log('Testing current extraction logic...');
  
  // Test the current selector logic
  const selectors = {
    mainContent: '[data-click-area="main"], ._4-u2 ._4-u3, ._588p, main, [role="main"], .content, #content, .documentation-content',
    excludeElements: 'script, style, noscript, .hidden_elem, ._2k32, [data-click-area="to_top_nav"]'
  };
  
  const content = await page.evaluate((selectors) => {
    // Try multiple selectors to find main content
    const selectorList = selectors.mainContent.split(', ');
    let mainElement = null;
    
    for (const selector of selectorList) {
      const element = document.querySelector(selector);
      if (element) {
        console.log('Found element with selector:', selector);
        mainElement = element;
        break;
      }
    }
    
    if (!mainElement) {
      return 'No main element found';
    }
    
    // Clone the element to avoid modifying the original
    const clone = mainElement.cloneNode(true);
    
    // Remove unwanted elements
    const toRemove = [
      '.hidden_elem',
      '[data-click-area="to_top_nav"]',
      'script',
      'noscript', 
      'style',
      '._2k32',
      '._1dyy', // Table of contents
      'fb\\:like',
      '.img', // Icon elements
      '[data-visualcompletion="css-img"]'
    ];
    
    // Add selectors from config if available
    if (selectors.excludeElements) {
      toRemove.push(...selectors.excludeElements.split(', '));
    }
    
    toRemove.forEach(selector => {
      try {
        const elements = clone.querySelectorAll(selector);
        console.log(`Removing ${elements.length} elements with selector: ${selector}`);
        elements.forEach(el => el.remove());
      } catch (e) {
        console.log(`Error with selector ${selector}:`, e.message);
      }
    });
    
    return clone.innerHTML.substring(0, 500);
  }, selectors);
  
  console.log('Extracted content preview:');
  console.log(content);
  
  // Test a more targeted approach - just the documentation sections
  console.log('\n--- Testing targeted approach ---');
  
  const targetedContent = await page.evaluate(() => {
    // Get all the main documentation sections
    const sections = document.querySelectorAll('._4-u2 ._4-u3');
    const combinedContent = Array.from(sections).map(section => section.innerHTML).join('\n\n');
    return combinedContent.substring(0, 500);
  });
  
  console.log('Targeted content preview:');
  console.log(targetedContent);
  
  await browser.close();
})();
