{"title": "Facebook Marketing API - Ad Videos Reference", "summary": "Complete reference documentation for the Facebook Marketing API Ad Videos endpoint, covering video upload, management, and deletion operations for ad accounts. Includes detailed parameters, return types, and error codes for video operations.", "content": "# Ad Videos\n\nThe Ad Videos endpoint allows you to manage video assets within Facebook ad accounts. This endpoint supports creating (uploading) and deleting video content for use in advertising campaigns.\n\n## Operations Overview\n\n### Reading\nReading operations are not supported on this endpoint.\n\n### Creating\nYou can upload videos by making a POST request to the `advideos` edge:\n- **Endpoint**: `/act_{ad_account_id}/advideos`\n- **Creates**: A Video object\n\n#### Parameters\n\n**Basic Video Parameters:**\n- `name` (string): The name of the video in the library\n- `title` (UTF-8 string): The name of the video being uploaded (max 255 characters, supports emoji)\n- `description` (UTF-8 string): Video description (supports emoji)\n- `source` (string): The video file encoded as form data\n- `file_url` (string): URL to video file\n- `file_size` (int64): Size of video file in bytes (for chunked upload)\n\n**Chunked Upload Parameters:**\n- `upload_phase` (enum): Phase during chunked upload - `start`, `transfer`, `finish`, `cancel`\n- `upload_session_id` (numeric string): Session ID for chunked upload\n- `start_offset` (int64): Start position in bytes of chunk being sent\n- `end_offset` (int64): End position in bytes of chunk\n- `video_file_chunk` (string): The chunk of video data\n\n**360° Video Parameters:**\n- `fisheye_video_cropped` (boolean): Whether single fisheye video is cropped\n- `front_z_rotation` (float): Front z rotation in degrees\n- `original_fov` (int64): Original field of view of source camera\n- `original_projection_type` (enum): `equirectangular`, `cubemap`, `half_equirectangular`\n\n**Slideshow Parameters:**\n- `slideshow_spec` (JSON object):\n  - `images_urls` (list<URL>): 3-7 element array of image URLs (required)\n  - `duration_ms` (integer): Duration of each image in milliseconds (default: 1000)\n  - `transition_ms` (integer): Crossfade transition duration (default: 1000)\n  - `reordering_opt_in` (boolean): Default false\n  - `music_variations_opt_in` (boolean): Default false\n\n**Other Parameters:**\n- `unpublished_content_type` (enum): Content type - `SCHEDULED`, `SCHEDULED_RECURRING`, `DRAFT`, `ADS_POST`, `INLINE_CREATED`, `PUBLISHED`, `REVIEWABLE_BRANDED_CONTENT`\n- `transcode_setting_properties` (string): Properties for computing transcode settings\n- `composer_session_id` (string): Session identifier\n- `time_since_original_post` (int64): Time since original post\n\n#### Return Type\n```json\n{\n  \"id\": \"numeric string\",\n  \"upload_session_id\": \"numeric string\",\n  \"video_id\": \"numeric string\",\n  \"start_offset\": \"numeric string\",\n  \"end_offset\": \"numeric string\",\n  \"success\": \"bool\",\n  \"skip_upload\": \"bool\",\n  \"upload_domain\": \"string\",\n  \"region_hint\": \"string\",\n  \"xpv_asset_id\": \"numeric string\",\n  \"is_xpv_single_prod\": \"bool\",\n  \"transcode_bit_rate_bps\": \"numeric string\",\n  \"transcode_dimension\": \"numeric string\",\n  \"should_expand_to_transcode_dimension\": \"bool\",\n  \"action_id\": \"string\",\n  \"gop_size_seconds\": \"numeric string\",\n  \"target_video_codec\": \"string\",\n  \"target_hdr\": \"string\",\n  \"maximum_frame_rate\": \"numeric string\"\n}\n```\n\n### Updating\nUpdate operations are not supported on this endpoint.\n\n### Deleting\nYou can remove a video from an ad account by making a DELETE request:\n- **Endpoint**: `/act_{ad_account_id}/advideos`\n\n#### Parameters\n- `video_id` (video ID): Ad account library video ID (required)\n\n#### Return Type\n```json\n{\n  \"success\": \"bool\"\n}\n```\n\n## Error Codes\n\n### Create Operation Errors\n- **381**: Problem uploading video file\n- **100**: Invalid parameter\n- **222**: Video not visible\n- **389**: Unable to fetch video file from URL\n- **352**: Unsupported video format\n- **200**: Permissions error\n- **382**: Video file too small\n- **190**: Invalid OAuth 2.0 Access Token\n- **6001**: General video upload problem\n\n### Delete Operation Errors\n- **100**: Invalid parameter\n- **368**: Action deemed abusive or disallowed\n- **613**: Rate limit exceeded", "keyPoints": ["Ad Videos endpoint supports creating (uploading) and deleting video assets for ad accounts", "Supports both regular upload and chunked upload for large video files", "Includes specialized support for 360° videos and slideshow creation", "Reading and updating operations are not supported on this endpoint", "Comprehensive error handling with specific error codes for different failure scenarios"], "apiEndpoints": ["POST /act_{ad_account_id}/advideos", "DELETE /act_{ad_account_id}/advideos"], "parameters": ["name", "title", "description", "source", "file_url", "file_size", "upload_phase", "upload_session_id", "start_offset", "end_offset", "video_file_chunk", "slideshow_spec", "original_projection_type", "unpublished_content_type", "video_id"], "examples": ["Chunked upload workflow using upload_phase, upload_session_id, and video_file_chunk", "Slideshow creation with images_urls array and timing parameters", "360° video upload with projection type and field of view settings"], "tags": ["Facebook Marketing API", "Video Upload", "Ad Videos", "Chunked Upload", "360 Video", "Slideshow", "Video Management"], "relatedTopics": ["Video Format documentation", "Chunked upload process", "Ad Account management", "Video object reference", "OAuth 2.0 authentication", "Rate limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/", "processedAt": "2025-06-25T16:23:05.732Z", "processor": "openrouter-claude-sonnet-4"}