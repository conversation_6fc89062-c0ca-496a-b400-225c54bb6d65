{"title": "Facebook Marketing API - Ad Videos Reference", "summary": "Complete reference documentation for the Facebook Marketing API Ad Videos endpoint, covering video upload, management, and deletion operations for ad accounts. Includes detailed parameters, return types, and error codes for video operations.", "content": "# Ad Videos\n\nThe Ad Videos endpoint allows you to manage video assets within Facebook ad accounts. This endpoint supports creating (uploading) and deleting video content for use in advertising campaigns.\n\n## Operations Overview\n\n### Reading\nReading operations are not supported on this endpoint.\n\n### Creating (Video Upload)\nYou can upload videos by making a POST request to the `advideos` edge:\n- **Endpoint**: `/act_{ad_account_id}/advideos`\n- **Creates**: A Video object\n\n#### Key Parameters\n\n**Basic Video Properties:**\n- `name` (string): The name of the video in the library\n- `title` (UTF-8 string): Video name (must be < 255 characters, supports emoji)\n- `description` (UTF-8 string): Video description (supports emoji)\n- `source` (string): The video file encoded as form data\n- `file_url` (string): URL to video file\n- `file_size` (int64): Video file size in bytes (for chunked upload)\n\n**Chunked Upload Parameters:**\n- `upload_phase` (enum): Phase during chunked upload (start, transfer, finish, cancel)\n- `upload_session_id` (numeric string): Session ID for chunked upload\n- `start_offset` (int64): Start position in bytes of chunk being sent\n- `end_offset` (int64): End position in bytes of chunk\n- `video_file_chunk` (string): The actual video chunk data\n\n**360° Video Parameters:**\n- `fisheye_video_cropped` (boolean): Whether single fisheye video is cropped\n- `front_z_rotation` (float): Front z rotation in degrees\n- `original_fov` (int64): Original field of view of source camera\n- `original_projection_type` (enum): Original projection type (equirectangular, cubemap, half_equirectangular)\n\n**Slideshow Parameters:**\n- `slideshow_spec` (JSON object):\n  - `images_urls` (list<URL>): 3-7 element array of image URLs (required)\n  - `duration_ms` (integer): Duration of each image (default: 1000ms)\n  - `transition_ms` (integer): Crossfade transition duration (default: 1000ms)\n  - `reordering_opt_in` (boolean): Default false\n  - `music_variations_opt_in` (boolean): Default false\n\n**Content Type:**\n- `unpublished_content_type` (enum): SCHEDULED, SCHEDULED_RECURRING, DRAFT, ADS_POST, INLINE_CREATED, PUBLISHED, REVIEWABLE_BRANDED_CONTENT\n\n#### Return Type\nReturns a struct containing:\n- `id` (numeric string): Video ID\n- `upload_session_id` (numeric string): Upload session identifier\n- `video_id` (numeric string): Created video ID\n- `start_offset`, `end_offset` (numeric string): Byte offsets\n- `success` (bool): Upload success status\n- `skip_upload` (bool): Whether upload was skipped\n- `upload_domain` (string): Upload domain used\n- `region_hint` (string): Suggested region\n- Transcoding information: `transcode_bit_rate_bps`, `transcode_dimension`, etc.\n\n### Updating\nUpdate operations are not supported on this endpoint.\n\n### Deleting\nYou can remove a video from an ad account by making a DELETE request:\n- **Endpoint**: `/act_{ad_account_id}/advideos`\n- **Required Parameter**: `video_id` (video ID) - Ad account library video ID\n- **Returns**: `{\"success\": bool}`\n\n## Error Codes\n\n**Upload Errors:**\n- 381: Problem uploading video file\n- 382: Video file too small\n- 389: Unable to fetch video from URL\n- 352: Unsupported video format\n- 6001: General upload problem\n\n**General Errors:**\n- 100: Invalid parameter\n- 190: Invalid OAuth 2.0 Access Token\n- 200: Permissions error\n- 222: Video not visible\n- 368: Action deemed abusive or disallowed\n- 613: Rate limit exceeded", "keyPoints": ["Supports video upload via direct file or URL with chunked upload capability", "Includes specialized support for 360° videos and slideshow creation", "Reading and updating operations are not supported - only create and delete", "Comprehensive error handling for upload issues and permissions", "Returns detailed upload session information and transcoding parameters"], "apiEndpoints": ["POST /act_{ad_account_id}/advideos", "DELETE /act_{ad_account_id}/advideos"], "parameters": ["name", "title", "description", "source", "file_url", "file_size", "upload_phase", "upload_session_id", "start_offset", "end_offset", "video_file_chunk", "slideshow_spec", "fisheye_video_cropped", "original_projection_type", "unpublished_content_type", "video_id"], "examples": [], "tags": ["Facebook Marketing API", "Video Upload", "Ad Videos", "Chunked Upload", "360 Video", "Slideshow", "Video Management"], "relatedTopics": ["Video Format Documentation", "Chunked Upload Process", "Ad Account Management", "Graph API Video Reference", "OAuth 2.0 Access Tokens", "Rate Limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/", "processedAt": "2025-06-25T15:20:14.383Z", "processor": "openrouter-claude-sonnet-4"}