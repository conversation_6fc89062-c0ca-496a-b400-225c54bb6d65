{"title": "Facebook Marketing API Bidding", "summary": "Comprehensive guide to Facebook's ad auction system, covering bidding strategies, optimization goals, budgets, and delivery mechanisms. Explains how bids and budgets work with Facebook's ad auction for API-created ads, with the same functionality as Facebook's native tools.", "content": "# Facebook Marketing API Bidding\n\nLearn how your bids and budget work with Facebook's ad auction and delivery. This covers bidding options, placing bids for desired action, setting budget limits and tracking ads delivery. Facebook's auction functions the same way for API-created ads as they do for ads from Facebook tools.\n\n## Main Concepts\n\n### Bid Strategies\nProvide your bid preferences to control how much you're willing to pay for your advertising objectives.\n\n### Optimization Goals\nDefine advertising goals you want to achieve when Facebook delivers your ads, allowing the system to optimize delivery accordingly.\n\n### Budgets\nSet spending limits and control how much you invest in your advertising campaigns.\n\n### Pacing and Scheduling\nDetermine how your ads budget is spent over time, controlling the rate and timing of ad delivery.\n\n### Billing Events\nDefines events you want to pay for, including impressions, clicks, or various conversion actions.\n\n## Common Use Cases\n\n### Campaign Budget Optimization\nOptimize the distribution of a campaign budget across your campaign's ad sets for maximum efficiency.\n\n### Optimized Cost Per Mille Ads\nPrioritize your marketing goals and automatically deliver ads towards these goals in the most effective way possible.\n\n### Cost Per Action Ads\nSpecify conversion events and get charged by the amount of conversions rather than impressions or clicks.\n\n### Reach and Frequency\nBid on a predicted unique audience reach for your ads on Facebook and Instagram and control display frequency.\n\n### Bid Multipliers\nMaintain a nuanced bidding strategy within a single ad set with one targeted audience. **Note: Available on a limited basis.**\n\n## Documentation Structure\n\n### Overview\nCore concepts and usage requirements covering Budgets, Optimization Goals, and Bid Strategies.\n\n### Guides\nUse case based guides to help you perform specific bidding and optimization actions.\n\n### Support\nAccess to FAQs, API updates, helpful links, reference pages, and Ads Help Center resources.", "keyPoints": ["Facebook's auction system works identically for API-created ads and native Facebook tool ads", "Five main concepts: Bid Strategies, Optimization Goals, Budgets, Pacing/Scheduling, and Billing Events", "Campaign Budget Optimization allows automatic budget distribution across ad sets", "Multiple bidding models available including CPM, CPA, and reach/frequency", "Bid Multipliers provide advanced bidding control but are available on limited basis"], "apiEndpoints": [], "parameters": ["bid_strategy", "optimization_goal", "budget", "billing_event", "bid_amount", "campaign_budget_optimization", "pacing_type", "bid_multiplier"], "examples": [], "tags": ["bidding", "auction", "budget", "optimization", "campaign-management", "cost-control", "delivery", "facebook-ads"], "relatedTopics": ["Bid Strategies", "Optimization Goals", "Budgets", "Pacing and Scheduling", "Billing Events", "Campaign Budget Optimization", "Optimized Cost Per <PERSON>", "Cost Per Action Ads", "Reach and Frequency", "Bid Multipliers", "Ads Help Center"], "difficulty": "intermediate", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-api/bidding", "processedAt": "2025-06-25T15:06:29.820Z", "processor": "openrouter-claude-sonnet-4"}