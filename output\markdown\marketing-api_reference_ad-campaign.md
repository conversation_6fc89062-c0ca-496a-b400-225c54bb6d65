# Ad Set

Graph API Version

[v23.0](#)

# Ad Set

An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data. Ad sets enable you to group ads according to your criteria, and you can retrieve the ad-related statistics that apply to a set. See [Optimized CPM](/docs/marketing-api/optimizedcpm) and [Promoted Object](/docs/marketing-api/reference/ad-campaign/promoted-object).

For example, create an ad set with a daily budget:

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X POST \
  -F 'name="My Reach Ad Set"' \
  -F 'optimization_goal="REACH"' \
  -F 'billing_event="IMPRESSIONS"' \
  -F 'bid_amount=2' \
  -F 'daily_budget=1000' \
  -F 'campaign_id="<AD_CAMPAIGN_ID>"' \
  -F 'targeting={
       "geo_locations": {
         "countries": [
           "US"
         ]
       },
       "facebook_positions": [
         "feed"
       ]
     }' \
  -F 'status="PAUSED"' \
  -F 'promoted_object={
       "page_id": "<PAGE_ID>"
     }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets?&version=v23.0&name=My+Reach+Ad+Set&optimization_goal=REACH&billing_event=IMPRESSIONS&bid_amount=2&daily_budget=1000&campaign_id=%3CAD_CAMPAIGN_ID%3E&targeting=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22facebook_positions%22%3A%5B%22feed%22%5D%7D&status=PAUSED&promoted_object=%7B%22page_id%22%3A%22%3CPAGE_ID%3E%22%7D)[Open In Postman](https://l.facebook.com/l.php?u=https%3A%2F%2Fwww.postman.com%2Fmeta%2Ffacebook-marketing-api%2Ffolder%2F8123rwa%2Fadsets&h=AT1u0v1JXIVKvLSb1_hgpI3ZJfUrohtXAkG6ArpJiht0FLRDtBSbVh8fifgYodJacRXB77hOZR0sNDnEedsWGiXUS14tUZ0zXSWMexduy5Ctf5TxRTH95g52Y-ipI0dVuzajFcN-PL_GrRUFTfO97Q)

Give Feedback

Create an ad set with a lifetime budget

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X POST \
  -F 'name="My First Adset"' \
  -F 'lifetime_budget=20000' \
  -F 'start_time="2025-06-25T08:51:33-0700"' \
  -F 'end_time="2025-07-05T08:51:33-0700"' \
  -F 'campaign_id="<AD_CAMPAIGN_ID>"' \
  -F 'bid_amount=100' \
  -F 'billing_event="LINK_CLICKS"' \
  -F 'optimization_goal="LINK_CLICKS"' \
  -F 'targeting={
       "facebook_positions": [
         "feed"
       ],
       "geo_locations": {
         "countries": [
           "US"
         ]
       },
       "publisher_platforms": [
         "facebook",
         "audience_network"
       ]
     }' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets?&version=v23.0&name=My+First+Adset&lifetime_budget=20000&start_time=2025-06-25T08%3A51%3A33-0700&end_time=2025-07-05T08%3A51%3A33-0700&campaign_id=%3CAD_CAMPAIGN_ID%3E&bid_amount=100&billing_event=LINK_CLICKS&optimization_goal=LINK_CLICKS&targeting=%7B%22facebook_positions%22%3A%5B%22feed%22%5D%2C%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22publisher_platforms%22%3A%5B%22facebook%22%2C%22audience_network%22%5D%7D&status=PAUSED)

Give Feedback

### Limits

The following are the limits on ad sets

Limit

Value

Maximum number of ad sets per regular ad account

5000 non-deleted ad sets

Maximum number of ad sets per bulk ad account

10000 non-deleted ad sets

Maximum number of ads per ad set

50 non-archived ads

### Housing, Employment and Credit Ads

Facebook is committed to protecting people from discrimination, and we are continually improving our ability to detect and deter potential abuse. It’s already against [our policies](https://www.facebook.com/policies/ads/prohibited_content/discriminatory_practices) to discriminate by wrongfully targeting or excluding specific groups of people. As part of a [historic settlement agreement](https://l.facebook.com/l.php?u=https%3A%2F%2Fnewsroom.fb.com%2Fnews%2F2019%2F03%2Fprotecting-against-discrimination-in-ads%2F&h=AT1F5eXYq9Y6N7zoiY2_aefjRxpzTHd0BBssqCaO0nCY9jSxqI1FEngsk-uNTo40BM_9-7m6VZRwaBhYmis9DcqdWZ7CcsN64OWVHLZN3XgPr6vlgeUKYQzB4C6R_EKX-oEZmR6BNWnZD2WIh8N4kw), we are making changes to the way we manage housing, employment and credit ads.

Advertisers must specify a `special_ad_category` for ad campaigns that market housing, employment, and credit. In doing so, the set of targeting options available for ads in these campaigns will be restricted. See [Special Ad Category](/docs/marketing-api/special-ad-category) for more information.

### Targeting European Union Ads

Beginning Tuesday, May 16, 2023 advertisers who include the European Union (EU), associated territories, or select global/worldwide in their ad targeting on Facebook and Instagram will be asked to include information about who benefits from the ad (the beneficiary) and who is paying for the ad (the payor) for each ad set. Advertisers will be prompted for this information in all ads buying surfaces including Ads Manager and the Marketing API. Beginning Wednesday, August 16, 2023, if beneficiary and payer information is not provided, the ad will not be published.

We are launching this requirement to respond to the EU Digital Services Act (DSA) which goes into full effect for Facebook and Instagram later this year.

Ad sets targeted to the EU and/or associated territories (see [here](https://www.facebook.com/business/help/***************/) for a complete list) are required to provide beneficiary information (who benefits from the ad running), and payer information (who pays for the ad). This applies to new ads, duplicated ads, or significantly edited ads from May 16 forward, and without the required information, the API will respond with a wrong parameter error. For convenience the advertiser can set a saved beneficiary and payor in their ad account, which will be auto-populated during ad set creation, copying, and updating targets to include EU locations and ads under existing ad seta without configured the payor and beneficiary.. For more information about the ad account level parameters, `default_dsa_payor` and `default_dsa_beneficiary`, see to the check the [Ad Account reference document](/docs/marketing-api/reference/ad-account).

To facilitate the creation of ad sets targeting the EU, we're offering a new API which allows developers to get a list of likely beneficiary/payer strings, based on ad account activity. See [Ad Account DSA Recommendations](/docs/marketing-api/reference/ad-account/dsa_recommendations) for more information.

**Notice:**

*   When the default values are set in the ad account, during ad set creation, updating, and ad creation under an existing ad set, if one of them is not provided, the API will automatically fill the default value listed in the ad account. **Do not pass only one of them and expect the API to set the other one to be the same value.** For example, in the ad account settings, `default_dsa_payor` is `payor_default` and `default_dsa_beneficiary` is `beneficiary_default`. During ad set creation, if only `dsa_payor` is passed with the payor, the `dsa_beneficiary` will be automatically filled with value of `beneficiary_default` instead of `dsa_payor`.
*   If no saved default values are set or the values are unset, without explicitly passing the payor or beneficiary during ad set creation or when making updates, it will trigger an error and the request will fail.
*   The `payer` and the `beneficiary` fields are only for ad sets targeting the EU and/or associated territories.
*   For ad sets targeting regions other than the EU and/or associated territories, that information will not be saved even if it is provided.

To facilitate the creation of ad sets targeting the EU, we're offering a new API which allows developers to get a list of likely beneficiary/payer strings, based on ad account activity. See [Ad Account Dsa Recommendations](/docs/marketing-api/reference/ad-account/dsa_recommendations) for more information.

## Reading

An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data. Ad sets enable you to group ads according to your criteria, and you can retrieve the ad-related statistics that apply to a set.

The `date_preset = lifetime` parameter is disabled in Graph API v10.0 and replaced with `date_preset = maximum`, which returns a maximum of 37 months of data. For v9.0 and below, `date_preset = maximum` will be enabled on May 25, 2021, and any `lifetime` calls will default to `maximum` and return only 37 months of data.

### Examples

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X GET \
  -d 'fields="name,status"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=%3CAD_SET_ID%3E%2F?fields=name%2Cstatus&version=v23.0)

Give Feedback

To retrieve date-time related fields in a UNIX timestamp format, use the `date_format` parameter:

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X GET \
  -d 'fields="id,name,start_time,end_time"' \
  -d 'date_format="U"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=%3CAD_SET_ID%3E%2F?fields=id%2Cname%2Cstart_time%2Cend_time%26date_format=U&version=v23.0)

Give Feedback

To retrieve information for multiple ad sets:

PHP Business SDKcURL

```
`use FacebookAds\Object\AdSet;
use FacebookAds\Object\Fields\AdSetFields;

$ad_set_ids = array(<AD_SET_1_ID>, <AD_SET_2_ID>, <AD_SET_3_ID>);
$fields = array(
  AdSetFields::NAME,
  AdSetFields::CONFIGURED_STATUS,
  AdSetFields::EFFECTIVE_STATUS,
);
$adsets = AdSet::readIds($ad_set_ids, $fields);

foreach ($adsets as $adset) {
  echo $adset->{AdSetFields::NAME}.PHP_EOL;
}`
```

To read all ad sets from one ad account:

PHP Business SDKPython Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdAccount;
use FacebookAds\Object\Fields\AdSetFields;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$adsets = $account->getAdSets(array(
  AdSetFields::NAME,
  AdSetFields::CONFIGURED_STATUS,
  AdSetFields::EFFECTIVE_STATUS,
));

foreach ($adsets as $adset) {
  echo $adset->{AdSetFields::NAME}.PHP_EOL;
}`
```

To read the names of ad sets with status paused in an ad account

PHP Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdAccount;
use FacebookAds\Object\AdSet;
use FacebookAds\Object\Fields\AdSetFields;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$adsets = $account->getAdSets(
  array(),
  array(
    AdSetFields::EFFECTIVE_STATUS => array(
      AdSet::STATUS_PAUSED,
    ),
  ));

foreach ($adsets as $adset) {
  echo $adset->{AdSetFields::NAME}.PHP_EOL;
}`
```

To read the `end_time` of multiple ad sets.

PHP Business SDKcURL

```
`use FacebookAds\Object\AdSet;
use FacebookAds\Object\Fields\AdSetFields;

$adsets = AdSet::readIds(
  array($ad_set_1_id, $ad_set_2_id),
  array(AdSetFields::END_TIME));

foreach ($adsets as $adset) {
  echo $adset->{AdSetFields::END_TIME}.PHP_EOL;
}`
```

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=GET&path=%3CAD_SET_ID%3E%2F%3Ffields%3Dadset_schedule&version=v23.0)

```
`GET /v23.0/<AD_SET_ID>/?fields=adset_schedule HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`date_preset`

enum{today, yesterday, this\_month, last\_month, this\_quarter, maximum, data\_maximum, last\_3d, last\_7d, last\_14d, last\_28d, last\_30d, last\_90d, last\_week\_mon\_sun, last\_week\_sun\_sat, last\_quarter, last\_year, this\_week\_mon\_today, this\_week\_sun\_today, this\_year}

Date Preset

`time_range`

{'since':YYYY-MM-DD,'until':YYYY-MM-DD}

Time Range. Note if time range is invalid, it will be ignored.

### Fields

Field

Description

`id`

numeric string

ID for the Ad Set

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`account_id`

numeric string

ID for the Ad Account associated with this Ad Set

`adlabels`

[list<AdLabel>](https://developers.facebook.com/docs/marketing-api/reference/ad-label/)

Ad Labels associated with this ad set

`adset_schedule`

list<DayPart>

Ad set schedule, representing a delivery schedule for a single day

`asset_feed_id`

numeric string

The ID of the asset feed that constains a content to create ads

`attribution_spec`

list<AttributionSpec>

Conversion attribution spec used for attributing conversions for optimization. Supported window lengths differ by optimization goal and campaign objective. See [Objective, Optimization Goal and `attribution_spec`](/docs/marketing-api/reference/ad-campaign-group#attribution_spec).

`bid_adjustments`

[AdBidAdjustments](https://developers.facebook.com/docs/marketing-api/reference/ad-bid-adjustments/)

Map of bid adjustment types to values

`bid_amount`

unsigned int32

Bid cap or target cost for this ad set. The bid cap used in a _lowest cost bid strategy_ is defined as the maximum bid you want to pay for a result based on your `optimization_goal`. The target cost used in a _target cost bid strategy_ lets Facebook bid on your behalf to meet your target on average and keep costs stable as you raise budget.

The bid amount's unit is cents for currencies like USD, EUR, and the basic unit for currencies like JPY, KRW. The bid amount for ads with `IMPRESSION` or `REACH` as `billing_event` is per 1,000 occurrences of that event, and the bid amount for ads with other `billing_event`s is for each occurrence.

`bid_constraints`

[AdCampaignBidConstraint](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-bid-constraint/)

Choose bid constraints for ad set to suit your specific business goals. It usually works together with `bid_strategy` field.

`bid_info`

map<string, unsigned int32>

Map of bid objective to bid value.

`bid_strategy`[](#)

enum {LOWEST\_COST\_WITHOUT\_CAP, LOWEST\_COST\_WITH\_BID\_CAP, COST\_CAP, LOWEST\_COST\_WITH\_MIN\_ROAS}

Bid strategy for this ad set when you use `AUCTION` as your buying type:  
`LOWEST_COST_WITHOUT_CAP`: Designed to get the most results for your budget based on your ad set `optimization_goal` without limiting your bid amount. This is the best strategy if you care most about cost efficiency. However with this strategy it may be harder to get stable average costs as you spend. This strategy is also known as _automatic bidding_. Learn more in [Ads Help Center, About bid strategies: Lowest cost](https://www.facebook.com/business/help/721453268045071).  
`LOWEST_COST_WITH_BID_CAP`: Designed to get the most results for your budget based on your ad set `optimization_goal` while limiting actual bid to your specified amount. With a bid cap you have more control over your cost per actual optimization event. However if you set a limit which is too low you may get less ads delivery. Get your bid cap with the field `bid_amount`. This strategy is also known as _manual maximum-cost bidding_. Learn more in [Ads Help Center, About bid strategies: Lowest cost](https://www.facebook.com/business/help/721453268045071).  
Notes:

*   If you enable campaign budget optimization, you should get `bid_strategy` at the parent campaign level.
*   `TARGET_COST` bidding strategy has been deprecated with [Marketing API v9](/docs/graph-api/changelog/version9.0).

`billing_event`

enum {APP\_INSTALLS, CLICKS, IMPRESSIONS, LINK\_CLICKS, NONE, OFFER\_CLAIMS, PAGE\_LIKES, POST\_ENGAGEMENT, THRUPLAY, PURCHASE, LISTING\_INTERACTION}

The billing event for this ad set:  
`APP_INSTALLS`: Pay when people install your app.  
`CLICKS`: Pay when people click anywhere in the ad.  
`IMPRESSIONS`: Pay when the ads are shown to people.  
`LINK_CLICKS`: Pay when people click on the link of the ad.  
`OFFER_CLAIMS`: Pay when people claim the offer.  
`PAGE_LIKES`: Pay when people like your page.  
`POST_ENGAGEMENT`: Pay when people engage with your post.  
`VIDEO_VIEWS`: Pay when people watch your video ads for at least 10 seconds.  
`THRUPLAY`: Pay for ads that are played to completion, or played for at least 15 seconds.

`brand_safety_config`

BrandSafetyCampaignConfig

brand\_safety\_config

`budget_remaining`

numeric string

Remaining budget of this Ad Set

`campaign`

[Campaign](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/)

The campaign that contains this ad set

`campaign_active_time`

numeric string

Campaign running length

`campaign_attribution`

enum

campaign\_attribution, a new field for app ads campaign, used to indicate a campaign's attribution type, eg: SKAN or AEM

`campaign_id`

numeric string

The ID of the campaign that contains this ad set

`configured_status`

enum {ACTIVE, PAUSED, DELETED, ARCHIVED}

The status set at the ad set level. It can be different from the effective status due to its parent campaign. Prefer using 'status' instead of this.

`contextual_bundling_spec`

ContextualBundlingSpec

specs of contextual bundling Ad Set setup, including signal of opt-in/out the feature

`created_time`

datetime

Time when this Ad Set was created

`creative_sequence`

list<numeric string>

Order of the adgroup sequence to be shown to users

`daily_budget`

numeric string

The daily budget of the set defined in your [account currency](/docs/marketing-api/adset/budget-limits).

`daily_min_spend_target`

numeric string

Daily minimum spend target of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. This target is not a guarantee but our best effort.

`daily_spend_cap`

numeric string

Daily spend cap of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign.

`destination_type`

string

Destination of ads in this Ad Set.

Options include: `WEBSITE`, `APP`, `MESSENGER`, `INSTAGRAM_DIRECT`.

The `ON_AD`, `ON_POST`, `ON_VIDEO`, `ON_PAGE`, and `ON_EVENT` destination types are currently in limited beta testing. Trying to duplicate campaigns with existing destination types using these new destination types may throw an error. See the [Outcome-Driven Ads Experiences](#odax) section below for more information.

`dsa_beneficiary`

string

The beneficiary of all ads in this ad set.

`dsa_payor`

string

The payor of all ads in this ad set.

`effective_status`

enum {ACTIVE, PAUSED, DELETED, CAMPAIGN\_PAUSED, ARCHIVED, IN\_PROCESS, WITH\_ISSUES}

The effective status of the adset. The status could be effective either because of its own status, or the status of its parent campaign. `WITH_ISSUES` is available for version 3.2 or higher. `IN_PROCESS` is available for version 4.0 or higher.

`end_time`

datetime

End time, in UTC UNIX timestamp

`frequency_control_specs`

[list<AdCampaignFrequencyControlSpecs>](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-frequency-control-specs/)

An array of frequency control specs for this ad set. As there is only one event type currently supported, this array has no more than one element. Writes to this field are only available in ad sets where `REACH` is the objective.

`instagram_user_id`[](#)

numeric string

Represents your Instagram account id, used for ads, including dynamic creative ads on Instagram.

`is_dynamic_creative`[](#)

bool

Whether this ad set is a dynamic creative ad set. dynamic creative ad can be created only under ad set with this field set to be true.

`is_incremental_attribution_enabled`

bool

Whether the campaign should use incremental attribution optimization.

`issues_info`[](#)

[list<AdCampaignIssuesInfo>](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-issues-info/)

Issues for this ad set that prevented it from deliverying

`learning_stage_info`

[AdCampaignLearningStageInfo](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-learning-stage-info/)

Info about whether the ranking or delivery system is still learning for this ad set. While the ad set is still in learning , we might unstablized delivery performances.

`lifetime_budget`

numeric string

The lifetime budget of the set defined in your [account currency](/docs/marketing-api/adset/budget-limits).

`lifetime_imps`

int32

Lifetime impressions. Available only for campaigns with `buying_type=FIXED_CPM`

`lifetime_min_spend_target`

numeric string

Lifetime minimum spend target of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. This target is not a guarantee but our best effort.

`lifetime_spend_cap`

numeric string

Lifetime spend cap of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign.

`min_budget_spend_percentage`

numeric string

min\_budget\_spend\_percentage

`multi_optimization_goal_weight`

string

multi\_optimization\_goal\_weight

`name`

string

Name of the ad set

`optimization_goal`

enum {NONE, APP\_INSTALLS, AD\_RECALL\_LIFT, ENGAGED\_USERS, EVENT\_RESPONSES, IMPRESSIONS, LEAD\_GENERATION, QUALITY\_LEAD, LINK\_CLICKS, OFFSITE\_CONVERSIONS, PAGE\_LIKES, POST\_ENGAGEMENT, QUALITY\_CALL, REACH, LANDING\_PAGE\_VIEWS, VISIT\_INSTAGRAM\_PROFILE, VALUE, THRUPLAY, DERIVED\_EVENTS, APP\_INSTALLS\_AND\_OFFSITE\_CONVERSIONS, CONVERSATIONS, IN\_APP\_VALUE, MESSAGING\_PURCHASE\_CONVERSION, SUBSCRIBERS, REMINDERS\_SET, MEANINGFUL\_CALL\_ATTEMPT, PROFILE\_VISIT, PROFILE\_AND\_PAGE\_ENGAGEMENT, ADVERTISER\_SILOED\_VALUE, AUTOMATIC\_OBJECTIVE, MESSAGING\_APPOINTMENT\_CONVERSION}

The optimization goal this ad set is using.  
`NONE`: Only available in read mode for campaigns created pre-v2.4.  
`APP_INSTALLS`: Optimize for people more likely to install your app.  
`AD_RECALL_LIFT`: Optimize for people more likely to remember seeing your ads.  
`CLICKS`: Deprecated. Only available in read mode.  
`ENGAGED_USERS`: Optimize for people more likely to take a particular action in your app.  
`EVENT_RESPONSES`: Optimize for people more likely to attend your event.  
`IMPRESSIONS`: Show the ads as many times as possible.  
`LEAD_GENERATION`: Optimize for people more likely to fill out a lead generation form.  
`QUALITY_LEAD`: Optimize for people who are likely to have a deeper conversation with advertisers after lead submission.  
`LINK_CLICKS`: Optimize for people more likely to click in the link of the ad.  
`OFFSITE_CONVERSIONS`: Optimize for people more likely to make a conversion on the site.  
`PAGE_LIKES`: Optimize for people more likely to like your page.  
`POST_ENGAGEMENT`: Optimize for people more likely to engage with your post.  
`QUALITY_CALL`: Optimize for people who are likely to call the advertiser.  
`REACH`: Optimize to reach the most unique users for each day or interval specified in `frequency_control_specs`.  
`LANDING_PAGE_VIEWS`: Optimize for people who are most likely to click on and load your chosen landing page.  
`VISIT_INSTAGRAM_PROFILE`: Optimize for visits to the advertiser's Instagram profile.  
`VALUE`: Optimize for maximum total purchase value within the specified attribution window.  
`THRUPLAY`: Optimize delivery of your ads to people who are more likely to play your ad to completion, or play it for at least 15 seconds.  
`DERIVED_EVENTS`: Optimize for retention, which reaches people who are most likely to return to the app and open it again during a given time frame after installing. You can choose either two days, meaning the app is likely to be reopened between 24 and 48 hours after installation; or seven days, meaning the app is likely to be reopened between 144 and 168 hours after installation.  
`APP_INSTALLS_AND_OFFSITE_CONVERSIONS`: Optimizes for people more likely to install your app and make a conversion on your site.  
`CONVERSATIONS`: Directs ads to people more likely to have a conversation with the business.

`optimization_sub_event`

string

Optimization sub event for a specific optimization goal. For example: Sound-On event for Video-View-2s optimization goal.

`pacing_type`

list<string>

Defines the pacing type, standard or using ad scheduling

`promoted_object`

[AdPromotedObject](https://developers.facebook.com/docs/marketing-api/reference/ad-promoted-object/)

The object this ad set is promoting across all its ads.

`recommendations`

list<AdRecommendation>

If there are recommendations for this ad set, this field includes them. Otherwise, will not be included in the response. This field is not included in redownload mode.

`recurring_budget_semantics`

bool

If this field is `true`, your daily spend may be more than your daily budget while your weekly spend will not exceed 7 times your daily budget. More details explained in the [Ad Set Budget](/docs/marketing-api/adset/budget-limits) document. If this is `false`, your amount spent daily will not exceed the daily budget. This field is not applicable for lifetime budgets.

`regional_regulated_categories`

list<enum>

This param is used to specify `regional_regulated_categories`. Currently it supports `null` and the following values:

1.  TAIWAN\_FINSERV: Use this value to declare a Financial Service Ad Set if the ad targets Taiwan Audience
2.  AUSTRALIA\_FINSERV: Use this value to declare a Financial Service Ad Set if the ad set targets Australia Audience
3.  TAIWAN\_UNIVERSAL: Use this value to declare an Ad Set if it targets Taiwan Audience
4.  SINGAPORE\_UNIVERSAL: Use this value to declare an Ad Set if it targets Singapore Audience

If an ad set is a Financial Service Ad and it targets Taiwan, it needs to declare both `TAIWAN_FINSERV` and `TAIWAN_UNIVERSAL`

Example: `null` or `[AUSTRALIA_FINSERV]` or `[TAIWAN_FINSERV, TAIWAN_UNIVERSAL]`

`regional_regulation_identities`

RegionalRegulationIdentities

This param is used to specify regional\_regulation\_identities used to represent the ad set. Currently it supports the following fields:

1.  taiwan\_finserv\_beneficiary: used for TAIWAN\_FINSERV category
2.  taiwan\_finserv\_payer: used for TAIWAN\_FINSERV category
3.  australia\_finserv\_beneficiary: used for AUSTRALIA\_FINSERV category
4.  australia\_finserv\_payer: used for AUSTRALIA\_FINSERV category
5.  taiwan\_universal\_beneficiary: used for TAIWAN\_UNIVERSAL category
6.  taiwan\_universal\_payer: used for TAIWAN\_UNIVERSAL category
7.  singapore\_universal\_beneficiary: used for SINGAPORE\_UNIVERSAL category
8.  singapore\_universal\_payer: used for SINGAPORE\_UNIVERSAL category

Example:

`regional_regulation_identities: { "taiwan_finserv_beneficiary": <verified_identity_id>, "taiwan_finserv_payer": <verified_identity_id>, "taiwan_universal_beneficiary": <verified_identity_id>, "taiwan_universal_payer": <verified_identity_id>, }`

During creation and update, the passed identities fields need to correspond to declared categories.

To update an existing ad set identities, you need to pass new values for both categories and identities to overwrite the identity id or `null` to remove existing id.

For example:

Upon creation, `regional_regulated_categories` is `[TAIWAN_FINSERV, TAIWAN_UNIVERSAL]` and `regional_regulation_identities` is

`regional_regulation_identities: { "taiwan_finserv_beneficiary": <id_123>, "taiwan_finserv_payer": <id_123>, "taiwan_universal_beneficiary": <id_456>, "taiwan_universal_payer": <id_456>, }`

For update, passing `[TAIWAN_UNIVERSAL]` and `regional_regulation_identities: { "taiwan_finserv_beneficiary": null "taiwan_finserv_payer": null, "taiwan_universal_beneficiary": <id_789>, "taiwan_universal_payer": <id_789>, }`

will remove `TAIWAN_FINSERV` declaration and update the identities ID of `TAIWAN_UNIVERSAL`

`review_feedback`

string

Reviews for dynamic creative ad

`rf_prediction_id`

id

Reach and frequency prediction ID

`source_adset`

[AdSet](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/)

The source ad set that this ad set was copied from

`source_adset_id`

numeric string

The source ad set id that this ad set was copied from

`start_time`

datetime

Start time, in UTC UNIX timestamp

`status`

enum {ACTIVE, PAUSED, DELETED, ARCHIVED}

The status set at the ad set level. It can be different from the effective status due to its parent campaign. The field returns the same value as `configured_status`, and is the suggested one to use.

`targeting`

Targeting

Targeting

`targeting_optimization_types`[](#)

list<KeyValue:string,int32>

Targeting options that are relaxed and used as a signal for optimization

`time_based_ad_rotation_id_blocks`

list<list<integer>>

Specify ad creative that displays at custom date ranges in a campaign as an array. A list of Adgroup IDs. The list of ads to display for each time range in a given schedule. For example display first ad in Adgroup for first date range, second ad for second date range, and so on. You can display more than one ad per date range by providing more than one ad ID per array. For example set `time_based_ad_rotation_id_blocks` to \[\[1\], \[2, 3\], \[1, 4\]\]. On the first date range show ad 1, on the second date range show ad 2 and ad 3 and on the last date range show ad 1 and ad 4. Use with `time_based_ad_rotation_intervals` to specify date ranges.

`time_based_ad_rotation_intervals`

list<unsigned int32>

Date range when specific ad creative displays during a campaign. Provide date ranges in an array of UNIX timestamps where each timestamp represents the start time for each date range. For example a 3-day campaign from May 9 12am to May 11 11:59PM PST can have three date ranges, the first date range starts from May 9 12:00AM to May 9 11:59PM, second date range starts from May 10 12:00AM to May 10 11:59PM and last starts from May 11 12:00AM to May 11 11:59PM. The first timestamp should match the campaign start time. The last timestamp should be at least 1 hour before the campaign end time. You must provide at least two date ranges. All date ranges must cover the whole campaign length, so any date range cannot exceed campaign length. Use with `time_based_ad_rotation_id_blocks` to specify ad creative for each date range.

`updated_time`

datetime

Time when the Ad Set was updated

`use_new_app_click`

bool

If set, allows Mobile App Engagement ads to optimize for LINK\_CLICKS

`value_rule_set_id`

numeric string

value\_rule\_set\_id

### Edges

Edge

Description

[`activities`](/docs/marketing-api/reference/ad-campaign/activities/)

Edge<AdActivity>

The activities of this ad set

[`ad_studies`](/docs/marketing-api/reference/ad-campaign/ad_studies/)

Edge<AdStudy>

The ad studies containing this ad set

[`adcreatives`](/docs/marketing-api/reference/ad-campaign/adcreatives/)

Edge<AdCreative>

The creatives of this ad set

[`adrules_governed`](/docs/marketing-api/reference/ad-campaign/adrules_governed/)

Edge<AdRule>

Ad rules that govern this ad set - by default, this only returns rules that either directly mention the ad set by id or indirectly through the set `entity_type`

[`ads`](/docs/marketing-api/reference/ad-campaign/ads/)

Edge<Adgroup>

The ads under this ad set

[`asyncadrequests`](/docs/marketing-api/reference/ad-campaign/asyncadrequests/)

Edge<AdAsyncRequest>

Async ad requests for this ad set

[`copies`](/docs/marketing-api/reference/ad-campaign/copies/)

Edge<AdCampaign>

The copies of this ad set

[`delivery_estimate`](/docs/marketing-api/reference/ad-campaign/delivery_estimate/)

Edge<AdCampaignDeliveryEstimate>

The delivery estimate for this ad set

[`message_delivery_estimate`](/docs/marketing-api/reference/ad-campaign/message_delivery_estimate/)

Edge<MessageDeliveryEstimate>

Delivery estimation of the marketing message campaign

[`targetingsentencelines`](/docs/marketing-api/reference/ad-campaign/targetingsentencelines/)

Edge<TargetingSentenceLine>

The targeting description sentence for this ad set

### Error Codes

Error

Description

100

Invalid parameter

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

190

Invalid OAuth 2.0 Access Token

200

Permissions error

2635

You are calling a deprecated version of the Ads API. Please update to the latest version.

2500

Error parsing graph query

The `date_preset = lifetime` parameter is disabled in Graph API v10.0 and replaced with `date_preset = maximum`, which returns a maximum of 37 months of data. For v9.0 and below, `date_preset = maximum` will be enabled on May 25, 2021, and any `lifetime` calls will default to `maximum` and return only 37 months of data.

## Creating

For v20.0+, the Impressions optimization goal is deprecated for the legacy Post Engagement objective and the `ON_POST` destination\_type.

### Examples

Validate an ad set with a daily budget where the campaign objective is set to `APP_INSTALLS`.

```
curl -X POST \\
  -F 'name="Mobile App Installs Ad Set"' \\
  -F 'daily\_budget=1000' \\
  -F 'bid\_amount=2' \\
  -F 'billing\_event="IMPRESSIONS"' \\
  -F 'optimization\_goal="APP\_INSTALLS"' \\
  -F 'campaign\_id="<AD\_CAMPAIGN\_ID>"' \\
  -F 'promoted\_object={
       "application\_id": "<APP\_ID>",
       "object\_store\_url": "<APP\_STORE\_URL>"
     }' \\
  -F 'targeting={
       "device\_platforms": \[
         "mobile"
       \],
       "facebook\_positions": \[
         "feed"
       \],
       "geo\_locations": {
         "countries": \[
           "US"
         \]
       },
       "publisher\_platforms": \[
         "facebook",
         "audience\_network"
       \],
       "user\_os": \[
         "IOS"
       \]
     }' \\
  -F 'status="PAUSED"' \\
  -F 'access\_token=<ACCESS\_TOKEN>' \\
https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/adsets
```

### Considerations

#### Bid/Budget Validations

**Note:**

*   All values in this section are in US Dollars.
    
*   Differenct currencies have different minimum daily budget limits.
    
*   Minimum values are defined in terms of the daily budget but apply to lifetime budgets as well.
    

When creating an ad set, there will be a minimum budget for different billing events (Clicks, Impressions, Actions). If the minimum daily budget is $5, a campaign lasting 5 days will need at least $25 for budget.

Budget amounts shown are for illustrative purposes only and can change based on situation.

If `bid_strategy` is set to `LOWEST_COST_WITHOUT_CAP` in the ad set:

Billing Event

Minimum Daily Budget

Impressions

$0.50

Clicks/Likes/Video Views

$2.50

Low-frequency Actions  
(Includes mobile app installs, offer claims, or canvas app installs)

$40  
**Important:** This minimum daily budget is the same for all countries.

If `bid_strategy` is set to `LOWEST_COST_WITH_BID_CAP` in the ad set:

Billing Event

Minimum Daily Budget

Impressions

At least the `bid_amount`. For example, if the bid amount is $10, then $10 will be the minimum budget required.

Clicks/Actions

5x the `bid_amount` for a Click or Action. For example, if the bid amount is $5 per click/action, then $25 will be the minimum budget required.

Budgets in non-USD currencies will be converted and validated upon time of ad set creation.

For ads belonging to ad accounts from countries in the list below, the minimum values are 2x the ones in the tables. For example, if the billing event is an Impression, the minimum daily budget is $0.50, but in the the following countries the minimum would be $1.00:

Australia, Austria, Belgium, Canada, Denmark, Finland, France, Germany, Greece, Hong Kong, Israel, Italy, Japan, Netherlands, New Zealand, Norway, Singapore, South Korea, Spain, Sweden, Switzerland, Taiwan, United Kingdom, United States of America.

The only exception to this rule are Low-Frequency Actions when `bid_strategy` is `LOWEST_COST_WITHOUT_CAP`.

#### Locale targeted page post

If you promote a Page post which has been targeted by locale the ad set targeting must include the same, or a subset of, locale targeting as the Page post.

E.g. if the Page post is targeted at locales 6 (US English) and 24 (UK English), then the ad set must be targeted at one or more of the same locales.

#### Mobile App Ads

Mobile app ad sets should

*   be used in conjunction with [targeting spec](/docs/reference/ads-api/targeting-specs#mobile) fields `user_device` and `user_os`
    
*   have a `MOBILE_APP_*` objective on the [campaign](/docs/marketing-api/adcampaign)
    

#### Desktop App Ads

Desktop app ad sets must

*   include a [targeting spec](/docs/reference/ads-api/targeting-specs) of either
    
    *   `'page_types':['desktopfeed']` or
        
    *   `'page_types':['rightcolumn']` or
        
    *   `'page_types':['desktop']` along with the other targeting options you have selected.
        
    
*   include a `CANVAS_APP_*` objective
    

#### Lookalike Expansion

Beginning with v13.0, for newly created ad sets that optimize for value, conversions, or app events, lookalike expansion will be turned on by default and cannot be disabled. When getting an ad set that optimizes for value, conversions, or app events, we will return a new lookalike property in the `targeting_optimization_types` map that indicates lookalike expansion is enabled and complements the existing `detailed_targeting` property for the detailed targeting expansion.

#### Targeting DSA Regulated Locations (EU)

For ad sets targeting the EU and/or associated territories, the `dsa_payor` and `dsa_beneficiary` fields are required. The information provided in these 2 fields will be shown to end users to indicate who is paying for the ad and who is the beneficiary of the ad.

**Request**  
Include the following fields in an API call to the `/{adset_id}` endpoint.

```
{
  "dsa\_payor": "<PAYOR\_NAME>",
  "dsa\_beneficiary": "<BENEFICIARY\_NAME>"
  ...
}
```

**Fields**

Name

Description

`dsa_payor`

string (max 512 char)

The payor of all ads in this ad set.

`dsa_beneficiary`

string (max 512 char)

The beneficiary of all ads in this ad set.

If these fields are not provided, the API may returns the following errors:  
**Payor missing error**

```
{
  "error": {
    "message": "Invalid parameter",
    "type": "FacebookApiException",
    "code": 100,
    "error\_data": "{\\"blame\_field\_specs\\":\[\[\\"dsa\_payor\\"\]\]}",
    "error\_subcode": 3858079,
    "is\_transient": false,
    "error\_user\_title": "No payor provided in DSA regulated region",
    "error\_user\_msg": "The DSA requires ads to provide payor information in regulated regions. Updating/creating ad needs to provide payor of the ad.",
    "fbtrace\_id": "fbtrace\_id"
  },
  "\_\_fb\_trace\_id\_\_": "fbtrace\_id",
  "\_\_www\_request\_id\_\_": "request\_id"
}
```
**Beneficiary missing error**
```
{
  "error": {
    "message": "Invalid parameter",
    "type": "FacebookApiException",
    "code": 100,
    "error\_data": "{\\"blame\_field\_specs\\":\[\[\\"dsa\_beneficiary\\"\]\]}",
    "error\_subcode": 3858081,
    "is\_transient": false,
    "error\_user\_title": "No payor/beneficiary provided in DSA regulated location",
    "error\_user\_msg": "The DSA requires ads to provide beneficiary information in regulated regions. Updating/creating ad needs to provide beneficiary of the ad.",
    "fbtrace\_id": "fbtrace\_id"
  },
  "\_\_fb\_trace\_id\_\_": "fbtrace\_id",
  "\_\_www\_request\_id\_\_": "request\_id"
}
```

You can make a POST request to `copies` edge from the following paths:

*   [`/{ad_set_id}/copies`](/docs/marketing-api/reference/ad-campaign/copies/)

When posting to this edge, an [AdSet](/docs/marketing-api/reference/ad-campaign/) will be created.

### Parameters

Parameter

Description

`campaign_id`

numeric string or integer

Single ID of a campaign to make parent of the copy. The copy inherits all campaign settings, such as budget from the parent.Ignore if you want to keep the copy under the original campaign parent.

`deep_copy`

boolean

Default value: `false`

Whether to copy all the child ads. Limits: the total number of children ads to copy should not exceed 3 for a synchronous call and 51 for an asynchronous call.

`end_time`

datetime

The end time of the set, e.g. `2015-03-12 23:59:59-07:00` or `2015-03-12 23:59:59 PDT`. UTC UNIX timestamp. When creating a set with a daily budget, specify `end_time=0` to set the set to be ongoing without end date. If not set, the copied adset will inherit the end time from the original set

`rename_options`

JSON or object-like arrays

Rename options

`start_time`

datetime

The start time of the set, e.g. `2015-03-12 23:59:59-07:00` or `2015-03-12 23:59:59 PDT`. UTC UNIX timestamp. If not set, the copied adset will inherit the start time from the original set

`status_option`

enum {ACTIVE, PAUSED, INHERITED\_FROM\_SOURCE}

Default value: `PAUSED`

`ACTIVE`: the copied adset will have active status. `PAUSED`: the copied adset will have paused status. `INHERITED_FROM_SOURCE`: the copied adset will have the status from the original set.

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `copied_adset_id` in the return type.

Struct {

`copied_adset_id`: numeric string,

`ad_object_ids`: List \[

Struct {

`ad_object_type`: enum {unique\_adcreative, ad, ad\_set, campaign, opportunities, privacy\_info\_center, topline, ad\_account, product},

`source_id`: numeric string,

`copied_id`: numeric string,

}

\],

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

You can make a POST request to `adsets` edge from the following paths:

*   [`/act_{ad_account_id}/adsets`](/docs/marketing-api/reference/ad-account/adsets/)

When posting to this edge, an [AdSet](/docs/marketing-api/reference/ad-campaign/) will be created.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets%3Fname%3DMy%2BFirst%2BAdSet%26daily_budget%3D10000%26bid_amount%3D300%26billing_event%3DIMPRESSIONS%26optimization_goal%3DREACH%26campaign_id%3D%253CAD_CAMPAIGN_ID%253E%26promoted_object%3D%257B%2522page_id%2522%253A%2522%253CPAGE_ID%253E%2522%257D%26targeting%3D%257B%2522facebook_positions%2522%253A%255B%2522feed%2522%255D%252C%2522geo_locations%2522%253A%257B%2522countries%2522%253A%255B%2522US%2522%255D%252C%2522regions%2522%253A%255B%257B%2522key%2522%253A%********%2522%257D%255D%252C%2522cities%2522%253A%255B%257B%2522key%2522%253A777934%252C%2522radius%2522%253A10%252C%2522distance_unit%2522%253A%2522mile%2522%257D%255D%257D%252C%2522genders%2522%253A%255B1%255D%252C%2522age_max%2522%253A24%252C%2522age_min%2522%253A20%252C%2522publisher_platforms%2522%253A%255B%2522facebook%2522%252C%2522audience_network%2522%255D%252C%2522device_platforms%2522%253A%255B%2522mobile%2522%255D%252C%2522flexible_spec%2522%253A%255B%257B%2522interests%2522%253A%255B%257B%2522id%2522%253A%2522%253CINTEREST_ID%253E%2522%252C%2522name%2522%253A%2522%253CINTEREST_NAME%253E%2522%257D%255D%257D%255D%257D%26status%3DPAUSED&version=v23.0)

```
`POST /v23.0/act_<AD_ACCOUNT_ID>/adsets HTTP/1.1
Host: graph.facebook.com

name=My+First+AdSet&daily_budget=10000&bid_amount=300&billing_event=IMPRESSIONS&optimization_goal=REACH&campaign_id=%3CAD_CAMPAIGN_ID%3E&promoted_object=%7B%22page_id%22%3A%22%3CPAGE_ID%3E%22%7D&targeting=%7B%22facebook_positions%22%3A%5B%22feed%22%5D%2C%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%2C%22regions%22%3A%5B%7B%22key%22%3A%224081%22%7D%5D%2C%22cities%22%3A%5B%7B%22key%22%3A777934%2C%22radius%22%3A10%2C%22distance_unit%22%3A%22mile%22%7D%5D%7D%2C%22genders%22%3A%5B1%5D%2C%22age_max%22%3A24%2C%22age_min%22%3A20%2C%22publisher_platforms%22%3A%5B%22facebook%22%2C%22audience_network%22%5D%2C%22device_platforms%22%3A%5B%22mobile%22%5D%2C%22flexible_spec%22%3A%5B%7B%22interests%22%3A%5B%7B%22id%22%3A%22%3CINTEREST_ID%3E%22%2C%22name%22%3A%22%3CINTEREST_NAME%3E%22%7D%5D%7D%5D%7D&status=PAUSED`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`adlabels`

list<Object>

Specifies list of labels to be associated with this object. This field is optional

`adset_schedule`

list<Object>

Ad set schedule, representing a delivery schedule for a single day

`attribution_spec`

list<JSON object>

Conversion attribution spec used for attributing conversions for optimization. Supported window lengths differ by optimization goal and campaign objective.

`bid_amount`

integer

Bid cap or target cost for this ad set. The bid cap used in a _lowest cost bid strategy_ is defined as the maximum bid you want to pay for a result based on your `optimization_goal`. The target cost used in a _target cost bid strategy_ lets Facebook bid to meet your target on average and keep costs stable as you spend. If an ad level `bid_amount` is specified, updating this value will overwrite the previous ad level bid. Unless you are using [Reach and Frequency](/docs/marketing-api/reachandfrequency), `bid_amount` is required if `bid_strategy` is set to `LOWEST_COST_WITH_BID_CAP` or `COST_CAP`.  
The bid amount's unit is cents for currencies like USD, EUR, and the basic unit for currencies like JPY, KRW. The bid amount for ads with `IMPRESSION` or `REACH` as `billing_event` is per 1,000 occurrences, and has to be at least 2 US cents or more. For ads with other `billing_event`s, the bid amount is for each occurrence, and has a minimum value 1 US cents. The minimum bid amounts of other currencies are of similar value to the US Dollar values provided.

`bid_strategy`[](#)

enum{LOWEST\_COST\_WITHOUT\_CAP, LOWEST\_COST\_WITH\_BID\_CAP, COST\_CAP, LOWEST\_COST\_WITH\_MIN\_ROAS}

Choose bid strategy for this ad set to suit your specific business goals. Each strategy has tradeoffs and may be available for certain `optimization_goal`s:  
`LOWEST_COST_WITHOUT_CAP`: Designed to get the most results for your budget based on your ad set `optimization_goal` without limiting your bid amount. This is the best strategy if you care most about cost efficiency. However with this strategy it may be harder to get stable average costs as you spend. This strategy is also known as _automatic bidding_. Learn more in [Ads Help Center, About bid strategies: Lowest cost](https://www.facebook.com/business/help/721453268045071).  
`LOWEST_COST_WITH_BID_CAP`: Designed to get the most results for your budget based on your ad set `optimization_goal` while limiting actual bid to your specified amount. With a bid cap you have more control over your cost per actual optimization event. However if you set a limit which is too low you may get less ads delivery. If you select this, you must provide a bid cap with the `bid_amount` field. Note: during creation this bid strategy is set if you provide `bid_amount` only. This strategy is also known as _manual maximum-cost bidding_. Learn more in [Ads Help Center, About bid strategies: Lowest cost](https://www.facebook.com/business/help/721453268045071).  

Notes:

*   If you enable campaign budget optimization, you should set `bid_strategy` at the parent campaign level.
    
*   `TARGET_COST` bidding strategy has been deprecated with [Marketing API v9](/docs/graph-api/changelog/version9.0).
    

`billing_event`

enum{APP\_INSTALLS, CLICKS, IMPRESSIONS, LINK\_CLICKS, NONE, OFFER\_CLAIMS, PAGE\_LIKES, POST\_ENGAGEMENT, THRUPLAY, PURCHASE, LISTING\_INTERACTION}

The billing event that this ad set is using:  
APP\_INSTALLS: Pay when people install your app.  
CLICKS: Deprecated.  
IMPRESSIONS: Pay when the ads are shown to people.  
LINK\_CLICKS: Pay when people click on the link of the ad.  
OFFER\_CLAIMS: Pay when people claim the offer.  
PAGE\_LIKES: Pay when people like your page.  
POST\_ENGAGEMENT: Pay when people engage with your post.  
VIDEO\_VIEWS: Pay when people watch your video ads for at least 10 seconds.  
THRUPLAY: Pay for ads that are played to completion, or played for at least 15 seconds.

`budget_schedule_specs`

list<JSON or object-like arrays>

Initial high demand periods to be created with the ad set.  
Provide list of `time_start`, `time_end`,`budget_value`, and `budget_value_type`.  
For example,  
\-F 'budget\_schedule\_specs=\[{  
"time\_start":1699081200,  
"time\_end":1699167600,  
"budget\_value":100,  
"budget\_value\_type":"ABSOLUTE"  
}\]'  
See [High Demand Period](https://developers.facebook.com/docs/graph-api/reference/high-demand-period/) for more details on each field.

`budget_source`

enum{NONE, RMN}

budget\_source

`budget_split_set_id`

numeric string or integer

budget\_split\_set\_id

`campaign_attribution`

enum{}

campaign\_attribution

`campaign_id`

numeric string or integer

The ad campaign you wish to add this ad set to.

`campaign_spec`

Campaign spec

Provide `name`, `objective` and `buying_type` for a campaign you want to create. Otherwise you need to provide `campaign_id` for an existing ad campaign. For example:  
\-F 'campaign\_spec={  
  "name": "Inline created campaign",  
  "objective": "CONVERSIONS",  
  "buying\_type": "AUCTION"  
}'  
  
Please refer to the [Outcome-Driven Ads Experiences mapping table](/docs/marketing-api/reference/ad-campaign-group#odax-mapping) to find new objectives and their corresponding destination types, optimization goals and promoted objects.

`contextual_bundling_spec`

Object

settings of Contextual Bundle to support ads serving in Facebook contextual surfaces

`creative_sequence`

list<numeric string or integer>

Order of the adgroup sequence to be shown to users

`daily_budget`

int64

The daily budget defined in your [account currency](/docs/marketing-api/adset/budget-limits), allowed only for ad sets with a duration (difference between `end_time` and `start_time`) longer than 24 hours.  
Either `daily_budget` or `lifetime_budget` must be greater than 0.

`daily_imps`

int64

Daily impressions. Available only for campaigns with `buying_type=FIXED_CPM`

`daily_min_spend_target`

int64

Daily minimum spend target of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. This target is not a guarantee but our best effort.

`daily_spend_cap`

int64

Daily spend cap of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. Set the value to *************** to remove the spend cap.

`destination_type`

enum{WEBSITE, APP, MESSENGER, APPLINKS\_AUTOMATIC, WHATSAPP, INSTAGRAM\_DIRECT, FACEBOOK, MESSAGING\_MESSENGER\_WHATSAPP, MESSAGING\_INSTAGRAM\_DIRECT\_MESSENGER, MESSAGING\_INSTAGRAM\_DIRECT\_MESSENGER\_WHATSAPP, MESSAGING\_INSTAGRAM\_DIRECT\_WHATSAPP, SHOP\_AUTOMATIC, ON\_AD, ON\_POST, ON\_EVENT, ON\_VIDEO, ON\_PAGE, INSTAGRAM\_PROFILE, FACEBOOK\_PAGE, INSTAGRAM\_PROFILE\_AND\_FACEBOOK\_PAGE, INSTAGRAM\_LIVE, FACEBOOK\_LIVE, IMAGINE}

Destination of ads in this Ad Set. Options include: Website, App, Messenger, `INSTAGRAM_DIRECT`, `INSTAGRAM_PROFILE`.

`dsa_beneficiary`

string

dsa\_beneficiary

`dsa_payor`

string

dsa\_payor

`end_time`

datetime

End time, required when `lifetime_budget` is specified. e.g. `2015-03-12 23:59:59-07:00` or `2015-03-12 23:59:59 PDT`. When creating a set with a daily budget, specify `end_time=0` to set the set to be ongoing and have no end date. UTC UNIX timestamp

`execution_options`

list<enum{validate\_only, include\_recommendations}>

Default value: `Set`

An execution setting  
`validate_only`: when this option is specified, the API call will not perform the mutation but will run through the validation rules against values of each field.  
`include_recommendations`: this option cannot be used by itself. When this option is used, recommendations for ad object's configuration will be included. A separate section [recommendations](/docs/marketing-api/reference/ad-recommendation) will be included in the response, but only if recommendations for this specification exist.  
If the call passes validation or review, response will be `{"success": true}`. If the call does not pass, an error will be returned with more details. These options can be used to improve any UI to display errors to the user much sooner, e.g. as soon as a new value is typed into any field corresponding to this ad object, rather than at the upload/save stage, or after review.

`existing_customer_budget_percentage`

int64

existing\_customer\_budget\_percentage

`frequency_control_specs`

list<Object>

An array of frequency control specs for this ad set. As there is only one event type currently supported, this array has no more than one element. Writes to this field are only available in ad sets where `REACH` is the objective.

`is_dynamic_creative`[](#)

boolean

Indicates the ad set must only be used for dynamic creatives. Dynamic creative ads can be created in this ad set. Defaults to `false`

`is_sac_cfca_terms_certified`

boolean

is\_sac\_cfca\_terms\_certified

`lifetime_budget`

int64

Lifetime budget, defined in your [account currency](/docs/marketing-api/adset/budget-limits). If specified, you must also specify an `end_time`.  
Either `daily_budget` or `lifetime_budget` must be greater than 0.

`lifetime_imps`

int64

Lifetime impressions. Available only for campaigns with `buying_type=FIXED_CPM`

`lifetime_min_spend_target`

int64

Lifetime minimum spend target of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. This target is not a guarantee but our best effort.

`lifetime_spend_cap`

int64

Lifetime spend cap of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. Set the value to *************** to remove the spend cap.

`max_budget_spend_percentage`

int64

max\_budget\_spend\_percentage

`min_budget_spend_percentage`

int64

min\_budget\_spend\_percentage

`multi_optimization_goal_weight`

enum{UNDEFINED, BALANCED, PREFER\_INSTALL, PREFER\_EVENT}

multi\_optimization\_goal\_weight

`name`

string

Ad set name, max length of 400 characters.

RequiredSupports Emoji

`optimization_goal`

enum{NONE, APP\_INSTALLS, AD\_RECALL\_LIFT, ENGAGED\_USERS, EVENT\_RESPONSES, IMPRESSIONS, LEAD\_GENERATION, QUALITY\_LEAD, LINK\_CLICKS, OFFSITE\_CONVERSIONS, PAGE\_LIKES, POST\_ENGAGEMENT, QUALITY\_CALL, REACH, LANDING\_PAGE\_VIEWS, VISIT\_INSTAGRAM\_PROFILE, VALUE, THRUPLAY, DERIVED\_EVENTS, APP\_INSTALLS\_AND\_OFFSITE\_CONVERSIONS, CONVERSATIONS, IN\_APP\_VALUE, MESSAGING\_PURCHASE\_CONVERSION, SUBSCRIBERS, REMINDERS\_SET, MEANINGFUL\_CALL\_ATTEMPT, PROFILE\_VISIT, PROFILE\_AND\_PAGE\_ENGAGEMENT, ADVERTISER\_SILOED\_VALUE, AUTOMATIC\_OBJECTIVE, MESSAGING\_APPOINTMENT\_CONVERSION}

What the ad set is optimizing for.  
`APP_INSTALLS`: Will optimize for people more likely to install your app.  
`ENGAGED_USERS`: Will optimize for people more likely to take a particular action in your app.  
`EVENT_RESPONSES`: Will optimize for people more likely to attend your event.  
`IMPRESSIONS`: Will show the ads as many times as possible.  
`LEAD_GENERATION`: Will optimize for people more likely to fill out a lead generation form.  
`LINK_CLICKS`: Will optimize for people more likely to click in the link of the ad.  
`OFFER_CLAIMS`: Will optimize for people more likely to claim the offer.  
`OFFSITE_CONVERSIONS`: Will optimize for people more likely to make a conversion in the site  
`PAGE_ENGAGEMENT`: Will optimize for people more likely to engage with your page.  
`PAGE_LIKES`: Will optimize for people more likely to like your page.  
`POST_ENGAGEMENT`: Will optimize for people more likely to engage with your post.  
`REACH`: Optimize to reach the most unique users of each day or interval specified in `frequency_control_specs`.  
`SOCIAL_IMPRESSIONS`: Increase the number of impressions with social context. For example, with the names of one or more of the user's friends attached to the ad who have already liked the page or installed the app.  
`VALUE`: Will optimize for maximum total purchase value within the specified attribution window.  
`THRUPLAY`: Will optimize delivery of your ads to people are more likely to play your ad to completion, or play it for at least 15 seconds.  
`AD_RECALL_LIFT`: Optimize for people more likely to remember seeing your ads.  
`VISIT_INSTAGRAM_PROFILE`: Optimize for visits to the advertiser's instagram profile.

`optimization_sub_event`

enum{NONE, VIDEO\_SOUND\_ON, TRIP\_CONSIDERATION, TRAVEL\_INTENT, TRAVEL\_INTENT\_NO\_DESTINATION\_INTENT, TRAVEL\_INTENT\_BUCKET\_01, TRAVEL\_INTENT\_BUCKET\_02, TRAVEL\_INTENT\_BUCKET\_03, TRAVEL\_INTENT\_BUCKET\_04, TRAVEL\_INTENT\_BUCKET\_05}

Optimization sub event for a specific optimization goal (ex: Sound-On event for Video-View-2s optimization goal)

`pacing_type`

list<string>

Defines the pacing type, standard by default or using [ad scheduling](/docs/marketing-api/adset/pacing)

`promoted_object`

Object

The object this ad set is promoting across all its ads. Required with certain campaign objectives.  
**CONVERSIONS**

*   `pixel_id` (Conversion pixel ID)
*   `pixel_id` (Facebook pixel ID) and `custom_event_type`
*   `pixel_id` (Facebook pixel ID) and `pixel_rule` and `custom_event_type`
*   `event_id` (Facebook event ID) and `custom_event_type`
*   `application_id`, `object_store_url`, and `custom_event_type` for mobile app events
*   `offline_conversion_data_set_id` (Offline dataset ID) and `custom_event_type` for offline conversions

**PAGE\_LIKES**

*   `page_id`

**OFFER\_CLAIMS**

*   `page_id`

**LINK\_CLICKS**

*   `application_id` and `object_store_url` for mobile app or Canvas app engagement link clicks

**APP\_INSTALLS**

*   `application_id` and `object_store_url`

**if the `optimization_goal` is `OFFSITE_CONVERSIONS`**

*   `application_id`, `object_store_url`, and `custom_event_type` (Standard Events)
*   `application_id`, `object_store_url`, `custom_event_type = OTHER` and `custom_event_str` (Custom Events)

**PRODUCT\_CATALOG\_SALES**

*   `product_set_id`
*   `product_set_id` and `custom_event_type`

When `optimization_goal` is `LEAD_GENERATION`, `page_id` needs to be passed as promoted\_object.  
  
Please refer to the [Outcome-Driven Ads Experiences mapping table](/docs/marketing-api/reference/ad-campaign-group#odax-mapping) to find new objectives and their corresponding destination types, optimization goals and promoted objects.

`rf_prediction_id`

numeric string or integer

Reach and frequency prediction ID

`source_adset_id`

numeric string or integer

The source adset id that this ad is copied from (if applicable).

`start_time`

datetime

The start time of the set, e.g. `2015-03-12 23:59:59-07:00` or `2015-03-12 23:59:59 PDT`. UTC UNIX timestamp

`status`

enum{ACTIVE, PAUSED, DELETED, ARCHIVED}

Only `ACTIVE` and `PAUSED` are valid for creation. The other statuses can be used for update. If it is set to `PAUSED`, all its active ads will be paused and have an effective status `ADSET_PAUSED`.

`targeting`

Targeting object

An ad set's targeting structure. "countries" is required. See [targeting](/docs/marketing-api/targeting-specs).

`time_based_ad_rotation_id_blocks`

list<list<int64>>

Specify ad creative that displays at custom date ranges in a campaign as an array. A list of Adgroup IDs. The list of ads to display for each time range in a given schedule. For example display first ad in Adgroup for first date range, second ad for second date range, and so on. You can display more than one ad per date range by providing more than one ad ID per array. For example set `time_based_ad_rotation_id_blocks` to \[\[1\], \[2, 3\], \[1, 4\]\]. On the first date range show ad 1, on the second date range show ad 2 and ad 3 and on the last date range show ad 1 and ad 4. Use with `time_based_ad_rotation_intervals` to specify date ranges.

`time_based_ad_rotation_intervals`

list<int64>

Date range when specific ad creative displays during a campaign. Provide date ranges in an array of UNIX timestamps where each timestamp represents the start time for each date range. For example a 3-day campaign from May 9 12am to May 11 11:59PM PST can have three date ranges, the first date range starts from May 9 12:00AM to May 9 11:59PM, second date range starts from May 10 12:00AM to May 10 11:59PM and last starts from May 11 12:00AM to May 11 11:59PM. The first timestamp should match the campaign start time. The last timestamp should be at least 1 hour before the campaign end time. You must provide at least two date ranges. All date ranges must cover the whole campaign length, so any date range cannot exceed campaign length. Use with `time_based_ad_rotation_id_blocks` to specify ad creative for each date range.

`time_start`

datetime

Time start

`time_stop`

datetime

Time stop

`tune_for_category`

enum{NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES\_ELECTIONS\_POLITICS, ONLINE\_GAMBLING\_AND\_GAMING, FINANCIAL\_PRODUCTS\_SERVICES}

tune\_for\_category

`value_rule_set_id`

numeric string or integer

Value Rule Set ID

`value_rules_applied`

boolean

value\_rules\_applied

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

Struct {

`id`: numeric string,

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

2641

Your ad includes or excludes locations that are currently restricted

368

The action attempted has been deemed abusive or is otherwise disallowed

2695

The ad set creation reached its campaign group(ios14) limit.

900

No such application exists.

300

Edit failure

## Updating

### Examples

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X POST \
  -F 'billing_event="IMPRESSIONS"' \
  -F 'optimization_goal="LINK_CLICKS"' \
  -F 'bid_amount=200' \
  -F 'targeting={
       "geo_locations": {
         "countries": [
           "US"
         ]
       },
       "facebook_positions": [
         "feed"
       ]
     }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=POST&path=%3CAD_SET_ID%3E%2F?&version=v23.0&billing_event=IMPRESSIONS&optimization_goal=LINK_CLICKS&bid_amount=200&targeting=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22facebook_positions%22%3A%5B%22feed%22%5D%7D)

Give Feedback

To update the `end_time` of an ad set, using ISO-8601 date-time format

PHP SDKPython SDKcURL

```
`use FacebookAds\Object\AdSet;

$adset = new AdSet('<AD_SET_ID>');
$adset->end_time = '2013-10-02T00:00:00-0700';
$adset->update();`
```

To update the status of an ad set to paused

PHP SDKPython SDKcURL

```
`use FacebookAds\Object\AdSet;

$adset = new AdSet('<AD_SET_ID>');
$adset->campaign_status = AdSet::STATUS_PAUSED;
$adset->update();`
```

### Remarks

An archived ad set can only update two fields: `name` and `campaign_status`. The `campaign_status` field can only be changed to `DELETED`.

A deleted ad set can only change its `name`.

There are two considerations to take into account when adjusting an ad set's budget value or budget type:

*   When updating a set's lifetime or daily budget to a lower value, the new value must be at least 10% greater than the current amount spent already. For example: if an ad set has a $1000 lifetime budget and has spend $300 so far, the lowest new lifetime budget would be $330.
    
*   Since `v2.4`, ad sets have a minimum required budget. Any update must take that into consideration. Check the details at the [Create Considerations](#create-considerations) section from this page.
    

**Note:** When using the Reservation buying type, some fields may not be available to be updated through the API.

You can't perform this operation on this endpoint.

## Deleting

### Examples

cURLNode.js SDKPHP SDKPython SDKJava SDKRuby SDK

```


[

Copy Code

](#)

`curl -X DELETE \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/`
```

[Open In Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=DELETE&path=%3CAD_SET_ID%3E%2F?&version=v23.0&)

Give Feedback

You can delete an [AdSet](/docs/marketing-api/reference/ad-campaign/) by making a DELETE request to [`/{ad_set_id}`](/docs/marketing-api/reference/ad-campaign/).

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=DELETE&path=%3CAD_SET_ID%3E%2F&version=v23.0)

```
`DELETE /v23.0/<AD_SET_ID>/ HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Return Type

Struct {

`success`: bool,

}

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

## Outcome-Driven Ads Experiences

### Example

**Outcome-Driven Ads Experiences (Engagement Outcome + `ON_PAGE` destination\_type)**

```
curl \-i \-X POST \\
  \-d "name=New ODAX Adset" \\
  \-d "autobid=true" \\
  \-d "optimization\_goal=PAGE\_LIKES" \\
  \-d "destination\_type=ON\_PAGE" \\
  \-d "billing\_event=IMPRESSIONS" \\
  \-d "daily\_budget=500" \\
  \-d "targeting={\\"geo\_locations\\": {\\"countries\\": \[\\"US\\"\]}}" \\
  \-d "promoted\_object={\\"page\_id\\": PAGE\_ID}" \\
  \-d "campaign\_id=CAMPAIGN\_ID" \\
  \-d "status=PAUSED" \\
  \-d "access\_token=ACCESS\_TOKEN" \\
  https://graph.facebook.com/v11.0/
  act\_AD\_ACCOUNT\_ID/adsets

```

**Legacy**

```
curl \-i \-X POST \\
  \-d "name=New ODAX Adset" \\
  \-d "autobid=true" \\
  \-d "optimization\_goal=PAGE\_LIKES" \\
  \-d "billing\_event=IMPRESSIONS" \\
  \-d "daily\_budget=500" \\
  \-d "targeting={\\"geo\_locations\\": {\\"countries\\": \[\\"US\\"\]}}" \\
  \-d "promoted\_object={\\"page\_id\\": PAGE\_ID}" \\
  \-d "campaign\_id=CAMPAIGN\_ID" \\
  \-d "status=PAUSED" \\
  \-d "access\_token=ACCESS\_TOKEN" \\
  https://graph.facebook.com/v11.0/
  act\_AD\_ACCOUNT\_ID/adsets
```

### Restrictions

There will be new restrictions on Outcome-Driven Ads Experiences (ODAX) campaigns as outlined in the table below. Refer to the [Outcome-Driven Ads Experiences mapping table](/docs/marketing-api/reference/ad-campaign-group#odax-mapping) to find the new objectives and their corresponding destination types, optimization goals and promoted objects.

ODAX Objectives

Conversion Location (L2)

Conversion Events (L2)

Optimization Goals (L2)

Legacy Objectives

**Awareness**  
_Reach the largest number of people who are likely to remember your ad._

N/A

N/A

Ad Recall Lift, Reach, Impressions

  

API enum {`AD_RECALL_LIFT`, `REACH`, `IMPRESSIONS`}

Reach, Brand Awareness

**Traffic**  
_Send people to a destination like your website, app or Shop._

Facebook Shops (closed beta)

N/A

Link Clicks

  

API enum {`LINK_CLICKS`}

Traffic

Website

N/A

Landing Page Views, Link Clicks, Impressions, Daily Unique Reach

  

API enum {`LANDING_PAGE_VIEWS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Traffic

App

N/A

Link Clicks, Daily Unique Reach

  

API enum {`LINK_CLICKS`, `REACH`}

Traffic

Messenger

N/A

Link Clicks, Impressions, Daily Unique Reach

  

API enum {`LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Traffic

WhatsApp

N/A

Link Clicks, Impressions, Daily Unique Reach

  

API enum {`LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Traffic

**Engagement**  
_Find people likely to interact with your business online, and take actions like starting a conversation or commenting on posts._

On Video

N/A

ThruPlay, 2 second continuous view

  

API enum {`THRUPLAY`, `TWO_SECOND_CONTINUOUS_VIDEO_VIEWS`}

Video Views

On Post

N/A

Post Engagement, Impressions, Daily Unique Reach

  

API enum {`POST_ENGAGEMENT`, `IMPRESSIONS`, `REACH`}

Post Engagement

On Event

N/A

Event Response, Impressions, Post Engagement, Daily Unique Reach

  

API enum {`EVENT_RESPONSES`, `IMPRESSIONS`, `POST_ENGAGEMENT`, `REACH`}

Event Responses

Messenger

N/A

Conversations, Link Clicks

  

API enum {`CONVERSATIONS`, `LINK_CLICKS`}

Messages

WhatsApp

N/A

Conversations, Link Clicks

  

API enum {`CONVERSATIONS`, `LINK_CLICKS`}

Messages

Instagram

N/A

Conversations, Link Clicks

  

API enum {`CONVERSATIONS`, `LINK_CLICKS`}

Messages

Website

AddToWishlist, Contact, CustomizeProduct, Donate, FindLocation,, Schedule, Search, StartTrial, SubmitApplication, Subscribe, ViewContent

Conversions, Landing Page Views, Link Clicks, Impressions, Daily Unique Reach

  

API enum {`OFFSITE_CONVERSIONS`, `ONSITE_CONVERSIONS`, `LANDING_PAGE_VIEWS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Conversions

App

Achieve Level, Activate App, Add to Wishlist, Complete Tutorial, Contact, Customize Product, Donate, Find Location, In-App Ad Click, In-App Ad Impression, Rate, Schedule, Search, Spent Credits, Start Trial, Submit Application, Subscribe, Unlock Achievement, View Content

App Events, Link Clicks, Daily Unique Reach

  

API enum {`APP_INSTALLS_AND_OFFSITE_CONVERSIONS`, `LINK_CLICKS`, `REACH`}

Conversions

On Page

N/A

Page Likes

  

API enum {`PAGE_LIKES`}

Engagement

**Leads**  
_Find people interested in your business who are likely to share their contact information._

Website

Lead, CompleteRegistration, Contact, FindLocation, Schedule, StartTrial, SubmitApplication, Subscribe

Conversions, Landing Page Views, Link Clicks, Impressions, Daily Unique Reach

  

API enum {`OFFSITE_CONVERSIONS`, `ONSITE_CONVERSIONS`, `LANDING_PAGE_VIEWS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Conversions

Instant Forms

N/A

Leads

  

API enum {`LEAD_GENERATION`, `QUALITY_LEAD`}

Lead Generation

Messenger

N/A

Leads

  

API enum {`LEAD_GENERATION`, `QUALITY_LEAD`}

Messages

Calls

N/A

Calls

  

API enum {`QUALITY_CALL`}

Lead Generation

App

Complete Registration, Complete Tutorial, Contact, Find Location, Schedule, Start Trial, Submit Application, Subscribe

App Events, Link Clicks, Daily Unique Reach

  

API enum {`APP_INSTALLS_AND_OFFSITE_CONVERSIONS`, `LINK_CLICKS`, `REACH`}

Conversions

**App Promotion**  
_Find people likely to install your app._

N/A

All app events, including all custom events

Non-AAA: Link Clicks, App Installs, App Events, Value

  

API enum {`LINK_CLICKS`, `APP_INSTALLS`, `APP_INSTALLS_AND_OFFSITE_CONVERSIONS`, `VALUE`}

  

AAA: App Installs, App Installs w/ App Events, App Events, Value

  

API enum {`APP_INSTALLS`, `APP_INSTALLS_AND_OFFSITE_CONVERSIONS`, `VALUE`}

App Installs

**Sales**  
_Find people likely to make purchases or take other important actions online or in store._

Website & Facebook Shops (closed beta)

Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent

(source of truth: same as today's Conversions objective + web and shop)

  

API enum {`OFFSITE_CONVERSIONS`, `VALUE`, `LINK_CLICKS`, `LANDING_PAGE_VIEWS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Conversions

Website

Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent

Conversions, Value, Landing Page Views, Link Clicks, Impressions, Daily Unique Reach

  

API enum {`OFFSITE_CONVERSIONS`, `VALUE`, `LANDING_PAGE_VIEWS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Conversions

App

Purchase, Initiate Checkout, Add Payment Info, Add to Cart, Complete Registration, Donate, In-App Ad Click, In-App Ad Impression, Spent Credits, Start Trial, Subscribe, View Content

App Events, Link Clicks, Daily Unique Reach

  

API enum {`OFFSITE_CONVERSIONS`, `LINK_CLICKS`, `REACH`}

Conversions

Website & App

Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent

Conversions

  

API enum {`OFFSITE_CONVERSIONS`}

Conversions

Messenger

Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent

Conversations, Conversions, Link Clicks, Impressions, Reach

  

API enum {`CONVERSATIONS`, `OFFSITE_CONVERSIONS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Conversions

WhatsApp

Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent

Conversions, Link Clicks, Impressions, Reach

  

API enum {`OFFSITE_CONVERSIONS`, `LINK_CLICKS`, `IMPRESSIONS`, `REACH`}

Conversions