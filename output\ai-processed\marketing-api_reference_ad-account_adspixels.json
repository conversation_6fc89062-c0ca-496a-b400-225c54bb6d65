{"title": "Facebook Marketing API - Ad Account Ads Pixels Edge", "summary": "Documentation for the Facebook Marketing API's Ad Account Ads Pixels edge, which allows reading and creating ads pixels associated with an ad account. This endpoint enables management of Facebook pixels for tracking website conversions and building custom audiences.", "content": "# Ad Account Ads Pixels\n\nThe Ad Account Ads Pixels edge allows you to manage Facebook pixels associated with an ad account. This API endpoint supports reading existing pixels and creating new ones for tracking website events and building custom audiences.\n\n## Reading\n\nRetrieve ads pixels associated with an ad account.\n\n### Endpoint\n```\nGET /v23.0/<PIXEL_ID>/?fields=code\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Fields\nThe response returns a JSON formatted result with:\n- `data`: A list of AdsPixel nodes\n- `paging`: Pagination information (see Graph API guide for details)\n- `summary`: Aggregated information about the edge\n  - `total_count` (int32): Total number of objects on this edge\n\n### Code Examples\n\n**cURL:**\n```bash\ncurl -X GET -G \\\n  -d 'fields=\"code\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<PIXEL_ID>/\n```\n\n**PHP SDK:**\n```php\ntry {\n  $response = $fb->get(\n    '/<PIXEL_ID>/?fields=code',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n}\n```\n\n**JavaScript SDK:**\n```javascript\nFB.api(\n    \"/<PIXEL_ID>/\",\n    {\n        \"fields\": \"code\"\n    },\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Error Codes\n- 200: Permissions error\n- 80004: Too many calls to this ad-account (rate limiting)\n- 190: Invalid OAuth 2.0 Access Token\n- 100: Invalid parameter\n\n## Creating\n\nCreate a new ads pixel for an ad account.\n\n### Endpoint\n```\nPOST /v23.0/act_<AD_ACCOUNT_ID>/adspixels\n```\n\n### Parameters\n- `name` (string, required): Name of the pixel\n\n### Return Type\nReturns a struct with:\n- `id`: numeric string (pixel ID)\n\nThis endpoint supports read-after-write functionality.\n\n### Code Examples\n\n**cURL:**\n```bash\ncurl -X POST \\\n  -F 'name=\"My WCA Pixel\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adspixels\n```\n\n**PHP SDK:**\n```php\ntry {\n  $response = $fb->post(\n    '/act_<AD_ACCOUNT_ID>/adspixels',\n    array (\n      'name' => 'My WCA Pixel',\n    ),\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n}\n```\n\n### Error Codes\n- 6202: More than one pixel exists for this account\n- 6200: A pixel already exists for this account\n- 100: Invalid parameter\n- 200: Permissions error\n- 190: Invalid OAuth 2.0 Access Token\n\n## Updating\nUpdate operations are not supported on this endpoint.\n\n## Deleting\nDelete operations are not supported on this endpoint.", "keyPoints": ["Supports reading existing ads pixels and creating new ones for ad accounts", "Reading endpoint returns pixel data with pagination and summary information", "Creating requires only a pixel name parameter and returns the new pixel ID", "Update and delete operations are not supported on this endpoint", "Rate limiting applies - too many calls will result in error 80004"], "apiEndpoints": ["GET /v23.0/<PIXEL_ID>/", "POST /v23.0/act_<AD_ACCOUNT_ID>/adspixels"], "parameters": ["name (string) - Name of the pixel when creating", "fields - Specify fields to return in response", "summary - Specify summary fields like total_count", "access_token - OAuth 2.0 access token for authentication"], "examples": ["Reading pixel with cURL: GET request with fields parameter", "Creating pixel with PHP SDK: POST request with name parameter", "JavaScript SDK implementation for reading pixels", "Android and iOS SDK examples for mobile integration"], "tags": ["Facebook Marketing API", "Ads Pixels", "Ad Account", "Graph API", "Pixel Management", "Website Tracking", "Custom Audiences"], "relatedTopics": ["AdsPixel reference documentation", "Graph API pagination guide", "OAuth 2.0 Access Tokens", "Rate limiting in Ads Management API", "Read-after-write functionality", "Website Custom Audiences (WCA)"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adspixels/", "processedAt": "2025-06-25T16:22:09.146Z", "processor": "openrouter-claude-sonnet-4"}