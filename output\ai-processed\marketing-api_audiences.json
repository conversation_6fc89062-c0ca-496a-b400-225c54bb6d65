{"title": "Facebook Marketing API - Audiences", "summary": "Comprehensive guide to audience targeting in Facebook Marketing API, covering specific and broad targeting approaches. Includes custom audiences, lookalike audiences, dynamic audiences, and targeting options for effective ad delivery.", "content": "# Audiences\n\nAudience targeting helps you show your ads to the people you care about. There are two general approaches — specific or broad — you can take when creating a target audience. The approach you choose depends on what you're trying to accomplish and your available resources.\n\nYou can be specific and create audiences based on customer data, conversion data such as activity in your app or website, etc. Or, you can provide broader information, such as demographics or location, and we deliver ads to people who meet those attributes.\n\n## Common Uses\n\n- **Lookalike Audiences** — Target people most like your established customers\n- **Custom Audiences** — Build your target custom audience with data from:\n  - Mobile app and website behavior\n  - CRM data\n  - Engagement signals\n  - Offline conversions\n- **Dynamic Audiences** — Build an audience from mobile app and website signals\n- **Targeting Options** — Basic targeting includes:\n  - Demographics and events\n  - Location\n  - Interests and behaviors\n  - Advanced targeting specifications\n\n## Documentation Contents\n\n### Overview\nThe basics of audiences and targeting\n\n### Guides\nBuild audiences with data and learn more about our broad targeting options\n\n### Reference\nExplore our basic and advanced targeting options, targeting search, and the Custom Audience Terms of Service contracts\n\n### Special Ad Category\nTargeting options available for advertisers offering housing, employment, or credit opportunities", "keyPoints": ["Two main targeting approaches: specific (based on customer/conversion data) or broad (demographics/location)", "Custom Audiences can be built from mobile app, website, CRM, engagement, and offline conversion data", "Lookalike Audiences help target people similar to existing customers", "Dynamic Audiences leverage mobile app and website behavioral signals", "Special targeting considerations exist for housing, employment, and credit advertisers"], "apiEndpoints": [], "parameters": [], "examples": [], "tags": ["audiences", "targeting", "custom-audiences", "lookalike-audiences", "dynamic-audiences", "marketing-api", "facebook-ads"], "relatedTopics": ["Lookalike Audience Targeting", "Mobile App Audiences", "Website Audiences", "CRM Audiences", "Engagement Audiences", "Offline Conversions", "Dynamic Product Ads", "Product Audiences", "Targeting Options", "Advanced Targeting", "Special Ad Category"], "difficulty": "beginner", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-api/audiences", "processedAt": "2025-06-25T15:07:05.464Z", "processor": "openrouter-claude-sonnet-4"}