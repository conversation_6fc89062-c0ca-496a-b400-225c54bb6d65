const axios = require('axios');

async function testCompleteCampaignCreation() {
  try {
    console.log('🚀 Testing complete campaign creation...');
    
    const response = await axios.post('http://localhost:3000/api/v1/facebook/campaigns/complete', {
      adAccountId: 'act_263173616383414',
      campaignName: 'Test Complete Campaign - Pressure Max',
      objective: 'OUTCOME_TRAFFIC',
      adSetName: 'Test Ad Set - Pressure Washing Services',
      dailyBudget: 2000, // $20.00 per day
      targeting: {
        geo_locations: {
          countries: ['US']
        },
        age_min: 25,
        age_max: 55
      },
      adName: 'Test Ad - Professional Pressure Washing',
      creative: {
        message: 'Transform your home with professional pressure washing! Get a sparkling clean exterior that increases your property value.',
        headline: 'Professional Pressure Washing Services',
        description: 'Expert pressure washing for homes and businesses. Free estimates, insured, and satisfaction guaranteed.',
        link_url: 'https://pressuremax.com',
        cta_type: 'LEARN_MORE'
      }
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Complete campaign creation response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('❌ Complete campaign creation error:', error.response?.data || error.message);
  }
}

async function testEnhancedDataRetrieval() {
  try {
    console.log('\n🔍 Testing enhanced data retrieval...');
    
    // Test campaigns with enhanced fields
    console.log('\n📊 Fetching enhanced campaigns data...');
    const campaignsResponse = await axios.get('http://localhost:3000/api/v1/facebook/campaigns/act_1313976499465490');
    console.log(`✅ Retrieved ${campaignsResponse.data.length} campaigns with enhanced data`);
    
    if (campaignsResponse.data.length > 0) {
      const firstCampaign = campaignsResponse.data[0];
      console.log('📝 Sample campaign data:', {
        id: firstCampaign.id,
        name: firstCampaign.name,
        objective: firstCampaign.objective,
        status: firstCampaign.status,
        budget_remaining: firstCampaign.budget_remaining,
        bid_strategy: firstCampaign.bid_strategy,
        buying_type: firstCampaign.buying_type,
        special_ad_categories: firstCampaign.special_ad_categories
      });
    }

    // Test ad sets with enhanced fields
    console.log('\n📊 Fetching enhanced ad sets data...');
    const adSetsResponse = await axios.get('http://localhost:3000/api/v1/facebook/adsets/act_1313976499465490');
    console.log(`✅ Retrieved ${adSetsResponse.data.length} ad sets with enhanced data`);
    
    if (adSetsResponse.data.length > 0) {
      const firstAdSet = adSetsResponse.data[0];
      console.log('📝 Sample ad set data:', {
        id: firstAdSet.id,
        name: firstAdSet.name,
        campaign_id: firstAdSet.campaign_id,
        daily_budget: firstAdSet.daily_budget,
        targeting: firstAdSet.targeting ? 'Available' : 'Not available',
        optimization_goal: firstAdSet.optimization_goal,
        billing_event: firstAdSet.billing_event,
        bid_strategy: firstAdSet.bid_strategy
      });
    }

    // Test ads with enhanced fields
    console.log('\n📊 Fetching enhanced ads data...');
    const adsResponse = await axios.get('http://localhost:3000/api/v1/facebook/ads/act_1313976499465490');
    console.log(`✅ Retrieved ${adsResponse.data.length} ads with enhanced data`);
    
    if (adsResponse.data.length > 0) {
      const firstAd = adsResponse.data[0];
      console.log('📝 Sample ad data:', {
        id: firstAd.id,
        name: firstAd.name,
        adset_id: firstAd.adset_id,
        campaign_id: firstAd.campaign_id,
        creative: firstAd.creative ? 'Available' : 'Not available',
        preview_shareable_link: firstAd.preview_shareable_link
      });
    }

  } catch (error) {
    console.error('❌ Enhanced data retrieval error:', error.response?.data || error.message);
  }
}

async function runAllTests() {
  await testCompleteCampaignCreation();
  await testEnhancedDataRetrieval();
}

runAllTests();
