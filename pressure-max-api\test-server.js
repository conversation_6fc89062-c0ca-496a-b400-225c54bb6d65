const express = require('express');
const cors = require('cors');

const app = express();
const port = 3000;

// Basic middleware
app.use(cors({
  origin: 'http://localhost:3001',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

// Basic auth endpoints
app.post('/api/v1/auth/register', (req, res) => {
  console.log('Register request:', req.body);
  res.status(201).json({
    message: 'User registered successfully',
    user: {
      id: 'user_123',
      email: req.body.email,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      role: 'user'
    },
    tokens: {
      accessToken: 'demo_access_token_123',
      refreshToken: 'demo_refresh_token_123'
    }
  });
});

app.post('/api/v1/auth/login', (req, res) => {
  console.log('Login request:', req.body);
  res.json({
    message: 'Login successful',
    user: {
      id: 'user_123',
      email: req.body.email,
      firstName: 'Demo',
      lastName: 'User',
      role: 'user'
    },
    tokens: {
      accessToken: 'demo_access_token_123',
      refreshToken: 'demo_refresh_token_123'
    }
  });
});

app.post('/api/v1/auth/logout', (req, res) => {
  res.json({ message: 'Logout successful' });
});

// User profile endpoint
app.get('/api/v1/users/profile', (req, res) => {
  res.json({
    id: 'user_123',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: 'user',
    isActive: true,
    lastLoginAt: new Date().toISOString()
  });
});

// Facebook endpoints
app.get('/api/v1/facebook/oauth-url', (req, res) => {
  const { redirectUri } = req.query;
  const state = 'demo_state_' + Date.now();

  // Real Facebook OAuth URL with actual app ID
  const permissions = [
    'ads_management',
    'ads_read',
    'business_management',
    'pages_read_engagement',
    'pages_manage_ads',
    'leads_retrieval',
    'email',
    'public_profile'
  ].join(',');

  const oauthUrl = `https://www.facebook.com/v18.0/dialog/oauth?` +
    `client_id=394349039883481&` +
    `redirect_uri=${encodeURIComponent(redirectUri || 'http://localhost:3001/facebook-callback')}&` +
    `scope=${encodeURIComponent(permissions)}&` +
    `response_type=code&` +
    `state=${state}`;

  console.log('Generated Facebook OAuth URL:', oauthUrl);

  res.json({
    oauthUrl,
    state
  });
});

// Facebook OAuth callback handler
app.post('/api/v1/facebook/oauth-callback', (req, res) => {
  const { code, state } = req.body;
  console.log('Facebook OAuth callback received:', { code: code?.substring(0, 20) + '...', state });

  // In a real implementation, you would:
  // 1. Exchange code for access token
  // 2. Get user profile from Facebook
  // 3. Create or update user in database
  // 4. Generate JWT tokens

  // For demo, return success with mock user data
  res.json({
    message: 'Facebook account connected successfully',
    user: {
      id: 'user_fb_' + Date.now(),
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      facebookConnected: true
    },
    tokens: {
      accessToken: 'demo_fb_access_token_' + Date.now(),
      refreshToken: 'demo_fb_refresh_token_' + Date.now()
    },
    profile: {
      id: '*****************',
      name: 'Facebook User',
      email: '<EMAIL>'
    },
    permissions: [
      'ads_management',
      'ads_read',
      'business_management',
      'pages_read_engagement',
      'pages_manage_ads',
      'leads_retrieval',
      'email',
      'public_profile'
    ]
  });
});

app.get('/api/v1/facebook/ad-accounts', async (req, res) => {
  try {
    // Use the real Facebook access token
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Make real API call to Facebook
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v18.0/me/adaccounts', {
      params: {
        fields: 'id,name,account_status,currency,timezone_name,business,account_id',
        access_token: accessToken
      }
    });

    console.log('Facebook Ad Accounts Response:', response.data);
    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook API Error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'act_demo',
        account_id: 'demo_account',
        name: 'Demo Ad Account (API Error)',
        account_status: 'ACTIVE',
        currency: 'USD',
        timezone_name: 'America/New_York',
        error: 'Real API call failed, showing demo data'
      }
    ]);
  }
});

app.get('/api/v1/facebook/pages', async (req, res) => {
  try {
    // Use the real Facebook access token
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Make real API call to Facebook
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v18.0/me/accounts', {
      params: {
        fields: 'id,name,category,access_token,picture',
        access_token: accessToken
      }
    });

    console.log('Facebook Pages Response:', response.data);
    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook Pages API Error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'page_demo',
        name: 'Demo Business Page (API Error)',
        category: 'Business',
        error: 'Real API call failed, showing demo data'
      }
    ]);
  }
});

app.post('/api/v1/facebook/campaigns', (req, res) => {
  console.log('Create campaign request:', req.body);
  res.status(201).json({
    message: 'Campaign created successfully',
    campaign: {
      id: 'camp_' + Date.now(),
      name: req.body.name,
      objective: req.body.objective,
      status: 'PAUSED',
      created_time: new Date().toISOString()
    }
  });
});

app.get('/api/v1/facebook/campaigns/:adAccountId', (req, res) => {
  res.json([
    {
      id: 'camp_123',
      name: 'Demo Campaign 1',
      objective: 'REACH',
      status: 'ACTIVE',
      created_time: '2024-01-15T10:00:00Z',
      daily_budget: 5000
    },
    {
      id: 'camp_456',
      name: 'Demo Lead Gen Campaign',
      objective: 'LEAD_GENERATION',
      status: 'PAUSED',
      created_time: '2024-01-10T15:30:00Z',
      daily_budget: 3000
    }
  ]);
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The requested endpoint ${req.originalUrl} does not exist`
  });
});

app.listen(port, () => {
  console.log(`🚀 Pressure Max API server running on port ${port}`);
  console.log(`📚 Health check available at http://localhost:${port}/health`);
  console.log(`🔗 CORS enabled for http://localhost:3001`);
});

module.exports = app;
