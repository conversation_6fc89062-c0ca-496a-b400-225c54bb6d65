<h1 id="overview">Overview</h1>

<p>The Marketing API is a Meta business tool designed to empower developers and marketers with the ability to automate advertising efforts across Meta technologies. It offers a comprehensive suite of functionalities that streamline the processes of ad creation, management, and performance analysis.</p>

<p>One of the primary features of the Marketing API is its ability to facilitate the automated creation of ads. You can programmatically generate ad campaigns, ad sets, and individual ads, allowing for rapid deployment and iteration based on real-time performance data. This automation also enables businesses to reach larger audiences with greater efficiency.</p>

<p>In addition to ad creation, you can:</p>

<ul>
<li>Update, pause, or delete ads seamlessly</li>
<li>Ensure that campaigns remain aligned with business objectives</li>
<li>Access detailed insights and analytics to track  ad performance and make data-driven decisions to improve outcomes</li>
</ul>


<h2 id="how-it-works">How it Works</h2>
<div style="text-align:center;"></div><h3 id="ad-campaigns">Ad campaigns</h3>

<p>A campaign is the highest level organizational structure within an ad account and should represent a single objective, for example, to drive Page post engagement. Setting the objective of the campaign enforces validation on any ads added to that campaign to ensure they also have the correct objective.</p>

<h3 id="ad-sets">Ad sets</h3>

<p>Ad sets are groups of ads and are used to configure the budget and period the ads should run for. All ads contained within an ad set should have the same targeting, budget, billing, optimization goal, and duration.</p>

<p>Create an ad set for each target audience with your bid; ads in the set target the same audience with the same bid. This helps control the amount you spend on each audience, determine when the audience will see your ads, and provides metrics for each audience.</p>

<h3 id="ad-creatives">Ad creatives</h3>

<p>Ad creatives contain just the visual elements of the ad and you can't change them once they're created. Each ad account has a creative library to store creatives for reuse in ads.</p>

<h3 id="ads">Ads</h3>

<p>An ad object contains all of the information necessary to display an ad on Facebook, Instagram, Messenger, and WhatsApp, including the ad creative. Create multiple ads in each ad set to optimize ad delivery based on different images, links, video, text, or placements.</p>

<h3 id="ad-components">Ad Components</h3>

<p>This table shows how the various ad components align to the different levels of ad creation.</p>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th></th><th>Ad Campaign</th><th>Ad Set</th><th>Ad</th></tr></thead><tbody class="_5m37" id="u_0_1_CB"><tr class="row_0"><td><p><strong>Objective</strong></p>
</td><td><div style="color:green;text-align:center;font-size:22pt;">✓</div></td><td></td><td></td></tr><tr class="row_1 _5m29"><td><p><strong>Schedule</strong></p>
</td><td></td><td><div style="color:green;text-align:center;font-size:22pt;">✓</div></td><td></td></tr><tr class="row_2"><td><p><strong>Budget</strong></p>
</td><td></td><td><div style="color:green;text-align:center;font-size:22pt;">✓</div></td><td></td></tr><tr class="row_3 _5m29"><td><p><strong>Bidding</strong></p>
</td><td></td><td><div style="color:green;text-align:center;font-size:22pt;">✓</div></td><td></td></tr><tr class="row_4"><td><p><strong>Audience</strong></p>
</td><td></td><td><div style="color:green;text-align:center;font-size:22pt;">✓</div></td><td></td></tr><tr class="row_5 _5m29"><td><p><strong>Ad Creative</strong></p>
</td><td></td><td></td><td><div style="color:green;text-align:center;font-size:22pt;">✓</div></td></tr></tbody></table></div>

