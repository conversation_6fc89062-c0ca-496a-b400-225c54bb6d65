# Facebook Marketing API - Ad Account User Reference

## Summary
Reference documentation for the Ad Account User endpoint in Facebook's Marketing API v23.0. This endpoint provides read-only access to App Scoped User data associated with ad accounts, including user ID, name, and assigned tasks.

## Key Points
- Read-only endpoint for accessing App Scoped User data in ad accounts
- Returns user ID, name, and assigned tasks by default
- No parameters required for basic requests
- Only supports GET operations - no create, update, or delete functionality
- Part of Facebook Marketing API v23.0

## API Endpoints
- `GET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser}`

## Parameters
- ad-account-id (required in URL path)
- fields (optional query parameter for field selection)

## Content
# Ad Account User

## Overview

The Ad Account User endpoint provides access to data of App Scoped Users associated with ad accounts in the Facebook Marketing API.

**API Version:** v23.0

## Reading

Retrieve data of App Scoped User associated with an ad account.

### HTTP Request

```http
GET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser} HTTP/1.1
Host: graph.facebook.com
```

### Parameters

This endpoint doesn't have any parameters.

### Response Fields

| Field | Type | Description | Default |
|-------|------|-------------|----------|
| `id` | numeric string | ID of the App Scoped User | |
| `name` | string | User public full name | ✓ |
| `tasks` | list<string> | Tasks of App Scoped User | ✓ |

*Fields marked with ✓ are returned by default*

## Limitations

- **Creating**: Not supported on this endpoint
- **Updating**: Not supported on this endpoint  
- **Deleting**: Not supported on this endpoint

This is a read-only endpoint that only supports GET operations.

## Examples
GET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser}

---
**Tags:** Facebook Marketing API, Ad Account, User Management, App Scoped User, Read-only API, Graph API
**Difficulty:** beginner
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account-user
**Processed:** 2025-06-25T15:50:37.929Z