{"title": "Facebook Marketing API Overview", "summary": "The Marketing API is a comprehensive set of Graph API endpoints designed to help developers create, manage, and optimize advertising campaigns across Meta technologies including Facebook, Instagram, Messenger, and WhatsApp. It provides tools for creating ads, managing campaigns, and analyzing performance.", "content": "## Marketing API Overview\n\n### Key Features\n- Create and manage ad campaigns\n- Set up ad sets and ad creatives\n- Analyze campaign insights\n- Optimize audience targeting\n- Integrate advertising workflows\n\n### Getting Started\n1. Understand Meta's ad campaign structure\n2. Obtain necessary API credentials\n3. Choose appropriate API endpoints\n4. Implement ad creation and management\n\n### Current API Version\n- Latest version: `v23.0`\n\n### Core Capabilities\n- Ad campaign creation\n- Campaign management\n- Audience targeting\n- Performance tracking\n- Brand safety controls", "keyPoints": ["Comprehensive API for advertising across Meta platforms", "Supports full ad campaign lifecycle management", "Provides detailed insights and optimization tools", "Integrates with multiple Meta technologies", "Supports advanced audience targeting"], "apiEndpoints": ["/marketing-api/campaigns", "/marketing-api/ad-sets", "/marketing-api/ad-creatives", "/marketing-api/audiences", "/marketing-api/insights"], "parameters": ["campaign_id", "ad_account_id", "targeting_specs", "budget", "optimization_goal"], "examples": ["Basic ad creation workflow", "Campaign management operations", "Audience targeting configuration"], "tags": ["advertising", "marketing", "meta", "facebook", "api"], "relatedTopics": ["Conversions API", "Catalog API", "Business Management API", "Commerce Platform"], "difficulty": "intermediate", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-apis", "processedAt": "2025-06-25T14:55:02.364Z", "processor": "openrouter-claude-3.5-haiku"}