# Facebook Marketing API - Ad Account Reference

## Summary
Complete reference documentation for the Facebook Marketing API Ad Account object, including fields, edges, CRUD operations, and usage examples. Ad Accounts represent entities that create and manage ads on Facebook with specific limits and permissions.

## Key Points
- Ad Accounts represent entities that create and manage Facebook ads with specific access levels and permissions
- Ad volume limits are enforced per page, with different limits for regular (6,000) vs bulk (50,000) ad accounts
- The end_advertiser field cannot be changed once set to a value other than NONE or UNFOUND
- DSA payor and beneficiary fields must be set together or unset together for compliance
- Account status and effective_status determine whether ads are considered 'running or in review'

## API Endpoints
- `GET /act_{ad_account_id}`
- `POST /act_{ad_account_id}`
- `GET /act_{ad_account_id}/ads_volume`
- `GET /act_{ad_account_id}/users`
- `POST /{business_id}/adaccount`
- `GET /act_{ad_account_id}/activities`
- `GET /act_{ad_account_id}/adcreatives`

## Parameters
- account_id
- account_status
- name
- currency
- timezone_id
- spend_cap
- amount_spent
- end_advertiser
- media_agency
- partner
- agency_client_declaration
- default_dsa_beneficiary
- default_dsa_payor
- funding_source
- business
- capabilities

## Content
# Facebook Marketing API - Ad Account Reference

## Overview

Represents a business, person or other entity who creates and manages ads on Facebook. Multiple people can manage an account, and each person can have one or more levels of access to an account.

**Important Notice**: In response to Apple's iOS 14.5 requirements, there are breaking changes affecting SDKAdNetwork, Marketing API and Ads Insights API endpoints.

## Ad Volume Management

You can view the volume of ads running or in review for your ad accounts. These ads count against the ads limit per page that will be enforced.

### Querying Ad Volume

```bash
curl -G \
  -d "access_token=<access_token>" \
  "https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume"
```

Response:
```json
{"data":[{"ads_running_or_in_review_count":2}]}
```

### Running or In Review Status

An ad is considered "running or in review" when:
- `effective_status` is `1` (active)
- `configured_status` is `active` and `effective_status` is `9` (pending review) or `17` (pending processing)
- Ad account status is `1` (active), `8` (pending settlement), or `9` (in grace period)
- Ad set schedule indicates current time is within run period

## Account Limits

| Limit | Value |
|-------|-------|
| Maximum ad accounts per person | 25 |
| Maximum people with access per ad account | 25 |
| Maximum ads per regular ad account | 6,000 non-archived non-deleted |
| Maximum ads per bulk ad account | 50,000 non-archived non-deleted |
| Maximum archived ads per ad account | 100,000 |
| Maximum ad sets per regular ad account | 6,000 non-archived non-deleted |
| Maximum ad sets per bulk ad account | 10,000 non-archived non-deleted |
| Maximum ad campaigns per regular ad account | 6,000 non-archived non-deleted |
| Maximum ad campaigns per bulk ad account | 10,000 non-archived non-deleted |

## Reading Ad Accounts

### Basic Read

```bash
curl -G \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>
```

### Finding Users with Access

```bash
curl -G \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/users
```

### Digital Services Act Information

```bash
curl -X GET \
"https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>?fields=default_dsa_payor%2Cdefault_dsa_beneficiary&access_token=<ACCESS_TOKEN>"
```

## Key Fields

### Core Fields
- `id`: The string `act_{ad_account_id}`
- `account_id`: The ID of the Ad Account
- `account_status`: Status of the account (1=ACTIVE, 2=DISABLED, etc.)
- `name`: Name of the account
- `currency`: Currency used for the account
- `timezone_id`: Timezone ID of the ad account
- `business`: The Business Manager if owned by one

### Financial Fields
- `amount_spent`: Current amount spent by the account
- `balance`: Bill amount due for this Ad Account
- `spend_cap`: Maximum amount that can be spent
- `funding_source`: ID of the payment method
- `funding_source_details`: Detailed payment method information

### Configuration Fields
- `agency_client_declaration`: Agency advertising details (requires Admin privileges)
- `capabilities`: List of capabilities an Ad Account can have
- `brand_safety_content_filter_levels`: Content filter levels for ads
- `default_dsa_beneficiary`: Default DSA beneficiary value
- `default_dsa_payor`: Default DSA payor value

## Creating Ad Accounts

To create a new ad account, specify required fields:

```bash
curl \
-F "name=MyAdAccount" \
-F "currency=USD" \
-F "timezone_id=1" \
-F "end_advertiser=<END_ADVERTISER_ID>" \
-F "media_agency=<MEDIA_AGENCY_ID>" \
-F "partner=NONE" \
-F "access_token=<ACCESS_TOKEN>" \
"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount"
```

### Required Parameters
- `name`: The name of the ad account
- `currency`: ISO 4217 Currency Code
- `timezone_id`: ID for the timezone
- `end_advertiser`: Entity the ads will target (Page ID, App ID, or NONE/UNFOUND)
- `media_agency`: The agency (Page ID, App ID, or NONE/UNFOUND)
- `partner`: Advertising partner (Page ID, App ID, or NONE/UNFOUND)

**Important**: Once `end_advertiser` is set to a value other than `NONE` or `UNFOUND`, it cannot be changed.

## Updating Ad Accounts

Update an ad account with POST request:

```bash
POST /act_{ad_account_id}
```

### Key Update Parameters
- `name`: Update account name
- `spend_cap`: Set spending limit
- `spend_cap_action`: Reset amount_spent or delete spend_cap
- `default_dsa_beneficiary`: Set DSA beneficiary
- `default_dsa_payor`: Set DSA payor
- `is_notifications_enabled`: Enable/disable notifications

**Note**: DSA payor and beneficiary must be set together or both unset with empty strings.

## Important Edges

- `/activities`: Ad account activities
- `/adcreatives`: Ad creatives
- `/ads_volume`: Ad volume information
- `/users`: Users with access
- `/customaudiences`: Custom audiences
- `/campaigns`: Ad campaigns
- `/adsets`: Ad sets
- `/ads`: Ads
- `/insights`: Performance insights

## Error Codes

Common error codes:
- `100`: Invalid parameter
- `190`: Invalid OAuth 2.0 Access Token
- `200`: Permissions error
- `613`: Rate limit exceeded
- `80004`: Too many calls to ad account
- `368`: Action deemed abusive or disallowed

## Examples
curl -G -d 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>

curl -G -d 'access_token=<access_token>' 'https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume'

curl -F 'name=MyAdAccount' -F 'currency=USD' -F 'timezone_id=1' 'https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount'

---
**Tags:** Facebook Marketing API, Ad Account, Account Management, Ad Volume, Business Manager, DSA Compliance, Account Limits, CRUD Operations
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account
**Processed:** 2025-06-25T15:10:44.429Z