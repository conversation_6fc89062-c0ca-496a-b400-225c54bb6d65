# Facebook Marketing API - Ad Account Ad Place Page Sets

## Summary
This endpoint manages Ad Place Page Sets for Facebook ad accounts, allowing creation of location-based page sets for targeting users based on their location types. The endpoint only supports creation operations, with reading, updating, and deleting operations not available.

## Key Points
- Only creation (POST) operations are supported - reading, updating, and deleting are not available
- Creates AdPlacePageSet objects for location-based targeting
- Requires a parent page ID and name, with optional location types
- Supports read-after-write functionality
- Applies specifically to published Pages

## API Endpoints
- `/act_{ad_account_id}/ad_place_page_sets`

## Parameters
- location_types (list<enum>)
- name (string, required)
- parent_page (numeric string/integer, required)

## Content
# Ad Account Ad Place Page Sets

This endpoint applies to published Pages and manages Ad Place Page Sets for Facebook ad accounts.

## Supported Operations

### Reading
Reading operations are not supported on this endpoint.

### Creating
You can create Ad Place Page Sets by making a POST request to the `ad_place_page_sets` edge.

**Endpoint Path:**
- `/act_{ad_account_id}/ad_place_page_sets`

When posting to this edge, an [AdPlacePageSet](https://developers.facebook.com/docs/marketing-api/reference/ad-place-page-set/) will be created.

#### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `location_types` | list<enum {recent, home}> | Type of user location the page set targets (e.g., 'recent', 'home') | No |
| `name` | string | Name of The Place PageSet | Yes |
| `parent_page` | numeric string or integer | The parent page ID for all the locations pages | Yes |

#### Return Type
This endpoint supports read-after-write and will read the node represented by `id` in the return type.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |

### Updating
Updating operations are not supported on this endpoint.

### Deleting
Deleting operations are not supported on this endpoint.

---
**Tags:** Facebook Marketing API, Ad Account, Place Page Sets, Location Targeting, Graph API  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/ad_place_page_sets/  
**Processed:** 2025-06-25T16:19:33.784Z